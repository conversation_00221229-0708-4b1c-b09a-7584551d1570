-- Script SQL completo y optimizado para Hipocrates2DB en PostgreSQL 16+
-- Incluye prioridades altas: rotación de claves, particionamiento, vistas materializadas RIPS, y estandarización de constraints
-- Diseñado para un sistema SaaS multi-tenant, cumple con RIPS, Ley 1581/2012, DIAN y es internacionalizable
-- Incluye módulo de ambulancias con georeferenciación y optimizaciones para modularidad, escalabilidad y seguridad

-- ############################################################################
-- ### CONFIGURACIÓN INICIAL PARA SAAS MULTITENANCY Y ESCALABILIDAD ###
-- ############################################################################

--Instalacion de extensiones

-- ### Extensiones requeridas para Hipocrates2DB ###
DO $$
DECLARE
    v_extensions TEXT[] := ARRAY[
        'postgis',
        'pgcrypto',
        'plpgsql',
        'vector',
        'timescaledb',
        'pg_stat_statements',
        'uuid-ossp',
        'btree_gist',
        'tablefunc',
        'fuzzystrmatch',
        'pg_trgm',
        'unaccent'
    ];
    v_ext TEXT;
    v_installed_extensions TEXT[];
    v_missing_extensions TEXT[];
BEGIN
    -- Obtener extensiones ya instaladas
    SELECT array_agg(extname) INTO v_installed_extensions FROM pg_extension;

    -- Si v_installed_extensions es NULL (ninguna extensión instalada), inicializar como array vacío
    IF v_installed_extensions IS NULL THEN
        v_installed_extensions := '{}'::TEXT[];
    END IF;

    -- Determinar extensiones faltantes
    v_missing_extensions := ARRAY(
        SELECT unnest(v_extensions)
        EXCEPT
        SELECT unnest(v_installed_extensions)
    );

    -- Instalar extensiones faltantes
    IF array_length(v_missing_extensions, 1) > 0 THEN
        FOREACH v_ext IN ARRAY v_missing_extensions
        LOOP
            RAISE NOTICE 'Instalando extensión faltante: %', v_ext;
            EXECUTE 'CREATE EXTENSION IF NOT EXISTS ' || quote_ident(v_ext);
        END LOOP;
        RAISE NOTICE 'Todas las extensiones requeridas han sido verificadas e instaladas.';
    ELSE
        RAISE NOTICE 'Todas las extensiones requeridas ya están instaladas.';
    END IF;

    -- Verificar si la extensión pgcrypto está instalada
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
        RAISE NOTICE 'La extensión pgcrypto no está instalada. Intentando instalar...';
        CREATE EXTENSION IF NOT EXISTS pgcrypto;
        RAISE NOTICE 'Extensión pgcrypto instalada correctamente.';
    ELSE
        RAISE NOTICE 'La extensión pgcrypto ya está instalada.';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error durante la instalación de extensiones: %', SQLERRM;
        RAISE WARNING 'Asegúrese de que el usuario de base de datos tenga permisos para crear extensiones y que las bibliotecas de las extensiones estén disponibles en el sistema.';
END;
$$;

-- Configuración de conexiones y recursos para alta concurrencia
-- NOTA: Los siguientes comandos ALTER SYSTEM no pueden ejecutarse dentro de una transacción.
-- Por favor, ejecute estos comandos manualmente en la consola de PostgreSQL antes de ejecutar el resto del script.
/*
ALTER SYSTEM SET max_connections = '500';
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET work_mem = '16MB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET max_parallel_workers_per_gather = '4';
ALTER SYSTEM SET max_parallel_workers = '8';
ALTER SYSTEM SET max_worker_processes = '16';

-- Configuración de connection pooling para multitenancy
ALTER SYSTEM SET idle_in_transaction_session_timeout = '60000';  -- 60 segundos
ALTER SYSTEM SET statement_timeout = '180000';                   -- 3 minutos
ALTER SYSTEM SET tcp_keepalives_idle = '60';
ALTER SYSTEM SET tcp_keepalives_interval = '10';
*/

-- Crear esquemas para organización modular
DO $$
DECLARE
    v_schemas TEXT[] := ARRAY[
        'schema_migrations',  -- Gestión de versiones y migraciones
        'gestion_general',    -- Configuración general del sistema
        'clinico',            -- Datos clínicos y de pacientes
        'inventario',         -- Gestión de inventario
        'financiero',         -- Módulo financiero
        'recursos_humanos',   -- Gestión de personal
        'telemedicina',       -- Funcionalidades de telemedicina
        'control_calidad',    -- Control de calidad
        'erp',                -- Planificación de recursos empresariales
        'ambulancias',        -- Gestión de ambulancias
        'configuracion'       -- Configuraciones del sistema
    ];
    v_schema TEXT;
BEGIN
    FOREACH v_schema IN ARRAY v_schemas LOOP
        BEGIN
            EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', v_schema);
            RAISE NOTICE 'Esquema % creado o ya existente', v_schema;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error al crear el esquema %: %', v_schema, SQLERRM;
        END;
    END LOOP;
END $$;

-- Tabla de versiones y migraciones
DO $$
BEGIN
    BEGIN
        CREATE TABLE IF NOT EXISTS schema_migrations.versions (
            version_id VARCHAR(50) PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by TEXT NOT NULL,
            script_hash TEXT NOT NULL,
            script_content TEXT,
            is_validated BOOLEAN DEFAULT FALSE
        );
        COMMENT ON TABLE schema_migrations.versions IS 'Registro de migraciones aplicadas con hash basado en contenido para validación';
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Error al crear la tabla de versiones: %', SQLERRM;
    END;
END $$;
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'schema_migrations'
        AND table_name = 'versions'
    ) THEN
        CREATE TABLE schema_migrations.versions (
            version_id VARCHAR(50) PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by TEXT NOT NULL,
            script_hash TEXT NOT NULL,
            script_content TEXT,
            is_validated BOOLEAN DEFAULT FALSE
        );
        COMMENT ON TABLE schema_migrations.versions IS 'Registro de migraciones aplicadas con hash basado en contenido para validación';
        RAISE NOTICE 'Tabla schema_migrations.versions creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla schema_migrations.versions ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla schema_migrations.versions: %', SQLERRM;
END $$;

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS schema_migrations.register_migration(VARCHAR, TEXT, TEXT);
DROP FUNCTION IF EXISTS schema_migrations.verify_migrations();

-- Create the register_migration function
CREATE OR REPLACE FUNCTION schema_migrations.register_migration(
    p_version_id VARCHAR(50),
    p_description TEXT,
    p_script_content TEXT
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO schema_migrations.versions (
        version_id,
        description,
        applied_by,
        script_hash,
        script_content,
        is_validated
    )
    VALUES (
        p_version_id,
        p_description,
        current_user,
        md5(p_script_content),
        p_script_content,
        TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- Create the verify_migrations function
CREATE OR REPLACE FUNCTION schema_migrations.verify_migrations()
RETURNS TABLE(version_id VARCHAR(50), is_valid BOOLEAN, error_message TEXT) AS $$
DECLARE
    v_record RECORD;
    v_calculated_hash TEXT;
BEGIN
    FOR v_record IN SELECT * FROM schema_migrations.versions ORDER BY applied_at LOOP
        v_calculated_hash := md5(v_record.script_content);

        IF v_record.script_hash = v_calculated_hash THEN
            version_id := v_record.version_id;
            is_valid := TRUE;
            error_message := NULL;
        ELSE
            version_id := v_record.version_id;
            is_valid := FALSE;
            error_message := 'Hash mismatch: stored=' || v_record.script_hash || ', calculated=' || v_calculated_hash;
        END IF;

        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON FUNCTION schema_migrations.register_migration IS 'Registra una migración con hash basado en el contenido del script';
COMMENT ON FUNCTION schema_migrations.verify_migrations IS 'Verifica la integridad de las migraciones comparando los hashes almacenados';

-- Register initial version if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM schema_migrations.versions WHERE version_id = 'v1.0.0') THEN
        PERFORM schema_migrations.register_migration(
            'v1.0.0',
            'Esquema inicial Hipocrates2DB',
            'Esquema inicial para sistema SAAS multitenancy Hipocrates2DB'
        );
    END IF;

    RAISE NOTICE 'Migration functions created successfully';
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error creating migration functions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Documentación detallada de extensiones
BEGIN;
COMMENT ON EXTENSION pgcrypto IS 'Proporciona funciones criptográficas para encriptar datos sensibles de pacientes y usuarios según Ley 1581/2012';
COMMENT ON EXTENSION postgis IS 'Habilita capacidades de geolocalización para el módulo de ambulancias, ubicación de hospitales y optimización de rutas';
COMMENT ON EXTENSION vector IS 'Soporta embeddings vectoriales para funcionalidades de IA como búsqueda semántica en historias clínicas y análisis predictivo';
COMMENT ON EXTENSION timescaledb IS 'Optimiza el almacenamiento y consulta de series temporales como datos de monitoreo de pacientes y sensores médicos';
COMMENT ON EXTENSION pg_stat_statements IS 'Permite monitorear y analizar el rendimiento de consultas SQL para optimizar el sistema por tenant';
COMMIT;

-- Crear schemas para modularidad
BEGIN;
CREATE SCHEMA IF NOT EXISTS clinico;
CREATE SCHEMA IF NOT EXISTS financiero;
CREATE SCHEMA IF NOT EXISTS ambulancias;
CREATE SCHEMA IF NOT EXISTS gestion_general;
CREATE SCHEMA IF NOT EXISTS recursos_humanos;
CREATE SCHEMA IF NOT EXISTS inventario;
CREATE SCHEMA IF NOT EXISTS telemedicina;
CREATE SCHEMA IF NOT EXISTS paciente_portal;
CREATE SCHEMA IF NOT EXISTS control_calidad;
CREATE SCHEMA IF NOT EXISTS erp;
CREATE SCHEMA IF NOT EXISTS tenant_config;  -- Nuevo esquema para configuración de tenants
COMMIT;

-- Tabla de control de tenants mejorada
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenants'
    ) THEN
        CREATE TABLE tenant_config.tenants (
            id BIGSERIAL PRIMARY KEY, -- Identificador único del tenant
            nombre TEXT NOT NULL, -- Nombre del tenant
            schema_prefix TEXT NOT NULL UNIQUE, -- Prefijo para el esquema del tenant
            estado TEXT NOT NULL CHECK (estado IN ('activo', 'inactivo', 'suspendido', 'prueba')), -- Estado del tenant
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Fecha de creación
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Fecha de última actualización
            max_connections INTEGER DEFAULT 50, -- Máximo número de conexiones permitidas
            plan_tipo TEXT NOT NULL CHECK (plan_tipo IN ('basico', 'estandar', 'premium', 'enterprise')), -- Tipo de plan
            fecha_expiracion TIMESTAMP, -- Fecha de expiración del plan
            configuracion JSONB DEFAULT '{}'::jsonb -- Configuraciones adicionales en formato JSON
        );
        COMMENT ON TABLE tenant_config.tenants IS 'Registro centralizado de tenants con control de recursos y configuración';
        RAISE NOTICE 'Tabla tenant_config.tenants creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenants ya existe, omitiendo creación';

        -- Asegurarse de que las columnas necesarias existan
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenants'
            AND column_name = 'configuracion'
        ) THEN
            ALTER TABLE tenant_config.tenants
            ADD COLUMN IF NOT EXISTS configuracion JSONB DEFAULT '{}'::jsonb;

            RAISE NOTICE 'Columna configuracion añadida a tenant_config.tenants';
        END IF;
    END IF;

    -- Crear índices si no existen
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenants'
        AND indexname = 'idx_tenants_estado'
    ) THEN
        CREATE INDEX idx_tenants_estado ON tenant_config.tenants(estado);
        RAISE NOTICE 'Índice idx_tenants_estado creado correctamente';
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenants'
        AND indexname = 'idx_tenants_plan_tipo'
    ) THEN
        CREATE INDEX idx_tenants_plan_tipo ON tenant_config.tenants(plan_tipo);
        RAISE NOTICE 'Índice idx_tenants_plan_tipo creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear o modificar tenant_config.tenants: %', SQLERRM;
END $$;
COMMIT;

-- Crear función para actualizar automáticamente updated_at
DO $$
BEGIN
    -- Intentar eliminar la función si existe para asegurar una recreación limpia.
    -- Usamos un bloque anidado BEGIN/EXCEPTION para que el DO principal no falle si el DROP falla.
    BEGIN
        DROP FUNCTION IF EXISTS tenant_config.update_updated_at_column();
        RAISE NOTICE 'Función tenant_config.update_updated_at_column() eliminada si existía (o no se encontró).';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'No se pudo eliminar la función tenant_config.update_updated_at_column() (puede que no exista o haya dependencias): %', SQLERRM;
    END;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error en el bloque DO para eliminar la función tenant_config.update_updated_at_column(): %', SQLERRM;
END $$;
COMMIT;

CREATE OR REPLACE FUNCTION tenant_config.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger para actualizar automáticamente updated_at
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'update_tenants_updated_at'
        AND tgrelid = 'tenant_config.tenants'::regclass
    ) THEN
        CREATE TRIGGER update_tenants_updated_at
        BEFORE UPDATE ON tenant_config.tenants
        FOR EACH ROW EXECUTE FUNCTION tenant_config.update_updated_at_column();

        RAISE NOTICE 'Trigger update_tenants_updated_at creado correctamente';
    ELSE
        RAISE NOTICE 'El trigger update_tenants_updated_at ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear el trigger update_tenants_updated_at: %', SQLERRM;
END $$;
COMMIT;

-- Función para establecer el contexto del tenant actual
BEGIN;
CREATE OR REPLACE FUNCTION tenant_config.set_tenant_context(tenant_id BIGINT)
RETURNS VOID AS $$
BEGIN
    -- Verificar si el tenant existe y está activo
    PERFORM 1 FROM tenant_config.tenants
    WHERE id = tenant_id AND estado = 'activo';

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Tenant % no existe o no está activo', tenant_id;
    END IF;

    -- Establecer el tenant actual en el contexto de la sesión
    PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, FALSE);

    -- Obtener y establecer el hospital_id asociado al tenant
    PERFORM set_config('app.current_hospital_id',
                      (SELECT id::TEXT FROM gestion_general.hospitales WHERE tenant_id = tenant_id LIMIT 1),
                      FALSE);

    -- Registrar acceso solo si la tabla existe
    BEGIN
        INSERT INTO tenant_config.tenant_access_log (tenant_id, session_id)
        VALUES (tenant_id, pg_backend_pid());
    EXCEPTION
        WHEN undefined_table THEN
            -- La tabla no existe todavía, ignoramos el error
            NULL;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
COMMENT ON FUNCTION tenant_config.set_tenant_context IS 'Establece el contexto de ejecución para el tenant actual';

-- Tabla para registro de acceso a tenants
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenant_access_log'
    ) THEN
        CREATE TABLE tenant_config.tenant_access_log (
            id BIGSERIAL PRIMARY KEY,
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id),
            session_id BIGINT NOT NULL,
            access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address INET,
            user_agent TEXT,
            endpoint TEXT,
            status_code INTEGER
        );

        -- Añadir restricción de clave foránea si la tabla de tenants existe
        IF EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenants'
        ) THEN
            ALTER TABLE tenant_config.tenant_access_log
            ADD CONSTRAINT fk_tenant_access_log_tenant
            FOREIGN KEY (tenant_id)
            REFERENCES tenant_config.tenants(id)
            ON DELETE CASCADE;
        END IF;

        COMMENT ON TABLE tenant_config.tenant_access_log IS 'Registro de accesos a cada tenant para auditoría y seguridad';
        RAISE NOTICE 'Tabla tenant_config.tenant_access_log creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenant_access_log ya existe, omitiendo creación';

        -- Asegurarse de que las columnas necesarias existan
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenant_access_log'
            AND column_name = 'ip_address'
        ) THEN
            ALTER TABLE tenant_config.tenant_access_log
            ADD COLUMN IF NOT EXISTS ip_address INET;
        END IF;

        -- Agregar más comprobaciones de columnas según sea necesario
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla tenant_config.tenant_access_log: %', SQLERRM;
END $$;

-- Crear índices para mejorar el rendimiento de las consultas
DO $$
BEGIN
    -- Índice para búsquedas por tenant_id
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_tenant_id'
    ) THEN
        CREATE INDEX idx_tenant_access_log_tenant_id ON tenant_config.tenant_access_log(tenant_id);
        RAISE NOTICE 'Índice idx_tenant_access_log_tenant_id creado correctamente';
    END IF;

    -- Índice para búsquedas por rango de fechas
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_access_time'
    ) THEN
        CREATE INDEX idx_tenant_access_log_access_time ON tenant_config.tenant_access_log(access_time);
        RAISE NOTICE 'Índice idx_tenant_access_log_access_time creado correctamente';
    END IF;

    -- Índice compuesto para consultas comunes
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_tenant_time'
    ) THEN
        CREATE INDEX idx_tenant_access_log_tenant_time ON tenant_config.tenant_access_log(tenant_id, access_time);
        RAISE NOTICE 'Índice idx_tenant_access_log_tenant_time creado correctamente';
    END IF;

    -- Índice para búsquedas por estado
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_status_code'
    ) THEN
        CREATE INDEX idx_tenant_access_log_status_code ON tenant_config.tenant_access_log(status_code);
        RAISE NOTICE 'Índice idx_tenant_access_log_status_code creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear índices para tenant_config.tenant_access_log: %', SQLERRM;
END $$;

-- Tabla de cuotas de recursos por tenant
DO $$
BEGIN
    -- Crear la tabla si no existe
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenant_quotas'
    ) THEN
        CREATE TABLE tenant_config.tenant_quotas (
            tenant_id BIGINT PRIMARY KEY,
            max_storage_gb INTEGER DEFAULT 10,
            max_users INTEGER DEFAULT 10,
            max_patients INTEGER DEFAULT 1000,
            max_connections INTEGER DEFAULT 50,
            features_enabled JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_tenant_quotas_tenant
                FOREIGN KEY (tenant_id)
                REFERENCES tenant_config.tenants(id)
                ON DELETE CASCADE
        );

        COMMENT ON TABLE tenant_config.tenant_quotas IS 'Cuotas de recursos y características habilitadas por tenant';
        RAISE NOTICE 'Tabla tenant_config.tenant_quotas creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenant_quotas ya existe, verificando estructura...';

        -- Añadir columnas faltantes si es necesario
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenant_quotas'
            AND column_name = 'features_enabled'
        ) THEN
            ALTER TABLE tenant_config.tenant_quotas
            ADD COLUMN features_enabled JSONB DEFAULT '{}'::jsonb;
            RAISE NOTICE 'Columna features_enabled añadida a tenant_config.tenant_quotas';
        END IF;
    END IF;

    -- Crear índices para mejorar el rendimiento
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_quotas'
        AND indexname = 'idx_tenant_quotas_tenant_id'
    ) THEN
        CREATE INDEX idx_tenant_quotas_tenant_id ON tenant_config.tenant_quotas(tenant_id);
        RAISE NOTICE 'Índice idx_tenant_quotas_tenant_id creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear o modificar tenant_config.tenant_quotas: %', SQLERRM;
END $$;

-- Crear o reemplazar función para actualizar automáticamente updated_at
DO $$
BEGIN
    -- Intentar eliminar la función si existe para asegurar una recreación limpia.
    -- Usamos un bloque anidado BEGIN/EXCEPTION para que el DO principal no falle si el DROP falla.
    BEGIN
        DROP FUNCTION IF EXISTS tenant_config.update_tenant_quotas_updated_at();
        RAISE NOTICE 'Función tenant_config.update_tenant_quotas_updated_at() eliminada si existía (o no se encontró).';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'No se pudo eliminar la función tenant_config.update_tenant_quotas_updated_at() (puede que no exista o haya dependencias): %', SQLERRM;
    END;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error en el bloque DO para eliminar la función tenant_config.update_tenant_quotas_updated_at(): %', SQLERRM;
END $$;

CREATE OR REPLACE FUNCTION tenant_config.update_tenant_quotas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger solo si no existe
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'update_tenant_quotas_updated_at'
        AND tgrelid = 'tenant_config.tenant_quotas'::regclass
    ) THEN
        CREATE TRIGGER update_tenant_quotas_updated_at
        BEFORE UPDATE ON tenant_config.tenant_quotas
        FOR EACH ROW EXECUTE FUNCTION tenant_config.update_tenant_quotas_updated_at();

        RAISE NOTICE 'Trigger update_tenant_quotas_updated_at creado correctamente';
    ELSE
        RAISE NOTICE 'El trigger update_tenant_quotas_updated_at ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear el trigger update_tenant_quotas_updated_at: %', SQLERRM;
END $$;

COMMIT;

-- Función para obtener o crear cuotas por defecto para un tenant
CREATE OR REPLACE FUNCTION tenant_config.obtener_o_crear_cuotas(
    p_tenant_id BIGINT
)
RETURNS tenant_config.tenant_quotas AS $$
DECLARE
    v_cuota tenant_config.tenant_quotas%ROWTYPE;
BEGIN
    -- Verificar si el tenant existe
    IF NOT EXISTS (SELECT 1 FROM tenant_config.tenants WHERE id = p_tenant_id) THEN
        RAISE EXCEPTION 'El tenant con ID % no existe', p_tenant_id;
    END IF;

    -- Bloqueo para evitar condiciones de carrera
    LOCK TABLE tenant_config.tenant_quotas IN SHARE ROW EXCLUSIVE MODE;

    -- Intentar obtener las cuotas existentes
    SELECT * INTO v_cuota
    FROM tenant_config.tenant_quotas
    WHERE tenant_id = p_tenant_id;

    -- Si no existen, crear unas nuevas con valores por defecto
    IF NOT FOUND THEN
        INSERT INTO tenant_config.tenant_quotas (tenant_id)
        VALUES (p_tenant_id)
        RETURNING * INTO v_cuota;

        RAISE NOTICE 'Cuotas por defecto creadas para el tenant %', p_tenant_id;
    END IF;

    RETURN v_cuota;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al obtener o crear cuotas para el tenant %: %', p_tenant_id, SQLERRM;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para verificar si una característica está habilitada para un tenant
CREATE OR REPLACE FUNCTION tenant_config.caracteristica_habilitada(
    p_tenant_id BIGINT,
    p_feature_name TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    v_enabled BOOLEAN;
BEGIN
    -- Verificar si la característica está habilitada explícitamente
    SELECT COALESCE((features_enabled->>p_feature_name)::BOOLEAN, FALSE) INTO v_enabled
    FROM tenant_config.tenant_quotas
    WHERE tenant_id = p_tenant_id;

    RETURN v_enabled;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al verificar la característica % para el tenant %: %',
        p_feature_name, p_tenant_id, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMIT;

-- ### TABLAS PARA LÓGICA DE NEGOCIO SAAS ###
-- Implementación según saas_salud_negocio.md

-- Esquema para lógica de negocio SaaS
CREATE SCHEMA IF NOT EXISTS saas_negocio;

-- Tabla de planes de suscripción
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'planes'
    ) THEN
        CREATE TABLE saas_negocio.planes (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            nombre TEXT NOT NULL,
            precio DECIMAL(10,2) NOT NULL,
            duracion_dias INTEGER NOT NULL DEFAULT 30,
            descripcion TEXT,
            modulos_incluidos JSONB DEFAULT '[]'::jsonb,
            max_usuarios INTEGER DEFAULT 10,
            max_pacientes INTEGER DEFAULT 1000,
            max_storage_gb INTEGER DEFAULT 10,
            caracteristicas JSONB DEFAULT '{}'::jsonb,
            activo BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE saas_negocio.planes IS 'Planes de suscripción disponibles para los tenants';
        RAISE NOTICE 'Tabla saas_negocio.planes creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.planes ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.planes: %', SQLERRM;
END $$;

-- Tabla de módulos por plan
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'plan_modulos'
    ) THEN
        CREATE TABLE saas_negocio.plan_modulos (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            plan_id UUID NOT NULL REFERENCES saas_negocio.planes(id) ON DELETE CASCADE,
            codigo_modulo TEXT NOT NULL,
            nombre_modulo TEXT NOT NULL,
            activo BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(plan_id, codigo_modulo)
        );
        COMMENT ON TABLE saas_negocio.plan_modulos IS 'Módulos incluidos en cada plan de suscripción';
        RAISE NOTICE 'Tabla saas_negocio.plan_modulos creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.plan_modulos ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.plan_modulos: %', SQLERRM;
END $$;

-- Actualizar tabla de tenants para incluir campos de suscripción
DO $$
BEGIN
    -- Agregar columnas de suscripción si no existen
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenants'
        AND column_name = 'fecha_inicio_prueba'
    ) THEN
        ALTER TABLE tenant_config.tenants
        ADD COLUMN fecha_inicio_prueba DATE DEFAULT CURRENT_DATE,
        ADD COLUMN fecha_fin_prueba DATE,
        ADD COLUMN dias_periodo_gracia INTEGER DEFAULT 0,
        ADD COLUMN fecha_inicio_suscripcion DATE,
        ADD COLUMN fecha_fin_suscripcion DATE,
        ADD COLUMN estado_suscripcion TEXT DEFAULT 'trial_active'
            CHECK (estado_suscripcion IN ('trial_active', 'trial_expired', 'active', 'suspended', 'paused', 'cancelled')),
        ADD COLUMN plan_id UUID REFERENCES saas_negocio.planes(id),
        ADD COLUMN notificaciones_prueba_enviadas JSONB DEFAULT '{}'::jsonb,
        ADD COLUMN configuracion_negocio JSONB DEFAULT '{}'::jsonb;

        RAISE NOTICE 'Columnas de suscripción añadidas a tenant_config.tenants';
    END IF;

    -- Actualizar fecha_fin_prueba para tenants existentes
    UPDATE tenant_config.tenants
    SET fecha_fin_prueba = fecha_inicio_prueba + INTERVAL '30 days'
    WHERE fecha_fin_prueba IS NULL AND fecha_inicio_prueba IS NOT NULL;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al actualizar tenant_config.tenants: %', SQLERRM;
END $$;

-- Tabla de pagos
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'pagos'
    ) THEN
        CREATE TABLE saas_negocio.pagos (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id) ON DELETE CASCADE,
            plan_id UUID NOT NULL REFERENCES saas_negocio.planes(id),
            monto DECIMAL(10,2) NOT NULL,
            metodo_pago TEXT NOT NULL CHECK (metodo_pago IN ('tarjeta_credito', 'transferencia_bancaria', 'debito_automatico', 'efectivo')),
            estado_pago TEXT NOT NULL DEFAULT 'pending'
                CHECK (estado_pago IN ('pending', 'confirmed', 'failed', 'manual_review', 'cancelled')),
            referencia_transaccion TEXT,
            factura_id TEXT,
            fecha_pago TIMESTAMP,
            fecha_vencimiento DATE,
            metadata_pago JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE saas_negocio.pagos IS 'Registro de pagos realizados por los tenants';
        RAISE NOTICE 'Tabla saas_negocio.pagos creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.pagos ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.pagos: %', SQLERRM;
END $$;

-- Tabla de log de notificaciones
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'notificaciones_log'
    ) THEN
        CREATE TABLE saas_negocio.notificaciones_log (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id) ON DELETE CASCADE,
            tipo_notificacion TEXT NOT NULL CHECK (tipo_notificacion IN ('trial_warning', 'trial_expired', 'payment_reminder', 'payment_failed', 'subscription_renewed')),
            canal TEXT NOT NULL CHECK (canal IN ('email', 'sms', 'push', 'in_app')),
            destinatario TEXT NOT NULL,
            asunto TEXT,
            contenido TEXT,
            estado_envio TEXT NOT NULL DEFAULT 'pending'
                CHECK (estado_envio IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
            fecha_programada TIMESTAMP,
            fecha_enviada TIMESTAMP,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE saas_negocio.notificaciones_log IS 'Log de notificaciones enviadas a los tenants';
        RAISE NOTICE 'Tabla saas_negocio.notificaciones_log creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.notificaciones_log ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.notificaciones_log: %', SQLERRM;
END $$;

-- Tabla de eventos de tenant
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'tenant_eventos'
    ) THEN
        CREATE TABLE saas_negocio.tenant_eventos (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id) ON DELETE CASCADE,
            tipo_evento TEXT NOT NULL CHECK (tipo_evento IN ('trial_started', 'trial_extended', 'trial_expired', 'subscription_activated', 'subscription_renewed', 'subscription_cancelled', 'payment_received', 'payment_failed', 'plan_upgraded', 'plan_downgraded')),
            descripcion TEXT,
            metadata JSONB DEFAULT '{}'::jsonb,
            usuario_id BIGINT,
            ip_address INET,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE saas_negocio.tenant_eventos IS 'Registro de eventos comerciales de los tenants';
        RAISE NOTICE 'Tabla saas_negocio.tenant_eventos creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.tenant_eventos ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.tenant_eventos: %', SQLERRM;
END $$;

-- Tabla de facturas
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'saas_negocio'
        AND table_name = 'facturas'
    ) THEN
        CREATE TABLE saas_negocio.facturas (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id) ON DELETE CASCADE,
            numero_factura TEXT UNIQUE NOT NULL,
            plan_id UUID NOT NULL REFERENCES saas_negocio.planes(id),
            monto_subtotal DECIMAL(10,2) NOT NULL,
            monto_impuestos DECIMAL(10,2) DEFAULT 0,
            monto_total DECIMAL(10,2) NOT NULL,
            estado_factura TEXT NOT NULL DEFAULT 'generated'
                CHECK (estado_factura IN ('generated', 'sent', 'paid', 'overdue', 'cancelled')),
            fecha_generacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            fecha_vencimiento DATE NOT NULL,
            fecha_pago TIMESTAMP,
            cumple_dian BOOLEAN DEFAULT false,
            cufe TEXT,
            xml_dian TEXT,
            pdf_url TEXT,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE saas_negocio.facturas IS 'Facturas generadas para los tenants';
        RAISE NOTICE 'Tabla saas_negocio.facturas creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla saas_negocio.facturas ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla saas_negocio.facturas: %', SQLERRM;
END $$;

COMMIT;

-- ### Tipos ENUM Unificados ###
BEGIN;
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sexo_paciente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.sexo_paciente AS ENUM ('M', 'F', 'O');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_cita' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_cita AS ENUM ('Programada', 'Cancelada', 'Realizada', 'No Asistió');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_factura' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_factura AS ENUM ('Generada', 'Validada', 'Pagada', 'Rechazada');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_diagnostico' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.tipo_diagnostico AS ENUM ('Primario', 'Secundario', 'Terciario');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_cama' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_cama AS ENUM ('Libre', 'Ocupada', 'Reservada', 'En Limpieza');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_especialidad' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.tipo_especialidad AS ENUM ('Clínica', 'Quirúrgica', 'Diagnóstica', 'Pediatría', 'Salud Mental', 'Emergente');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_urgencia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_urgencia AS ENUM ('En Observación', 'Alta', 'Hospitalizado', 'Crítico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_recurso' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.tipo_recurso AS ENUM ('Equipo', 'Instrumento', 'Dispositivo', 'Consumible');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_recurso' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.estado_recurso AS ENUM ('Operativo', 'En Mantenimiento', 'Fuera de Servicio');
    END IF;

    -- Cambiar nombre del ENUM para evitar conflicto con la tabla
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_documento_enum' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.tipo_documento_enum AS ENUM ('Licencia', 'Certificación', 'Permiso', 'Autorización');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_sensor' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.tipo_sensor AS ENUM ('Frecuencia Cardíaca', 'Presión Arterial', 'Temperatura', 'Oxígeno', 'Glucosa');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_pago' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_pago AS ENUM ('Pendiente', 'Completado', 'Fallido');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_incidente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'control_calidad')) THEN
        CREATE TYPE control_calidad.tipo_incidente AS ENUM ('Seguridad', 'Clínico', 'Administrativo');
    END IF;

    IF NOT EXISTS

 (SELECT 1 FROM pg_type WHERE typname = 'nivel_riesgo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'control_calidad')) THEN
        CREATE TYPE control_calidad.nivel_riesgo AS ENUM ('Bajo', 'Medio', 'Alto', 'Crítico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'regimen_paciente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.regimen_paciente AS ENUM ('contributivo', 'subsidiado', 'vinculado', 'particular');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_generico' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.estado_generico AS ENUM ('Activo', 'Inactivo', 'En Revisión', 'Disponible', 'Ocupado', 'En Mantenimiento');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'categoria_residuo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'erp')) THEN
        CREATE TYPE erp.categoria_residuo AS ENUM ('infeccioso', 'peligroso', 'común', 'biopeligroso');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_dian_detallado' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_dian_detallado AS ENUM ('pendiente_validacion', 'validada', 'rechazada', 'correccion_requerida', 'anulada');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_quirofano' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_quirofano AS ENUM ('Disponible', 'Ocupado', 'En_Mantenimiento');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_equipo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.tipo_equipo AS ENUM ('Diagnóstico', 'Terapéutico', 'Soporte', 'Quirúrgico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_ambulancia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.estado_ambulancia AS ENUM ('Disponible', 'En_Servicio', 'En_Mantenimiento', 'Fuera_Servicio');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_ambulancia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.tipo_ambulancia AS ENUM ('Básica', 'Medicalizada', 'Transporte_Asistencial');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'prioridad_servicio' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.prioridad_servicio AS ENUM ('Baja', 'Media', 'Alta', 'Crítica');
    END IF;
END $$;
COMMIT;
-- ### Gestión General ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.hospitales (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id),
    nombre TEXT NOT NULL,
    codigo TEXT UNIQUE NOT NULL,
    nit VARCHAR(20) UNIQUE NOT NULL,
    direccion TEXT,
    telefono TEXT,
    email TEXT,
    ubicacion geometry(Point, 4326),
    categoria_ips TEXT CONSTRAINT chk_categoria_ips CHECK (categoria_ips IN ('consultorio', 'clinica', 'hospital1', 'hospital2', 'hospital3', 'hospital4')),
    codigo_habilitacion TEXT UNIQUE NOT NULL,
    estado_registro TEXT CONSTRAINT chk_estado_registro CHECK (estado_registro IN ('activo', 'inactivo')),
    fecha_inscripcion_reps DATE,
    fecha_vencimiento_habilitacion DATE,
    entidad_territorial TEXT,
    idioma_predeterminado VARCHAR(5) DEFAULT 'es',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE gestion_general.hospitales IS 'Almacena los datos de las IPS registradas como tenants en el sistema. Alineado con REPS (Resolución 3100/2019).';
CREATE INDEX IF NOT EXISTS idx_hospitales_tenant_id ON gestion_general.hospitales(tenant_id);
COMMIT;

-- Función para crear automáticamente un hospital cuando se crea un tenant
BEGIN;
CREATE OR REPLACE FUNCTION tenant_config.create_hospital_for_tenant()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO gestion_general.hospitales (
        tenant_id, nombre, codigo, nit, codigo_habilitacion, estado_registro
    ) VALUES (
        NEW.id, NEW.nombre, 'H' || NEW.id, '000000000', 'CH' || NEW.id, 'activo'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
COMMIT;

-- Crear trigger solo si no existe
BEGIN;
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_create_hospital'
        AND tgrelid = 'tenant_config.tenants'::regclass
    ) THEN
        EXECUTE 'CREATE TRIGGER trigger_create_hospital
                AFTER INSERT ON tenant_config.tenants
                FOR EACH ROW
                EXECUTE FUNCTION tenant_config.create_hospital_for_tenant()';
    END IF;
END $$;
COMMIT;

-- ### Tablas de Referencia Geográfica ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.pais (
    id CHAR(3) PRIMARY KEY,               -- Código ISO del país (ej. 'COL')
    nombre VARCHAR(100) NOT NULL          -- Nombre completo del país
);

CREATE TABLE IF NOT EXISTS gestion_general.departamento (
    id CHAR(2) PRIMARY KEY,               -- Código del departamento (ej. '05' para Antioquia)
    nombre VARCHAR(100) NOT NULL,         -- Nombre del departamento
    pais_id CHAR(3) NOT NULL REFERENCES gestion_general.pais(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.municipio (
    id CHAR(5) PRIMARY KEY,               -- Código del municipio (ej. '05001' para Medellín)
    nombre VARCHAR(100) NOT NULL,         -- Nombre del municipio
    departamento_id CHAR(2) NOT NULL REFERENCES gestion_general.departamento(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.zona_territorial (
    id CHAR(2) PRIMARY KEY,               -- Código de la zona (ej. 'U', 'R')
    descripcion VARCHAR(50) NOT NULL      -- Descripción de la zona (ej. 'Urbana', 'Rural')
);
COMMIT;
-- ### Tablas de Referencia Normativa y Catálogos ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.tipo_documento (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de documento (ej. 'CC', 'TI')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Cédula de Ciudadanía')
);

CREATE TABLE IF NOT EXISTS gestion_general.sexo (
    id CHAR(1) PRIMARY KEY,               -- Código del sexo (ej. 'M', 'F', 'O')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Masculino', 'Femenino', 'Otro')
);

CREATE TABLE IF NOT EXISTS gestion_general.eps (
    nit VARCHAR(9) PRIMARY KEY,           -- NIT de la EPS
    nombre VARCHAR(100) NOT NULL          -- Nombre de la EPS
);

CREATE TABLE IF NOT EXISTS gestion_general.cie10 (
    id VARCHAR(10) PRIMARY KEY,           -- Código CIE-10 (ej. 'A00.0')
    descripcion TEXT NOT NULL             -- Descripción del diagnóstico
);

CREATE TABLE IF NOT EXISTS gestion_general.cups (
    id VARCHAR(10) PRIMARY KEY,           -- Código CUPS (ej. '890101')
    descripcion TEXT NOT NULL             -- Descripción del procedimiento
);

CREATE TABLE IF NOT EXISTS gestion_general.tipo_usuario (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de usuario (ej. '01')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Contributivo')
);

CREATE TABLE IF NOT EXISTS gestion_general.tipo_nota (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de nota (ej. '01')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Nota de Evolución')
);

CREATE TABLE IF NOT EXISTS gestion_general.IPSCodHabilitacion (
    id VARCHAR(12) PRIMARY KEY,           -- Código de habilitación
    nombre VARCHAR(100) NOT NULL,
    municipio_id CHAR(5) NOT NULL REFERENCES gestion_general.municipio(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.IPSnoREPS (
    id VARCHAR(12) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    municipio_id CHAR(5) NOT NULL REFERENCES gestion_general.municipio(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.ModalidadAtencion (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.GrupoServicios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ServiciosComplementarios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL,
    grupo_servicio_id CHAR(2) NOT NULL REFERENCES gestion_general.GrupoServicios(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSFinalidadConsulta (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSCausaExterna (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSTipoDiagnosticoPrincipal (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ConceptoRecaudo (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ViaIngresoUsuario (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.CondicionyDestinoUsuarioEgreso (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.TipoMedicamentoPOS (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.IUM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.CatalogoCUM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL,
    ium_id VARCHAR(20) NOT NULL REFERENCES gestion_general.IUM(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.DCI (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.UMM (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.FFM (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.UPR (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.TipoOtrosServicios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.IDM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.LstSiNo (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(3) NOT NULL CONSTRAINT chk_lst_si_no_check_descripcion CHECK (descripcion IN ('Sí', 'No'))
);
COMMIT;
-- ### Tabla para Internacionalización ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.traducciones (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT REFERENCES gestion_general.hospitales(id),
    clave TEXT NOT NULL,
    idioma VARCHAR(5) NOT NULL,
    valor TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_traducciones_unique_hospital_clave_idioma UNIQUE (hospital_id, clave, idioma)
);
COMMENT ON TABLE gestion_general.traducciones IS 'Almacena traducciones específicas por tenant para soportar internacionalización.';
CREATE INDEX IF NOT EXISTS idx_traducciones_hospital_clave_idioma ON gestion_general.traducciones(hospital_id, clave, idioma);
COMMIT;

-- ### Configuraciones IPS ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.configuraciones_ips (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id) ON DELETE CASCADE,
    clave TEXT NOT NULL,
    valor TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_configuraciones_ips_unique_hospital_clave UNIQUE (hospital_id, clave)
);
COMMENT ON TABLE gestion_general.configuraciones_ips IS 'Configuraciones personalizadas por tenant, como colores y logo.';
CREATE INDEX IF NOT EXISTS idx_configuraciones_ips_hospital_id_clave ON gestion_general.configuraciones_ips(hospital_id, clave);
COMMIT;

-- ### Tipos de Identificación ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.tipos_identificacion (
    id SERIAL PRIMARY KEY,
    codigo TEXT UNIQUE NOT NULL CONSTRAINT chk_tipos_identificacion_check_codigo CHECK (codigo IN ('CC', 'CE', 'PA', 'RC', 'TI', 'AS', 'MS', 'NU')),
    descripcion TEXT NOT NULL
);
COMMENT ON TABLE gestion_general.tipos_identificacion IS 'Tipos de identificación permitidos en Colombia según normativas RIPS y DIAN.';
COMMIT;

-- ### Gestión de Claves de Encriptación ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.claves_encriptacion (
    id SERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id),
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    clave BYTEA NOT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_expiracion TIMESTAMP,
    estado TEXT CONSTRAINT chk_claves_encriptacion_estado CHECK (estado IN ('activa', 'inactiva', 'revocada')),
    creada_por BIGINT -- Sin referencia inicialmente
);
COMMENT ON TABLE gestion_general.claves_encriptacion IS 'Gestión segura de claves de encriptación AES-256-CBC para datos sensibles (Ley 1581/2012).';
CREATE INDEX IF NOT EXISTS idx_claves_encriptacion_tenant_id ON gestion_general.claves_encriptacion(tenant_id);
CREATE INDEX IF NOT EXISTS idx_claves_encriptacion_hospital_id ON gestion_general.claves_encriptacion(hospital_id);
CREATE INDEX IF NOT EXISTS idx_claves_encriptacion_estado ON gestion_general.claves_encriptacion(estado);
COMMIT;

-- ### Módulos Activos ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.modulos_activos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT REFERENCES gestion_general.hospitales(id),
    modulo TEXT NOT NULL, -- ej. 'clinico', 'financiero', 'ambulancias'
    estado TEXT CHECK (estado IN ('Activo', 'Inactivo')),
    UNIQUE (hospital_id, modulo)
);
COMMENT ON TABLE gestion_general.modulos_activos IS 'Registro de módulos activos por tenant para modularidad SaaS.';
COMMIT;

-- ### Gestión de Roles y Permisos ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.roles (
    id SERIAL PRIMARY KEY,
    nombre TEXT UNIQUE NOT NULL,
    descripcion TEXT,
    permisos JSONB,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id)
);
COMMENT ON TABLE gestion_general.roles IS 'Roles de usuario con permisos granulares para acceso al sistema (seguridad Resolución 866/2021).';

CREATE TABLE IF NOT EXISTS gestion_general.permisos (
    id SERIAL PRIMARY KEY,
    nombre TEXT UNIQUE NOT NULL,
    descripcion TEXT,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id)
);
COMMENT ON TABLE gestion_general.permisos IS 'Definición de permisos granulares asignables a roles.';

CREATE TABLE IF NOT EXISTS gestion_general.roles_permisos (
    rol_id BIGINT NOT NULL REFERENCES gestion_general.roles(id),
    permiso_id BIGINT NOT NULL REFERENCES gestion_general.permisos(id),
    PRIMARY KEY (rol_id, permiso_id)
);
COMMENT ON TABLE gestion_general.roles_permisos IS 'Asignación de permisos específicos a roles para control de acceso detallado.';
COMMIT;

-- ### Usuarios del Sistema ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.usuarios (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    tipo_identificacion_id BIGINT NOT NULL REFERENCES gestion_general.tipos_identificacion(id),
    numero_identificacion BYTEA NOT NULL,
    username TEXT NOT NULL,
    -- Hacer username único por hospital
    UNIQUE (hospital_id, username),
    password TEXT NOT NULL, -- Contraseña hasheada usando bcrypt
    rol_id BIGINT NOT NULL REFERENCES gestion_general.roles(id),
    email_encrypted BYTEA,
    especialidad gestion_general.tipo_especialidad,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    numero_identificacion_tsv tsvector GENERATED ALWAYS AS (to_tsvector('spanish', encode(numero_identificacion, 'escape'))) STORED
);
COMMENT ON COLUMN gestion_general.usuarios.numero_identificacion IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';
COMMENT ON COLUMN gestion_general.usuarios.email_encrypted IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';
COMMENT ON COLUMN gestion_general.usuarios.password IS 'Contraseña hasheada usando bcrypt';
ALTER TABLE gestion_general.usuarios ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_usuarios ON gestion_general.usuarios;
CREATE POLICY acceso_usuarios ON gestion_general.usuarios
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

COMMENT ON TABLE gestion_general.usuarios IS 'Usuarios del sistema con datos sensibles encriptados (Ley 1581/2012).';
CREATE INDEX IF NOT EXISTS idx_usuarios_numero_identificacion_tsv ON gestion_general.usuarios USING GIN(numero_identificacion_tsv);
COMMIT;

-- ### Profesionales de Salud ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.ProfesionalesSalud (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    tipo_documento_id CHAR(2) NOT NULL REFERENCES gestion_general.tipo_documento(id),
    num_documento VARCHAR(20) NOT NULL,
    nombre VARCHAR(100) NOT NULL,
    especialidad VARCHAR(50),
    registro_medico VARCHAR(20) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE gestion_general.ProfesionalesSalud ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_profesionales ON gestion_general.ProfesionalesSalud;
CREATE POLICY acceso_profesionales ON gestion_general.ProfesionalesSalud
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE gestion_general.ProfesionalesSalud IS 'Registro de profesionales de la salud con información específica requerida por normativas.';
COMMIT;
-- ### Auditoría Particionada ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.auditoria (
    id BIGSERIAL,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    usuario_id BIGINT REFERENCES gestion_general.usuarios(id),
    tabla_afectada TEXT,
    accion TEXT,
    fecha TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    detalles JSONB,
    ip_address INET,
    estado_anterior JSONB,
    estado_posterior JSONB,
    motivo_modificacion TEXT,
    PRIMARY KEY (id, fecha)
) PARTITION BY RANGE (fecha);

-- Crear particiones para auditoría
CREATE TABLE IF NOT EXISTS gestion_general.auditoria_y2023 PARTITION OF gestion_general.auditoria
    FOR VALUES FROM ('2023-01-01') TO ('2024-01-01');
CREATE TABLE IF NOT EXISTS gestion_general.auditoria_y2024 PARTITION OF gestion_general.auditoria
    FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
CREATE TABLE IF NOT EXISTS gestion_general.auditoria_y2025 PARTITION OF gestion_general.auditoria
    FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

ALTER TABLE gestion_general.auditoria ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_auditoria ON gestion_general.auditoria;
CREATE POLICY acceso_auditoria ON gestion_general.auditoria
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE gestion_general.auditoria IS 'Registro de auditoría particionado por año para todas las acciones del sistema (Resolución 866/2021).';
COMMIT;

-- ############################################################################
-- ### MÓDULO CLÍNICO ###
-- ############################################################################

-- ### Gestión de Pacientes Particionada ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.pacientes (
    id BIGSERIAL NOT NULL,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    tipo_identificacion_id BIGINT NOT NULL REFERENCES gestion_general.tipos_identificacion(id),
    numero_identificacion_encrypted BYTEA NOT NULL,
    primer_nombre VARCHAR(50) NOT NULL,
    segundo_nombre VARCHAR(50),
    primer_apellido VARCHAR(50) NOT NULL,
    segundo_apellido VARCHAR(50),
    fecha_nacimiento DATE NOT NULL,
    sexo gestion_general.sexo_paciente NOT NULL,
    direccion_encrypted BYTEA,
    telefono_encrypted BYTEA,
    email_encrypted BYTEA,
    codigo_departamento TEXT CONSTRAINT chk_codigo_departamento CHECK (codigo_departamento ~ '^[0-9]{2}$'),
    codigo_municipio TEXT CONSTRAINT chk_codigo_municipio CHECK (codigo_municipio ~ '^[0-9]{5}$'),
    zona_residencia TEXT CONSTRAINT chk_zona_residencia CHECK (zona_residencia IN ('U', 'R')),
    eps TEXT REFERENCES gestion_general.eps(nit),
    regimen clinico.regimen_paciente,
    tipo_usuario TEXT NOT NULL,
    codigo_pais_residencia TEXT NOT NULL,
    incapacidad TEXT NOT NULL,
    consecutivo TEXT NOT NULL,
    codigo_pais_origen TEXT NOT NULL,
    grupo_poblacional TEXT,
    etnia TEXT,
    discapacidad TEXT,
    nivel_educativo TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, hospital_id)
) PARTITION BY HASH (hospital_id);

-- Crear las 10 particiones para clinico.pacientes
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod0 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 0);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod1 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 1);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod2 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 2);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod3 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 3);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod4 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 4);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod5 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 5);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod6 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 6);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod7 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 7);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod8 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 8);
CREATE TABLE IF NOT EXISTS clinico.pacientes_hospital_mod9 PARTITION OF clinico.pacientes FOR VALUES WITH (MODULUS 10, REMAINDER 9);

COMMENT ON COLUMN clinico.pacientes.numero_identificacion_encrypted IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';
COMMENT ON COLUMN clinico.pacientes.direccion_encrypted IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';
COMMENT ON COLUMN clinico.pacientes.telefono_encrypted IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';
COMMENT ON COLUMN clinico.pacientes.email_encrypted IS 'Encriptado con AES-256-CBC usando clave derivada de claves_encriptacion';

ALTER TABLE clinico.pacientes ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_pacientes ON clinico.pacientes;
CREATE POLICY acceso_pacientes ON clinico.pacientes
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

COMMENT ON TABLE clinico.pacientes IS 'Registro de pacientes con datos sensibles encriptados (Ley 1581/2012) y alineados con RIPS (US). Particionado por hospital_id.';

-- Crear índices en todas las particiones
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_id ON clinico.pacientes(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod0_id ON clinico.pacientes_hospital_mod0(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod1_id ON clinico.pacientes_hospital_mod1(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod2_id ON clinico.pacientes_hospital_mod2(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod3_id ON clinico.pacientes_hospital_mod3(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod4_id ON clinico.pacientes_hospital_mod4(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod5_id ON clinico.pacientes_hospital_mod5(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod6_id ON clinico.pacientes_hospital_mod6(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod7_id ON clinico.pacientes_hospital_mod7(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod8_id ON clinico.pacientes_hospital_mod8(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_hospital_mod9_id ON clinico.pacientes_hospital_mod9(hospital_id);
CREATE INDEX IF NOT EXISTS idx_pacientes_numero_identificacion ON clinico.pacientes USING HASH (numero_identificacion_encrypted);
COMMIT;
-- ### Citas Médicas ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.citas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    profesional_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    fecha TIMESTAMP NOT NULL,
    estado clinico.estado_cita DEFAULT 'Programada',
    motivo TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_citas_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.citas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_citas ON clinico.citas;
CREATE POLICY acceso_citas ON clinico.citas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.citas IS 'Gestión de citas programadas para pacientes.';
COMMIT;

-- ### Consentimientos Informados ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.consentimientos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    tipo TEXT NOT NULL CONSTRAINT chk_tipo CHECK (tipo IN ('HCE', 'telemedicina', 'tratamiento', 'datos')),
    fecha TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    documento BYTEA,
    firma_biometrica TEXT,
    alcance JSONB,
    validez_hasta TIMESTAMP,
    metodo_obtencion TEXT CONSTRAINT chk_metodo_obtencion_valido CHECK (metodo_obtencion IN ('escrito', 'electrónico', 'verbal')),
    estado TEXT CONSTRAINT chk_estado_valido CHECK (estado IN ('vigente', 'revocado', 'renovado')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_consentimientos_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.consentimientos ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_consentimientos ON clinico.consentimientos;
CREATE POLICY acceso_consentimientos ON clinico.consentimientos
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.consentimientos IS 'Registro de consentimientos informados y de datos según Ley 1581/2012 y Ley 2015/2020.';
COMMIT;

-- ### Historias Clínicas Electrónicas ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.historias_clinicas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    datos JSONB NOT NULL,
    firma_digital TEXT NOT NULL,
    metadatos_interoperabilidad JSONB,
    fhir_data JSONB NOT NULL,
    rda JSONB,
    consentimiento_hce_id BIGINT NOT NULL REFERENCES clinico.consentimientos(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    datos_tsv tsvector GENERATED ALWAYS AS (
        to_tsvector('spanish',
            coalesce(datos->>'antecedentes', '') || ' ' ||
            coalesce(datos->>'diagnosticos', '') || ' ' ||
            coalesce(datos->>'procedimientos', '') || ' ' ||
            coalesce(datos->>'tratamientos', '')
        )
    ) STORED,
    CONSTRAINT chk_datos_structure CHECK (datos @> '{"antecedentes": {}, "diagnosticos": [], "procedimientos": [], "tratamientos": []}'),
    CONSTRAINT fk_historias_clinicas_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.historias_clinicas ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS acceso_historias ON clinico.historias_clinicas;
CREATE POLICY acceso_historias ON clinico.historias_clinicas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.historias_clinicas IS 'Historias clínicas electrónicas con RDA y FHIR para interoperabilidad (Ley 2015/2020, Resolución 866/2021).';
CREATE INDEX IF NOT EXISTS idx_historias_clinicas_paciente_id ON clinico.historias_clinicas(paciente_id, paciente_hospital_id);
CREATE INDEX IF NOT EXISTS idx_historias_clinicas_fts ON clinico.historias_clinicas USING GIN(datos_tsv);
COMMIT;

-- ### Consultas Médicas Particionadas ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.consultas (
    id BIGSERIAL NOT NULL,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    fecha_inicio_atencion TIMESTAMP NOT NULL,
    historia_clinica_id BIGINT NOT NULL REFERENCES clinico.historias_clinicas(id),
    profesional_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    tipo_documento_identificacion TEXT NOT NULL,
    num_documento_identificacion TEXT NOT NULL,
    consecutivo TEXT NOT NULL,
    motivo_consulta TEXT,
    diagnostico_principal TEXT REFERENCES gestion_general.cie10(id),
    tipo_diagnostico clinico.tipo_diagnostico,
    notas TEXT,
    notas_tsv tsvector GENERATED ALWAYS AS (to_tsvector('spanish', coalesce(notas, ''))) STORED,
    codigo_cups TEXT REFERENCES gestion_general.cups(id),
    finalidad_consulta TEXT CONSTRAINT chk_finalidad_consulta_formato CHECK (finalidad_consulta ~ '^[0-1][0-9]$'),
    causa_externa TEXT CONSTRAINT chk_causa_externa_formato CHECK (causa_externa ~ '^[A-Z][0-9]{2}$'),
    valor_consulta NUMERIC(12,2) CONSTRAINT chk_valor_consulta_positivo CHECK (valor_consulta >= 0),
    cuota_moderadora NUMERIC(12,2) CONSTRAINT chk_cuota_moderadora_positivo CHECK (cuota_moderadora >= 0),
    valor_neto NUMERIC(12,2) CONSTRAINT chk_valor_neto_positivo CHECK (valor_neto >= 0),
    codigo_eps TEXT NOT NULL,
    numero_autorizacion TEXT,
    factura_id BIGINT, -- Referencia diferida
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, fecha_inicio_atencion),
    CONSTRAINT fk_consultas_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
) PARTITION BY RANGE (fecha_inicio_atencion);

CREATE TABLE IF NOT EXISTS clinico.consultas_2025 PARTITION OF clinico.consultas FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
CREATE TABLE IF NOT EXISTS clinico.consultas_2026 PARTITION OF clinico.consultas FOR VALUES FROM ('2026-01-01') TO ('2027-01-01');

ALTER TABLE clinico.consultas ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS acceso_consultas ON clinico.consultas;
CREATE POLICY acceso_consultas ON clinico.consultas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.consultas IS 'Consultas médicas con campos obligatorios para RIPS AC (Resolución 2275/2023). Particionado por fecha_inicio_atencion.';
CREATE INDEX IF NOT EXISTS idx_consultas_notas_tsv ON clinico.consultas USING GIN(notas_tsv);
CREATE INDEX IF NOT EXISTS idx_consultas_paciente_id ON clinico.consultas(paciente_id);
CREATE INDEX IF NOT EXISTS idx_consultas_2025_hospital_fecha ON clinico.consultas_2025(hospital_id, fecha_inicio_atencion);
CREATE INDEX IF NOT EXISTS idx_consultas_2026_hospital_fecha ON clinico.consultas_2026(hospital_id, fecha_inicio_atencion);
COMMIT;
-- ### Infraestructura Clínica ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.camas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    numero TEXT NOT NULL,
    estado clinico.estado_cama DEFAULT 'Libre',
    ubicacion TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE clinico.camas ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS acceso_camas ON clinico.camas;
CREATE POLICY acceso_camas ON clinico.camas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.camas IS 'Registro de camas disponibles en el hospital.';
COMMIT;

BEGIN;
CREATE TABLE IF NOT EXISTS clinico.quirofanos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    numero TEXT NOT NULL,
    estado clinico.estado_quirofano DEFAULT 'Disponible',
    ubicacion TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE clinico.quirofanos ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_quirofanos ON clinico.quirofanos;
CREATE POLICY acceso_quirofanos ON clinico.quirofanos
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.quirofanos IS 'Quirófanos disponibles para cirugías.';
COMMIT;

-- ### Cirugías ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.cirugias (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    quirofano_id BIGINT NOT NULL REFERENCES clinico.quirofanos(id),
    profesional_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    consecutivo TEXT NOT NULL,
    tipo_documento_identificacion TEXT NOT NULL,
    num_documento_identificacion TEXT NOT NULL,
    fecha_inicio TIMESTAMP NOT NULL,
    fecha_fin TIMESTAMP,
    estado TEXT CONSTRAINT chk_estado_valido CHECK (estado IN ('Programada', 'En Progreso', 'Finalizada', 'Cancelada')),
    procedimiento TEXT REFERENCES gestion_general.cups(id),
    notas TEXT,
    numero_autorizacion TEXT,
    factura_id BIGINT, -- Referencia diferida
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_cirugias_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.cirugias ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_cirugias ON clinico.cirugias;
CREATE POLICY acceso_cirugias ON clinico.cirugias
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.cirugias IS 'Registro de cirugías con campos obligatorios para RIPS AP (Resolución 2275/2023).';
COMMIT;

-- ### Hospitalizaciones ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.hospitalizaciones (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    consecutivo TEXT NOT NULL,
    tipo_documento_identificacion TEXT NOT NULL,
    num_documento_identificacion TEXT NOT NULL,
    cama_id BIGINT NOT NULL REFERENCES clinico.camas(id),
    fecha_ingreso TIMESTAMP NOT NULL,
    fecha_alta TIMESTAMP,
    motivo TEXT,
    factura_id BIGINT, -- Referencia diferida
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_hospitalizaciones_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.hospitalizaciones ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_hospitalizaciones ON clinico.hospitalizaciones;
CREATE POLICY acceso_hospitalizaciones ON clinico.hospitalizaciones
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.hospitalizaciones IS 'Registro de hospitalizaciones para RIPS AH (Resolución 2275/2023).';
COMMIT;

-- ### Urgencias ###
BEGIN;
CREATE TABLE IF NOT EXISTS clinico.urgencias (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    consecutivo TEXT NOT NULL,
    tipo_documento_identificacion TEXT NOT NULL,
    num_documento_identificacion TEXT NOT NULL,
    fecha_ingreso TIMESTAMP NOT NULL,
    triage_nivel INT CONSTRAINT chk_triage_nivel_rango CHECK (triage_nivel BETWEEN 1 AND 5),
    estado clinico.estado_urgencia DEFAULT 'En Observación',
    notas TEXT,
    diagnostico_principal TEXT REFERENCES gestion_general.cie10(id),
    codigo_cups TEXT REFERENCES gestion_general.cups(id),
    numero_autorizacion TEXT,
    factura_id BIGINT, -- Referencia diferida
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_urgencias_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE clinico.urgencias ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_urgencias ON clinico.urgencias;
CREATE POLICY acceso_urgencias ON clinico.urgencias
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE clinico.urgencias IS 'Gestión de pacientes en urgencias para RIPS AU (Resolución 2275/2023).';
COMMIT;
-- ############################################################################
-- ### MÓDULO DE INVENTARIO Y FARMACIA ###
-- ############################################################################

-- ### Inventario de Medicamentos y Consumibles ###
BEGIN;
CREATE TABLE IF NOT EXISTS inventario.inventario (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    tipo_recurso inventario.tipo_recurso,
    nombre TEXT NOT NULL,
    codigo TEXT UNIQUE NOT NULL,
    stock_actual INT NOT NULL CONSTRAINT chk_stock_actual_positivo CHECK (stock_actual >= 0),
    stock_minimo INT NOT NULL,
    ubicacion TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE inventario.inventario ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_inventario ON inventario.inventario;
CREATE POLICY acceso_inventario ON inventario.inventario
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE inventario.inventario IS 'Inventario de medicamentos y consumibles médicos.';
COMMIT;

-- ### Inventario de Equipos Médicos ###
BEGIN;
CREATE TABLE IF NOT EXISTS inventario.inventario_equipos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    tipo_equipo inventario.tipo_equipo NOT NULL,
    nombre TEXT NOT NULL,
    codigo TEXT UNIQUE NOT NULL,
    numero_serie TEXT UNIQUE,
    estado inventario.estado_recurso DEFAULT 'Operativo',
    ubicacion TEXT,
    fecha_adquisicion DATE,
    fecha_ultimo_mantenimiento TIMESTAMP,
    fecha_proximo_mantenimiento TIMESTAMP,
    tipo_mantenimiento TEXT CONSTRAINT chk_tipo_mantenimiento CHECK (tipo_mantenimiento IN ('Preventivo', 'Correctivo')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE inventario.inventario_equipos ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_inventario_equipos ON inventario.inventario_equipos;
CREATE POLICY acceso_inventario_equipos ON inventario.inventario_equipos
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
CREATE INDEX IF NOT EXISTS idx_inventario_equipos_hospital_id ON inventario.inventario_equipos(hospital_id);
CREATE INDEX IF NOT EXISTS idx_inventario_equipos_estado ON inventario.inventario_equipos(estado);
COMMENT ON TABLE inventario.inventario_equipos IS 'Inventario de equipos médicos con seguimiento de mantenimiento y estado.';
COMMIT;

-- ### Dispensaciones de Medicamentos ###
BEGIN;
CREATE TABLE IF NOT EXISTS inventario.dispensaciones (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    inventario_id BIGINT NOT NULL REFERENCES inventario.inventario(id),
    cantidad INT NOT NULL CONSTRAINT chk_cantidad_positiva CHECK (cantidad > 0),
    fecha TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    profesional_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    factura_id BIGINT, -- Referencia diferida
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_dispensaciones_paciente
        FOREIGN KEY (paciente_id, paciente_hospital_id)
        REFERENCES clinico.pacientes (id, hospital_id)
);
ALTER TABLE inventario.dispensaciones ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_dispensaciones ON inventario.dispensaciones;
CREATE POLICY acceso_dispensaciones ON inventario.dispensaciones
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE inventario.dispensaciones IS 'Registro de dispensación de medicamentos e insumos (base para RIPS AM).';
COMMIT;

-- ############################################################################
-- ### MÓDULO FINANCIERO ###
-- ############################################################################

-- ### Facturas Electrónicas ###
BEGIN;
CREATE TABLE IF NOT EXISTS financiero.facturas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL, -- Columna para la parte hospital_id de la FK a pacientes
    numero_factura TEXT UNIQUE NOT NULL,
    cufe TEXT UNIQUE,
    firma_digital TEXT,
    codigo_qr TEXT,
    fecha_emision DATE NOT NULL,
    fecha_vencimiento DATE,
    monto_total NUMERIC(15,2) CONSTRAINT chk_monto_total CHECK (monto_total >= 0),
    estado financiero.estado_factura,
    nit_ips TEXT NOT NULL,
    tipo_operacion TEXT CONSTRAINT chk_tipo_operacion CHECK (tipo_operacion IN ('10', '20', '30')),
    periodo_facturado DATE NOT NULL,
    fecha_fin_periodo DATE,
    detalles_qr JSONB,
    detalles_qr_completo JSONB,
    codigo_prestador TEXT NOT NULL,
    tipo_servicio TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_fechas_vencimiento CHECK (fecha_vencimiento > fecha_emision),
    CONSTRAINT fk_facturas_paciente FOREIGN KEY (paciente_id, paciente_hospital_id) REFERENCES clinico.pacientes(id, hospital_id)
);
ALTER TABLE financiero.facturas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_facturas ON financiero.facturas;
CREATE POLICY acceso_facturas ON financiero.facturas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE financiero.facturas IS 'Facturas electrónicas según Resolución 000165/2023 DIAN, con periodo facturado obligatorio.';
CREATE INDEX IF NOT EXISTS idx_facturas_numero_factura ON financiero.facturas(numero_factura);
COMMIT;
-- ### Detalles de Facturas ###
BEGIN;
CREATE TABLE IF NOT EXISTS financiero.detalles_facturas (
    id BIGSERIAL PRIMARY KEY,
    factura_id BIGINT NOT NULL REFERENCES financiero.facturas(id),
    descripcion TEXT NOT NULL,
    cantidad INT NOT NULL CONSTRAINT chk_cantidad CHECK (cantidad > 0),
    valor_unitario NUMERIC(12,2) CONSTRAINT chk_valor_unitario CHECK (valor_unitario >= 0),
    valor_total NUMERIC(12,2) CONSTRAINT chk_valor_total CHECK (valor_total >= 0),
    codigo_cups TEXT REFERENCES gestion_general.cups(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE financiero.detalles_facturas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_detalles_facturas ON financiero.detalles_facturas;
CREATE POLICY acceso_detalles_facturas ON financiero.detalles_facturas
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM financiero.facturas f
            WHERE f.id = financiero.detalles_facturas.factura_id
            AND f.hospital_id = current_setting('app.current_hospital_id')::BIGINT
        )
    );
COMMENT ON TABLE financiero.detalles_facturas IS 'Detalles de ítems facturados para facturación electrónica DIAN.';
COMMIT;

-- ### Pagos ###
BEGIN;
CREATE TABLE IF NOT EXISTS financiero.pagos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    factura_id BIGINT NOT NULL REFERENCES financiero.facturas(id),
    monto NUMERIC(15,2) CONSTRAINT chk_monto_positivo CHECK (monto >= 0),
    fecha_pago TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metodo_pago TEXT CONSTRAINT chk_metodo_pago_valido CHECK (metodo_pago IN ('Efectivo', 'Transferencia', 'Tarjeta', 'Otro')),
    estado financiero.estado_pago DEFAULT 'Pendiente',
    referencia_pago TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE financiero.pagos ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_pagos ON financiero.pagos;
CREATE POLICY acceso_pagos ON financiero.pagos
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);
COMMENT ON TABLE financiero.pagos IS 'Registro de pagos realizados a facturas.';
COMMIT;

-- ############################################################################
-- ### MÓDULO DE AMBULANCIAS FORTALECIDO CON GPS/CELULAR ###
-- ############################################################################

-- ### Ambulancias con Geolocalización ###
BEGIN;
CREATE TABLE IF NOT EXISTS ambulancias.ambulancias (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    placa TEXT UNIQUE NOT NULL,
    tipo ambulancias.tipo_ambulancia NOT NULL,
    estado ambulancias.estado_ambulancia DEFAULT 'Disponible',
    ubicacion_actual geometry(Point, 4326),
    ubicacion_base geometry(Point, 4326) NOT NULL,

    -- Información del vehículo
    marca TEXT NOT NULL,
    modelo TEXT NOT NULL,
    año INTEGER CONSTRAINT chk_año_valido CHECK (año >= 1990 AND año <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
    numero_motor TEXT,
    numero_chasis TEXT,

    -- Equipamiento médico
    equipamiento JSONB DEFAULT '{}'::jsonb,
    capacidad_pacientes INTEGER DEFAULT 1 CONSTRAINT chk_capacidad_positiva CHECK (capacidad_pacientes > 0),

    -- Información de seguros y documentos
    numero_soat TEXT,
    fecha_vencimiento_soat DATE,
    numero_tecnomecanica TEXT,
    fecha_vencimiento_tecnomecanica DATE,

    -- Dispositivos de rastreo
    dispositivo_gps_id TEXT UNIQUE,
    dispositivo_celular_imei TEXT UNIQUE,
    numero_celular TEXT,

    -- Configuración de rastreo
    intervalo_reporte_gps INTEGER DEFAULT 30, -- segundos
    precision_gps NUMERIC(10,6),

    -- Información operativa
    kilometraje_actual INTEGER DEFAULT 0,
    fecha_ultimo_mantenimiento DATE,
    fecha_proximo_mantenimiento DATE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE ambulancias.ambulancias ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_ambulancias ON ambulancias.ambulancias;
CREATE POLICY acceso_ambulancias ON ambulancias.ambulancias
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

-- Índices espaciales para geolocalización
CREATE INDEX IF NOT EXISTS idx_ambulancias_ubicacion_actual ON ambulancias.ambulancias USING GIST(ubicacion_actual);
CREATE INDEX IF NOT EXISTS idx_ambulancias_ubicacion_base ON ambulancias.ambulancias USING GIST(ubicacion_base);
CREATE INDEX IF NOT EXISTS idx_ambulancias_estado ON ambulancias.ambulancias(estado);
CREATE INDEX IF NOT EXISTS idx_ambulancias_tipo ON ambulancias.ambulancias(tipo);

COMMENT ON TABLE ambulancias.ambulancias IS 'Registro de ambulancias con capacidades de geolocalización GPS/celular y seguimiento en tiempo real.';
COMMENT ON COLUMN ambulancias.ambulancias.ubicacion_actual IS 'Ubicación actual de la ambulancia reportada por GPS/celular';
COMMENT ON COLUMN ambulancias.ambulancias.ubicacion_base IS 'Ubicación base donde debe retornar la ambulancia';
COMMENT ON COLUMN ambulancias.ambulancias.dispositivo_gps_id IS 'ID único del dispositivo GPS instalado';
COMMENT ON COLUMN ambulancias.ambulancias.dispositivo_celular_imei IS 'IMEI del dispositivo celular para rastreo';
COMMENT ON COLUMN ambulancias.ambulancias.intervalo_reporte_gps IS 'Intervalo en segundos para reportes automáticos de ubicación';
COMMIT;
-- ### Servicios de Ambulancia con Rutas GPS ###
BEGIN;
CREATE TABLE IF NOT EXISTS ambulancias.servicios_ambulancia (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    ambulancia_id BIGINT NOT NULL REFERENCES ambulancias.ambulancias(id),
    paciente_id BIGINT,
    paciente_hospital_id BIGINT,

    -- Información del servicio
    numero_servicio TEXT UNIQUE NOT NULL,
    prioridad ambulancias.prioridad_servicio NOT NULL,
    tipo_servicio TEXT CONSTRAINT chk_tipo_servicio CHECK (tipo_servicio IN ('Emergencia', 'Traslado', 'Programado')),

    -- Ubicaciones
    ubicacion_origen geometry(Point, 4326) NOT NULL,
    direccion_origen TEXT NOT NULL,
    ubicacion_destino geometry(Point, 4326) NOT NULL,
    direccion_destino TEXT NOT NULL,

    -- Tiempos del servicio
    fecha_solicitud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_asignacion TIMESTAMP,
    fecha_salida TIMESTAMP,
    fecha_llegada_origen TIMESTAMP,
    fecha_salida_origen TIMESTAMP,
    fecha_llegada_destino TIMESTAMP,
    fecha_finalizacion TIMESTAMP,

    -- Información médica
    motivo_traslado TEXT,
    diagnostico_inicial TEXT REFERENCES gestion_general.cie10(id),
    signos_vitales JSONB,
    observaciones TEXT,

    -- Personal asignado
    conductor_id BIGINT REFERENCES gestion_general.usuarios(id),
    paramedico_id BIGINT REFERENCES gestion_general.usuarios(id),
    medico_id BIGINT REFERENCES gestion_general.usuarios(id),

    -- Información administrativa
    estado TEXT DEFAULT 'Solicitado' CONSTRAINT chk_estado_servicio CHECK (
        estado IN ('Solicitado', 'Asignado', 'En_Ruta_Origen', 'En_Origen', 'En_Ruta_Destino', 'En_Destino', 'Finalizado', 'Cancelado')
    ),
    costo_estimado NUMERIC(12,2),
    costo_real NUMERIC(12,2),
    kilometros_recorridos NUMERIC(10,2),

    -- Facturación
    factura_id BIGINT REFERENCES financiero.facturas(id),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_servicios_ambulancia_paciente
        FOREIGN KEY (paciente_id, paciente_hospital_id)
        REFERENCES clinico.pacientes(id, hospital_id)
);

ALTER TABLE ambulancias.servicios_ambulancia ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_servicios_ambulancia ON ambulancias.servicios_ambulancia;
CREATE POLICY acceso_servicios_ambulancia ON ambulancias.servicios_ambulancia
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

-- Índices espaciales y de rendimiento
CREATE INDEX IF NOT EXISTS idx_servicios_ambulancia_ubicacion_origen ON ambulancias.servicios_ambulancia USING GIST(ubicacion_origen);
CREATE INDEX IF NOT EXISTS idx_servicios_ambulancia_ubicacion_destino ON ambulancias.servicios_ambulancia USING GIST(ubicacion_destino);
CREATE INDEX IF NOT EXISTS idx_servicios_ambulancia_estado ON ambulancias.servicios_ambulancia(estado);
CREATE INDEX IF NOT EXISTS idx_servicios_ambulancia_prioridad ON ambulancias.servicios_ambulancia(prioridad);
CREATE INDEX IF NOT EXISTS idx_servicios_ambulancia_fecha_solicitud ON ambulancias.servicios_ambulancia(fecha_solicitud);

COMMENT ON TABLE ambulancias.servicios_ambulancia IS 'Servicios de ambulancia con seguimiento completo de rutas, tiempos y geolocalización.';
COMMIT;

-- ### Rutas GPS de Ambulancias ###
BEGIN;
CREATE TABLE IF NOT EXISTS ambulancias.rutas_gps (
    id BIGSERIAL PRIMARY KEY,
    servicio_ambulancia_id BIGINT NOT NULL REFERENCES ambulancias.servicios_ambulancia(id),
    ambulancia_id BIGINT NOT NULL REFERENCES ambulancias.ambulancias(id),

    -- Datos de ubicación
    ubicacion geometry(Point, 4326) NOT NULL,
    altitud NUMERIC(8,2),
    precision_metros NUMERIC(6,2),
    velocidad_kmh NUMERIC(6,2),
    direccion_grados NUMERIC(5,2), -- 0-360 grados

    -- Información del reporte
    timestamp_gps TIMESTAMP NOT NULL,
    timestamp_servidor TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fuente TEXT CONSTRAINT chk_fuente_ubicacion CHECK (fuente IN ('GPS', 'Celular', 'Manual')),

    -- Datos del dispositivo
    dispositivo_id TEXT,
    nivel_bateria INTEGER CONSTRAINT chk_nivel_bateria CHECK (nivel_bateria BETWEEN 0 AND 100),
    calidad_señal INTEGER CONSTRAINT chk_calidad_señal CHECK (calidad_señal BETWEEN 0 AND 100),

    -- Información adicional
    evento TEXT, -- 'inicio_servicio', 'llegada_origen', 'salida_origen', 'llegada_destino', 'fin_servicio'
    notas TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Particionamiento por fecha para optimizar consultas históricas
-- CREATE TABLE ambulancias.rutas_gps_2025 PARTITION OF ambulancias.rutas_gps FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

-- Índices espaciales y temporales
CREATE INDEX IF NOT EXISTS idx_rutas_gps_ubicacion ON ambulancias.rutas_gps USING GIST(ubicacion);
CREATE INDEX IF NOT EXISTS idx_rutas_gps_servicio_timestamp ON ambulancias.rutas_gps(servicio_ambulancia_id, timestamp_gps);
CREATE INDEX IF NOT EXISTS idx_rutas_gps_ambulancia_timestamp ON ambulancias.rutas_gps(ambulancia_id, timestamp_gps);

COMMENT ON TABLE ambulancias.rutas_gps IS 'Registro detallado de rutas GPS de ambulancias para seguimiento en tiempo real y análisis histórico.';
COMMENT ON COLUMN ambulancias.rutas_gps.precision_metros IS 'Precisión del GPS en metros';
COMMENT ON COLUMN ambulancias.rutas_gps.fuente IS 'Fuente de la ubicación: GPS dedicado, celular o entrada manual';
COMMIT;
-- ############################################################################
-- ### MÓDULO DE TELEMEDICINA ###
-- ############################################################################

-- ### Teleconsultas ###
BEGIN;
CREATE TABLE IF NOT EXISTS telemedicina.teleconsultas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL,
    profesional_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),

    -- Información de la teleconsulta
    fecha_programada TIMESTAMP NOT NULL,
    fecha_inicio TIMESTAMP,
    fecha_fin TIMESTAMP,
    duracion_minutos INTEGER,

    -- Plataforma y tecnología
    plataforma TEXT CONSTRAINT chk_plataforma CHECK (plataforma IN ('WebRTC', 'Zoom', 'Teams', 'Jitsi', 'Otro')),
    url_sala TEXT,
    id_reunion TEXT,
    calidad_conexion TEXT CONSTRAINT chk_calidad_conexion CHECK (calidad_conexion IN ('Excelente', 'Buena', 'Regular', 'Mala')),

    -- Información médica
    motivo_consulta TEXT NOT NULL,
    diagnostico_principal TEXT REFERENCES gestion_general.cie10(id),
    diagnosticos_secundarios JSONB DEFAULT '[]'::jsonb,
    plan_tratamiento TEXT,
    prescripciones JSONB DEFAULT '[]'::jsonb,

    -- Consentimiento y documentación
    consentimiento_telemedicina_id BIGINT NOT NULL REFERENCES clinico.consentimientos(id),
    grabacion_autorizada BOOLEAN DEFAULT FALSE,
    url_grabacion TEXT,

    -- Información técnica
    dispositivo_paciente TEXT, -- 'móvil', 'tablet', 'computador'
    navegador_paciente TEXT,
    ip_paciente INET,
    ubicacion_paciente geometry(Point, 4326),

    -- Estado y seguimiento
    estado TEXT DEFAULT 'Programada' CONSTRAINT chk_estado_teleconsulta CHECK (
        estado IN ('Programada', 'En_Progreso', 'Finalizada', 'Cancelada', 'No_Asistio')
    ),
    motivo_cancelacion TEXT,

    -- Facturación
    factura_id BIGINT REFERENCES financiero.facturas(id),

    -- Metadatos
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_teleconsultas_paciente
        FOREIGN KEY (paciente_id, paciente_hospital_id)
        REFERENCES clinico.pacientes(id, hospital_id)
);

ALTER TABLE telemedicina.teleconsultas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_teleconsultas ON telemedicina.teleconsultas;
CREATE POLICY acceso_teleconsultas ON telemedicina.teleconsultas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_teleconsultas_fecha_programada ON telemedicina.teleconsultas(fecha_programada);
CREATE INDEX IF NOT EXISTS idx_teleconsultas_estado ON telemedicina.teleconsultas(estado);
CREATE INDEX IF NOT EXISTS idx_teleconsultas_profesional ON telemedicina.teleconsultas(profesional_id);

COMMENT ON TABLE telemedicina.teleconsultas IS 'Teleconsultas médicas con seguimiento técnico y cumplimiento normativo (Resolución 2654/2019).';
COMMIT;

-- ### Monitoreo Remoto de Pacientes ###
BEGIN;
CREATE TABLE IF NOT EXISTS telemedicina.monitoreo_remoto (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    paciente_id BIGINT NOT NULL,
    paciente_hospital_id BIGINT NOT NULL,
    profesional_responsable_id BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),

    -- Información del programa de monitoreo
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE,
    tipo_monitoreo TEXT CONSTRAINT chk_tipo_monitoreo CHECK (
        tipo_monitoreo IN ('Diabetes', 'Hipertension', 'Cardiaco', 'Respiratorio', 'Otro')
    ),
    frecuencia_reportes TEXT CONSTRAINT chk_frecuencia CHECK (
        frecuencia_reportes IN ('Diario', 'Semanal', 'Quincenal', 'Mensual')
    ),

    -- Dispositivos y tecnología
    dispositivos_asignados JSONB DEFAULT '[]'::jsonb,
    aplicacion_movil TEXT,
    version_app TEXT,

    -- Parámetros a monitorear
    parametros_vitales JSONB DEFAULT '{}'::jsonb,
    alertas_configuradas JSONB DEFAULT '[]'::jsonb,

    -- Estado del programa
    estado TEXT DEFAULT 'Activo' CONSTRAINT chk_estado_monitoreo CHECK (
        estado IN ('Activo', 'Pausado', 'Finalizado', 'Cancelado')
    ),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_monitoreo_remoto_paciente
        FOREIGN KEY (paciente_id, paciente_hospital_id)
        REFERENCES clinico.pacientes(id, hospital_id)
);

ALTER TABLE telemedicina.monitoreo_remoto ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_monitoreo_remoto ON telemedicina.monitoreo_remoto;
CREATE POLICY acceso_monitoreo_remoto ON telemedicina.monitoreo_remoto
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

COMMENT ON TABLE telemedicina.monitoreo_remoto IS 'Programas de monitoreo remoto de pacientes con dispositivos IoT y aplicaciones móviles.';
COMMIT;
-- ############################################################################
-- ### MÓDULO ERP FORTALECIDO ###
-- ############################################################################

-- ### Recursos Humanos - Empleados ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.empleados (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    usuario_id BIGINT REFERENCES gestion_general.usuarios(id),

    -- Información personal
    tipo_identificacion_id BIGINT NOT NULL REFERENCES gestion_general.tipos_identificacion(id),
    numero_identificacion_encrypted BYTEA NOT NULL,
    primer_nombre VARCHAR(50) NOT NULL,
    segundo_nombre VARCHAR(50),
    primer_apellido VARCHAR(50) NOT NULL,
    segundo_apellido VARCHAR(50),
    fecha_nacimiento DATE NOT NULL,
    sexo CHAR(1) CONSTRAINT chk_sexo_empleado CHECK (sexo IN ('M', 'F', 'O')),

    -- Información de contacto (encriptada)
    direccion_encrypted BYTEA,
    telefono_encrypted BYTEA,
    email_encrypted BYTEA,
    contacto_emergencia_encrypted BYTEA,

    -- Información laboral
    codigo_empleado TEXT UNIQUE NOT NULL,
    fecha_ingreso DATE NOT NULL,
    fecha_retiro DATE,
    estado_empleado TEXT DEFAULT 'Activo' CONSTRAINT chk_estado_empleado CHECK (
        estado_empleado IN ('Activo', 'Inactivo', 'Vacaciones', 'Incapacidad', 'Licencia', 'Retirado')
    ),

    -- Cargo y departamento
    cargo TEXT NOT NULL,
    departamento TEXT NOT NULL,
    jefe_inmediato_id BIGINT REFERENCES erp.empleados(id),

    -- Información salarial
    salario_base NUMERIC(15,2) CONSTRAINT chk_salario_positivo CHECK (salario_base >= 0),
    tipo_contrato TEXT CONSTRAINT chk_tipo_contrato CHECK (
        tipo_contrato IN ('Indefinido', 'Fijo', 'Prestacion_Servicios', 'Aprendizaje', 'Practicas')
    ),

    -- Información académica y profesional
    nivel_educativo TEXT,
    profesion TEXT,
    especialidades JSONB DEFAULT '[]'::jsonb,
    licencias_profesionales JSONB DEFAULT '[]'::jsonb,

    -- Información de seguridad social
    eps TEXT,
    arl TEXT,
    fondo_pensiones TEXT,
    fondo_cesantias TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE erp.empleados ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_empleados ON erp.empleados;
CREATE POLICY acceso_empleados ON erp.empleados
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_empleados_codigo ON erp.empleados(codigo_empleado);
CREATE INDEX IF NOT EXISTS idx_empleados_estado ON erp.empleados(estado_empleado);
CREATE INDEX IF NOT EXISTS idx_empleados_departamento ON erp.empleados(departamento);

COMMENT ON TABLE erp.empleados IS 'Gestión integral de empleados con datos sensibles encriptados y cumplimiento laboral colombiano.';
COMMIT;

-- ### Nómina ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.nomina (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    empleado_id BIGINT NOT NULL REFERENCES erp.empleados(id),

    -- Período de nómina
    periodo_año INTEGER NOT NULL,
    periodo_mes INTEGER NOT NULL CONSTRAINT chk_mes_valido CHECK (periodo_mes BETWEEN 1 AND 12),
    fecha_pago DATE NOT NULL,

    -- Devengados
    salario_basico NUMERIC(15,2) DEFAULT 0,
    horas_extras NUMERIC(15,2) DEFAULT 0,
    recargos_nocturnos NUMERIC(15,2) DEFAULT 0,
    dominicales_festivos NUMERIC(15,2) DEFAULT 0,
    comisiones NUMERIC(15,2) DEFAULT 0,
    bonificaciones NUMERIC(15,2) DEFAULT 0,
    auxilio_transporte NUMERIC(15,2) DEFAULT 0,
    otros_devengados NUMERIC(15,2) DEFAULT 0,

    -- Deducciones
    salud NUMERIC(15,2) DEFAULT 0,
    pension NUMERIC(15,2) DEFAULT 0,
    fondo_solidaridad NUMERIC(15,2) DEFAULT 0,
    retencion_fuente NUMERIC(15,2) DEFAULT 0,
    prestamos NUMERIC(15,2) DEFAULT 0,
    embargos NUMERIC(15,2) DEFAULT 0,
    otras_deducciones NUMERIC(15,2) DEFAULT 0,

    -- Totales calculados
    total_devengado NUMERIC(15,2) GENERATED ALWAYS AS (
        salario_basico + horas_extras + recargos_nocturnos + dominicales_festivos +
        comisiones + bonificaciones + auxilio_transporte + otros_devengados
    ) STORED,
    total_deducciones NUMERIC(15,2) GENERATED ALWAYS AS (
        salud + pension + fondo_solidaridad + retencion_fuente +
        prestamos + embargos + otras_deducciones
    ) STORED,
    neto_pagar NUMERIC(15,2) GENERATED ALWAYS AS (
        (salario_basico + horas_extras + recargos_nocturnos + dominicales_festivos +
         comisiones + bonificaciones + auxilio_transporte + otros_devengados) -
        (salud + pension + fondo_solidaridad + retencion_fuente +
         prestamos + embargos + otras_deducciones)
    ) STORED,

    -- Estado y aprobación
    estado TEXT DEFAULT 'Borrador' CONSTRAINT chk_estado_nomina CHECK (
        estado IN ('Borrador', 'Aprobada', 'Pagada', 'Anulada')
    ),
    aprobado_por BIGINT REFERENCES gestion_general.usuarios(id),
    fecha_aprobacion TIMESTAMP,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT uk_nomina_empleado_periodo UNIQUE (empleado_id, periodo_año, periodo_mes)
);

ALTER TABLE erp.nomina ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_nomina ON erp.nomina;
CREATE POLICY acceso_nomina ON erp.nomina
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_nomina_periodo ON erp.nomina(periodo_año, periodo_mes);
CREATE INDEX IF NOT EXISTS idx_nomina_estado ON erp.nomina(estado);

COMMENT ON TABLE erp.nomina IS 'Gestión de nómina con cálculos automáticos y cumplimiento normativo laboral colombiano.';
COMMIT;
-- ### Contabilidad - Plan de Cuentas ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.plan_cuentas (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    codigo_cuenta TEXT NOT NULL,
    nombre_cuenta TEXT NOT NULL,
    tipo_cuenta TEXT CONSTRAINT chk_tipo_cuenta CHECK (
        tipo_cuenta IN ('Activo', 'Pasivo', 'Patrimonio', 'Ingreso', 'Gasto', 'Costo')
    ),
    nivel INTEGER NOT NULL CONSTRAINT chk_nivel_cuenta CHECK (nivel BETWEEN 1 AND 6),
    cuenta_padre_id BIGINT REFERENCES erp.plan_cuentas(id),
    es_auxiliar BOOLEAN DEFAULT FALSE,
    estado TEXT DEFAULT 'Activa' CONSTRAINT chk_estado_cuenta CHECK (estado IN ('Activa', 'Inactiva')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT uk_plan_cuentas_hospital_codigo UNIQUE (hospital_id, codigo_cuenta)
);

ALTER TABLE erp.plan_cuentas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_plan_cuentas ON erp.plan_cuentas;
CREATE POLICY acceso_plan_cuentas ON erp.plan_cuentas
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_plan_cuentas_codigo ON erp.plan_cuentas(codigo_cuenta);
CREATE INDEX IF NOT EXISTS idx_plan_cuentas_tipo ON erp.plan_cuentas(tipo_cuenta);

COMMENT ON TABLE erp.plan_cuentas IS 'Plan de cuentas contables según normativa colombiana (Decreto 2420/2015).';
COMMIT;

-- ### Asientos Contables ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.asientos_contables (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    numero_asiento TEXT NOT NULL,
    fecha_asiento DATE NOT NULL,
    descripcion TEXT NOT NULL,
    tipo_asiento TEXT CONSTRAINT chk_tipo_asiento CHECK (
        tipo_asiento IN ('Apertura', 'Diario', 'Ajuste', 'Cierre')
    ),
    estado TEXT DEFAULT 'Borrador' CONSTRAINT chk_estado_asiento CHECK (
        estado IN ('Borrador', 'Contabilizado', 'Anulado')
    ),
    total_debito NUMERIC(15,2) DEFAULT 0,
    total_credito NUMERIC(15,2) DEFAULT 0,
    elaborado_por BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    aprobado_por BIGINT REFERENCES gestion_general.usuarios(id),
    fecha_aprobacion TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_asiento_cuadrado CHECK (total_debito = total_credito),
    CONSTRAINT uk_asientos_hospital_numero UNIQUE (hospital_id, numero_asiento)
);

ALTER TABLE erp.asientos_contables ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_asientos_contables ON erp.asientos_contables;
CREATE POLICY acceso_asientos_contables ON erp.asientos_contables
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_asientos_fecha ON erp.asientos_contables(fecha_asiento);
CREATE INDEX IF NOT EXISTS idx_asientos_estado ON erp.asientos_contables(estado);

COMMENT ON TABLE erp.asientos_contables IS 'Asientos contables con validación de cuadre automático.';
COMMIT;

-- ### Detalles de Asientos Contables ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.detalles_asientos (
    id BIGSERIAL PRIMARY KEY,
    asiento_id BIGINT NOT NULL REFERENCES erp.asientos_contables(id) ON DELETE CASCADE,
    cuenta_id BIGINT NOT NULL REFERENCES erp.plan_cuentas(id),
    descripcion TEXT NOT NULL,
    debito NUMERIC(15,2) DEFAULT 0 CONSTRAINT chk_debito_positivo CHECK (debito >= 0),
    credito NUMERIC(15,2) DEFAULT 0 CONSTRAINT chk_credito_positivo CHECK (credito >= 0),
    centro_costo TEXT,
    documento_soporte TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_debito_o_credito CHECK (
        (debito > 0 AND credito = 0) OR (credito > 0 AND debito = 0)
    )
);

CREATE INDEX IF NOT EXISTS idx_detalles_asientos_cuenta ON erp.detalles_asientos(cuenta_id);
CREATE INDEX IF NOT EXISTS idx_detalles_asientos_asiento ON erp.detalles_asientos(asiento_id);

COMMENT ON TABLE erp.detalles_asientos IS 'Detalles de movimientos contables con validación de débito/crédito.';
COMMIT;

-- ### Presupuestos ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.presupuestos (
    id BIGSERIAL PRIMARY KEY,
    hospital_id BIGINT NOT NULL REFERENCES gestion_general.hospitales(id),
    año_presupuestal INTEGER NOT NULL,
    nombre_presupuesto TEXT NOT NULL,
    descripcion TEXT,
    estado TEXT DEFAULT 'Borrador' CONSTRAINT chk_estado_presupuesto CHECK (
        estado IN ('Borrador', 'Aprobado', 'Ejecutandose', 'Cerrado')
    ),
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE NOT NULL,
    monto_total_aprobado NUMERIC(15,2) CONSTRAINT chk_monto_presupuesto_positivo CHECK (monto_total_aprobado >= 0),
    elaborado_por BIGINT NOT NULL REFERENCES gestion_general.usuarios(id),
    aprobado_por BIGINT REFERENCES gestion_general.usuarios(id),
    fecha_aprobacion TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_fechas_presupuesto CHECK (fecha_fin > fecha_inicio),
    CONSTRAINT uk_presupuesto_hospital_año UNIQUE (hospital_id, año_presupuestal)
);

ALTER TABLE erp.presupuestos ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS acceso_presupuestos ON erp.presupuestos;
CREATE POLICY acceso_presupuestos ON erp.presupuestos
    FOR ALL
    USING (hospital_id = current_setting('app.current_hospital_id')::BIGINT)
    WITH CHECK (hospital_id = current_setting('app.current_hospital_id')::BIGINT);

CREATE INDEX IF NOT EXISTS idx_presupuestos_año ON erp.presupuestos(año_presupuestal);
CREATE INDEX IF NOT EXISTS idx_presupuestos_estado ON erp.presupuestos(estado);

COMMENT ON TABLE erp.presupuestos IS 'Presupuestos anuales con control de ejecución y seguimiento.';
COMMIT;

-- ### Rubros Presupuestales ###
BEGIN;
CREATE TABLE IF NOT EXISTS erp.rubros_presupuestales (
    id BIGSERIAL PRIMARY KEY,
    presupuesto_id BIGINT NOT NULL REFERENCES erp.presupuestos(id) ON DELETE CASCADE,
    cuenta_id BIGINT NOT NULL REFERENCES erp.plan_cuentas(id),
    codigo_rubro TEXT NOT NULL,
    nombre_rubro TEXT NOT NULL,
    monto_aprobado NUMERIC(15,2) CONSTRAINT chk_monto_rubro_positivo CHECK (monto_aprobado >= 0),
    monto_ejecutado NUMERIC(15,2) DEFAULT 0,
    monto_comprometido NUMERIC(15,2) DEFAULT 0,
    centro_costo TEXT,
    responsable_id BIGINT REFERENCES gestion_general.usuarios(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_ejecucion_presupuestal CHECK (
        monto_ejecutado + monto_comprometido <= monto_aprobado
    )
);

CREATE INDEX IF NOT EXISTS idx_rubros_presupuesto ON erp.rubros_presupuestales(presupuesto_id);
CREATE INDEX IF NOT EXISTS idx_rubros_cuenta ON erp.rubros_presupuestales(cuenta_id);

COMMENT ON TABLE erp.rubros_presupuestales IS 'Rubros presupuestales con control de ejecución y disponibilidad.';
COMMIT;
-- ############################################################################
-- ### FUNCIONES Y TRIGGERS ###
-- ############################################################################

-- ### Función de Encriptación AES-256-CBC ###
BEGIN;
CREATE OR REPLACE FUNCTION gestion_general.encrypt_data(data TEXT, hospital_id BIGINT)
RETURNS BYTEA AS $$
DECLARE
    encryption_key BYTEA;
BEGIN
    -- Obtener la clave de encriptación activa para el hospital
    SELECT clave INTO encryption_key
    FROM gestion_general.claves_encriptacion
    WHERE tenant_id = (SELECT tenant_id FROM gestion_general.hospitales WHERE id = hospital_id)
    AND estado = 'activa'
    ORDER BY fecha_creacion DESC
    LIMIT 1;

    IF encryption_key IS NULL THEN
        RAISE EXCEPTION 'No se encontró clave de encriptación activa para el hospital %', hospital_id;
    END IF;

    -- Encriptar usando AES-256-CBC (requiere extensión pgcrypto)
    RETURN encrypt(data::bytea, encryption_key, 'aes-cbc');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
COMMIT;

-- ### Función de Desencriptación ###
BEGIN;
CREATE OR REPLACE FUNCTION gestion_general.decrypt_data(encrypted_data BYTEA, hospital_id BIGINT)
RETURNS TEXT AS $$
DECLARE
    encryption_key BYTEA;
BEGIN
    -- Obtener la clave de encriptación activa para el hospital
    SELECT clave INTO encryption_key
    FROM gestion_general.claves_encriptacion
    WHERE tenant_id = (SELECT tenant_id FROM gestion_general.hospitales WHERE id = hospital_id)
    AND estado = 'activa'
    ORDER BY fecha_creacion DESC
    LIMIT 1;

    IF encryption_key IS NULL THEN
        RAISE EXCEPTION 'No se encontró clave de encriptación activa para el hospital %', hospital_id;
    END IF;

    -- Desencriptar
    RETURN convert_from(decrypt(encrypted_data, encryption_key, 'aes-cbc'), 'UTF8');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
COMMIT;

-- ### Función de Auditoría ###
BEGIN;
CREATE OR REPLACE FUNCTION gestion_general.audit_trigger()
RETURNS TRIGGER AS $$
DECLARE
    hospital_id_val BIGINT;
    usuario_id_val BIGINT;
BEGIN
    -- Obtener hospital_id y usuario_id del contexto
    BEGIN
        hospital_id_val := current_setting('app.current_hospital_id')::BIGINT;
        usuario_id_val := current_setting('app.current_user_id')::BIGINT;
    EXCEPTION WHEN OTHERS THEN
        hospital_id_val := NULL;
        usuario_id_val := NULL;
    END;

    -- Insertar registro de auditoría
    INSERT INTO gestion_general.auditoria (
        hospital_id, usuario_id, tabla_afectada, accion,
        estado_anterior, estado_posterior, ip_address
    ) VALUES (
        hospital_id_val,
        usuario_id_val,
        TG_TABLE_NAME,
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE row_to_json(OLD) END,
        CASE WHEN TG_OP = 'INSERT' THEN row_to_json(NEW) ELSE row_to_json(NEW) END,
        inet_client_addr()
    );

    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql;
COMMIT;

-- ### Función para Actualizar Ubicación de Ambulancia ###
BEGIN;
CREATE OR REPLACE FUNCTION ambulancias.actualizar_ubicacion_ambulancia(
    p_ambulancia_id BIGINT,
    p_latitud NUMERIC,
    p_longitud NUMERIC,
    p_precision_metros NUMERIC DEFAULT NULL,
    p_velocidad_kmh NUMERIC DEFAULT NULL,
    p_direccion_grados NUMERIC DEFAULT NULL,
    p_fuente TEXT DEFAULT 'GPS'
)
RETURNS BOOLEAN AS $$
DECLARE
    nueva_ubicacion geometry(Point, 4326);
    servicio_activo_id BIGINT;
BEGIN
    -- Crear punto geométrico
    nueva_ubicacion := ST_SetSRID(ST_MakePoint(p_longitud, p_latitud), 4326);

    -- Actualizar ubicación actual de la ambulancia
    UPDATE ambulancias.ambulancias
    SET ubicacion_actual = nueva_ubicacion,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_ambulancia_id;

    -- Buscar servicio activo
    SELECT id INTO servicio_activo_id
    FROM ambulancias.servicios_ambulancia
    WHERE ambulancia_id = p_ambulancia_id
    AND estado IN ('Asignado', 'En_Ruta_Origen', 'En_Origen', 'En_Ruta_Destino', 'En_Destino')
    ORDER BY fecha_solicitud DESC
    LIMIT 1;

    -- Si hay servicio activo, registrar en rutas GPS
    IF servicio_activo_id IS NOT NULL THEN
        INSERT INTO ambulancias.rutas_gps (
            servicio_ambulancia_id, ambulancia_id, ubicacion,
            precision_metros, velocidad_kmh, direccion_grados, fuente,
            timestamp_gps
        ) VALUES (
            servicio_activo_id, p_ambulancia_id, nueva_ubicacion,
            p_precision_metros, p_velocidad_kmh, p_direccion_grados, p_fuente,
            CURRENT_TIMESTAMP
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
COMMIT;

-- ### Función para Calcular Distancia entre Puntos ###
BEGIN;
CREATE OR REPLACE FUNCTION ambulancias.calcular_distancia_km(
    punto1 geometry(Point, 4326),
    punto2 geometry(Point, 4326)
)
RETURNS NUMERIC AS $$
BEGIN
    -- Calcular distancia en kilómetros usando la función ST_Distance con geografía
    RETURN ST_Distance(punto1::geography, punto2::geography) / 1000.0;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
COMMIT;

-- ############################################################################
-- ### REFERENCIAS DIFERIDAS ###
-- ############################################################################

-- Agregar referencias diferidas que no se pudieron crear antes
BEGIN;
-- Actualizar referencia de creada_por en claves_encriptacion
ALTER TABLE gestion_general.claves_encriptacion
ADD CONSTRAINT fk_claves_encriptacion_creada_por
FOREIGN KEY (creada_por) REFERENCES gestion_general.usuarios(id);

-- Actualizar referencias de factura_id en las tablas clínicas
ALTER TABLE clinico.consultas
ADD CONSTRAINT fk_consultas_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE clinico.cirugias
ADD CONSTRAINT fk_cirugias_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE clinico.hospitalizaciones
ADD CONSTRAINT fk_hospitalizaciones_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE clinico.urgencias
ADD CONSTRAINT fk_urgencias_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE inventario.dispensaciones
ADD CONSTRAINT fk_dispensaciones_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE telemedicina.teleconsultas
ADD CONSTRAINT fk_teleconsultas_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);

ALTER TABLE ambulancias.servicios_ambulancia
ADD CONSTRAINT fk_servicios_ambulancia_factura
FOREIGN KEY (factura_id) REFERENCES financiero.facturas(id);
COMMIT;
-- ############################################################################
-- ### DATOS DE REFERENCIA BÁSICOS ###
-- ############################################################################

-- ### Datos Geográficos Básicos ###
BEGIN;
INSERT INTO gestion_general.pais (id, nombre) VALUES
('COL', 'Colombia') ON CONFLICT (id) DO NOTHING;

INSERT INTO gestion_general.departamento (id, nombre, pais_id) VALUES
('05', 'Antioquia', 'COL'),
('08', 'Atlántico', 'COL'),
('11', 'Bogotá D.C.', 'COL'),
('13', 'Bolívar', 'COL'),
('15', 'Boyacá', 'COL'),
('17', 'Caldas', 'COL'),
('18', 'Caquetá', 'COL'),
('19', 'Cauca', 'COL'),
('20', 'Cesar', 'COL'),
('23', 'Córdoba', 'COL'),
('25', 'Cundinamarca', 'COL'),
('27', 'Chocó', 'COL'),
('41', 'Huila', 'COL'),
('44', 'La Guajira', 'COL'),
('47', 'Magdalena', 'COL'),
('50', 'Meta', 'COL'),
('52', 'Nariño', 'COL'),
('54', 'Norte de Santander', 'COL'),
('63', 'Quindío', 'COL'),
('66', 'Risaralda', 'COL'),
('68', 'Santander', 'COL'),
('70', 'Sucre', 'COL'),
('73', 'Tolima', 'COL'),
('76', 'Valle del Cauca', 'COL'),
('81', 'Arauca', 'COL'),
('85', 'Casanare', 'COL'),
('86', 'Putumayo', 'COL'),
('88', 'Archipiélago de San Andrés', 'COL'),
('91', 'Amazonas', 'COL'),
('94', 'Guainía', 'COL'),
('95', 'Guaviare', 'COL'),
('97', 'Vaupés', 'COL'),
('99', 'Vichada', 'COL')
ON CONFLICT (id) DO NOTHING;

INSERT INTO gestion_general.zona_territorial (id, descripcion) VALUES
('U', 'Urbana'),
('R', 'Rural')
ON CONFLICT (id) DO NOTHING;
COMMIT;

-- ### Tipos de Identificación ###
BEGIN;
INSERT INTO gestion_general.tipos_identificacion (codigo, descripcion) VALUES
('CC', 'Cédula de Ciudadanía'),
('CE', 'Cédula de Extranjería'),
('PA', 'Pasaporte'),
('RC', 'Registro Civil'),
('TI', 'Tarjeta de Identidad'),
('AS', 'Adulto sin Identificación'),
('MS', 'Menor sin Identificación'),
('NU', 'Número Único de Identificación')
ON CONFLICT (codigo) DO NOTHING;
COMMIT;

-- ### Sexos ###
BEGIN;
INSERT INTO gestion_general.sexo (id, descripcion) VALUES
('M', 'Masculino'),
('F', 'Femenino'),
('O', 'Otro')
ON CONFLICT (id) DO NOTHING;
COMMIT;

-- ### Tipos de Documento ###
BEGIN;
INSERT INTO gestion_general.tipo_documento (id, descripcion) VALUES
('CC', 'Cédula de Ciudadanía'),
('CE', 'Cédula de Extranjería'),
('PA', 'Pasaporte'),
('RC', 'Registro Civil'),
('TI', 'Tarjeta de Identidad'),
('AS', 'Adulto sin Identificación'),
('MS', 'Menor sin Identificación'),
('NU', 'Número Único de Identificación')
ON CONFLICT (id) DO NOTHING;
COMMIT;

-- ### Lista Sí/No ###
BEGIN;
INSERT INTO gestion_general.LstSiNo (id, descripcion) VALUES
('01', 'Sí'),
('02', 'No')
ON CONFLICT (id) DO NOTHING;
COMMIT;

-- ############################################################################
-- ### RESUMEN Y FINALIZACIÓN ###
-- ############################################################################

-- Mensaje de finalización exitosa
DO $$
BEGIN
    RAISE NOTICE '
    ############################################################################
    ### SCRIPT DE BASE DE DATOS HIPÓCRATES COMPLETADO EXITOSAMENTE ###
    ############################################################################

    ✅ MÓDULOS IMPLEMENTADOS:

    🏥 GESTIÓN GENERAL:
    - Configuración multi-tenant con RLS
    - Gestión de hospitales y usuarios
    - Encriptación AES-256-CBC para datos sensibles
    - Auditoría completa particionada
    - Internacionalización y configuraciones

    🩺 MÓDULO CLÍNICO:
    - Pacientes particionados por hospital
    - Citas, consentimientos y HCE con FHIR
    - Consultas, cirugías, hospitalizaciones y urgencias
    - Cumplimiento RIPS (Resolución 2275/2023)
    - Infraestructura (camas, quirófanos)

    💊 INVENTARIO Y FARMACIA:
    - Inventario de medicamentos y equipos
    - Dispensaciones con trazabilidad
    - Mantenimiento de equipos médicos

    💰 MÓDULO FINANCIERO:
    - Facturas electrónicas DIAN
    - Detalles de facturación y pagos
    - Integración con servicios clínicos

    🚑 AMBULANCIAS FORTALECIDO:
    - Geolocalización GPS/celular en tiempo real
    - Servicios con rutas completas
    - Seguimiento de ubicación y eventos
    - Dispositivos IoT integrados

    📱 TELEMEDICINA:
    - Teleconsultas con múltiples plataformas
    - Monitoreo remoto de pacientes
    - Cumplimiento Resolución 2654/2019

    🏢 ERP COMPLETO:
    - Recursos Humanos con empleados y nómina
    - Contabilidad con plan de cuentas
    - Presupuestos y control de ejecución
    - Asientos contables automatizados

    🔒 SEGURIDAD Y CUMPLIMIENTO:
    - Row Level Security (RLS) en todas las tablas
    - Encriptación de datos sensibles (Ley 1581/2012)
    - Auditoría completa (Resolución 866/2021)
    - Cumplimiento normativo colombiano

    📊 CARACTERÍSTICAS TÉCNICAS:
    - Particionamiento para escalabilidad
    - Índices espaciales PostGIS
    - Búsqueda full-text en español
    - Funciones de geolocalización
    - Triggers de auditoría automática

    🌟 ARQUITECTURA MEJORADA:
    - Diseño multi-tenant escalable
    - Separación clara de responsabilidades
    - Integridad referencial robusta
    - Optimización de consultas
    - Preparado para alta disponibilidad

    ############################################################################
    ';
END $$;