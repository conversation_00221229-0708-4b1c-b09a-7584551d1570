# Diseño de Lógica de Negocio para Plataforma SaaS Modular de Salud

## Descripción General

La aplicación es una plataforma SaaS modular para IPS (Instituciones Prestadoras de Salud), multitenant, con una lógica de negocio que contempla el manejo de periodos de prueba, pagos, suscripciones, administración de planes y notificaciones. Este documento describe detalladamente dicha lógica.

---

## Objetivos del Módulo de Negocio

- Controlar acceso por periodo de prueba.
- Enviar notificaciones por vencimiento del periodo de prueba.
- Suspender el acceso al vencer la prueba si no se realiza un pago.
- Administrar planes de suscripción.
- Gestionar pagos (múltiples métodos).
- Permitir configuración dinámica de periodos de prueba, precios y planes.
- Monitorear el estado comercial de cada tenant (IPS).

---

## Estructura General del Módulo

### 1. Registro e Inicio del Periodo de Prueba

- Al registrarse una IPS:
  - Se crea un `Tenant` en la base de datos.
  - Se asigna un `start_trial_date = current_date`.
  - Se calcula `end_trial_date = start_trial_date + trial_duration_days` (configurable por el admin).
  - Estado inicial: `trial_active`.

### 2. Notificaciones

- Notificaciones automáticas por correo:
  - 30 días antes del `end_trial_date`.
  - 15 días antes.
  - 7 días antes.
  - 1 día antes.
- Notificaciones configurables en tiempo y contenido (plantillas).

### 3. Suspensión Automática

- Si al llegar `end_trial_date + grace_period_days` no se ha realizado el pago:
  - Estado pasa a `suspended`.
  - Acceso denegado (excepto al módulo de pagos).
  - Se muestra mensaje: "Su periodo de prueba ha expirado. Elija un plan para continuar."

### 4. Activación por Pago

- El sistema reactiva el acceso automáticamente al confirmar el pago de un plan.
- Se actualiza:
  - `subscription_start_date = current_date`.
  - `subscription_end_date` según duración del plan.
  - Estado: `active`.

---

## Componentes del Módulo de Administración de Negocio

### A. Panel de Administración

- Configuración de:
  - Duración del periodo de prueba (días).
  - Periodo de gracia (grace period).
  - Planes disponibles (nombre, precio, duración, descripción, módulos incluidos).
  - Métodos de pago.
  - Plantillas de notificación (correo).
- Visualización:
  - Listado de tenants con estado actual (trial, activo, suspendido, cancelado, pausado).
  - Historial de pagos y actividad de suscripción.
  - Registro de eventos comerciales.

### B. Planes de Suscripción

Ejemplo:

- **Plan Básico**: \$100.000 COP/mes. Incluye Módulo A.
- **Plan Profesional**: \$250.000 COP/mes. Incluye Módulos A, B, C.
- **Plan Premium**: \$400.000 COP/mes. Todos los módulos + soporte prioritario.

### C. Métodos de Pago

- Integración con pasarelas:
  - Tarjeta de crédito (ej: Stripe, ePayco, Wompi).
  - Transferencia bancaria (manual con comprobante).
  - Débito automático.
- Estados de pago:
  - `pending`, `confirmed`, `failed`, `manual_review`.

---

## Modelo de Datos Simplificado (SQL-like)

```sql
TENANTS (
  id UUID PK,
  name TEXT,
  email TEXT,
  start_trial_date DATE,
  end_trial_date DATE,
  grace_period_days INT DEFAULT 0,
  subscription_start_date DATE,
  subscription_end_date DATE,
  status ENUM('trial_active', 'trial_expired', 'active', 'suspended', 'paused', 'cancelled'),
  plan_id UUID FK,
  trial_notifications_sent JSONB
)

PLANS (
  id UUID PK,
  name TEXT,
  price DECIMAL,
  duration_days INT,
  description TEXT
)

PLAN_MODULES (
  id UUID PK,
  plan_id UUID FK,
  module_code TEXT
)

PAYMENTS (
  id UUID PK,
  tenant_id UUID FK,
  amount DECIMAL,
  method TEXT,
  status ENUM('pending', 'confirmed', 'failed', 'manual_review'),
  transaction_reference TEXT,
  invoice_id TEXT,
  created_at TIMESTAMP,
  paid_at TIMESTAMP
)

NOTIFICATIONS_LOG (
  id UUID PK,
  tenant_id UUID FK,
  type TEXT,
  sent_at TIMESTAMP
)

TENANT_EVENTS (
  id UUID PK,
  tenant_id UUID FK,
  event_type TEXT,
  metadata JSONB,
  created_at TIMESTAMP
)

INVOICES (
  id UUID PK,
  tenant_id UUID FK,
  amount DECIMAL,
  status ENUM('generated','sent','paid'),
  sat_compliant BOOLEAN,
  generated_at TIMESTAMP
)
```

---

## Diagramas

### Diagrama Entidad-Relación (ER)

```
TENANTS ───────┐
               │
               ├───< PAYMENTS
               ├───< NOTIFICATIONS_LOG
               ├───< TENANT_EVENTS
               └───< INVOICES
                   
TENANTS >────── PLANS
PLANS ─────<─── PLAN_MODULES
```

### Flujo de Estados del Tenant

```
[Registro] 
   ↓
[Periodo de Prueba: trial_active] 
   ↓
[Notificaciones Programadas] 
   ↓
(no pago) → [Trial Expired] 
             ↓
          [Periodo de Gracia] 
             ↓
        (no pago) → [Suspensión: suspended] 
                       ↓
                    [Pago] → [Activo: active]
```

### Arquitectura de Microservicios

```
+---------------------+
|   API Gateway       |
+---------------------+
         ↓
+---------------------+       +---------------------+
| auth-service        |<----->| tenant-service       |
+---------------------+       +---------------------+
         ↓                           ↓
+---------------------+       +---------------------+
| billing-service     |<----->| notification-service |
+---------------------+       +---------------------+
         ↓
+---------------------+
| analytics-service   |
+---------------------+
```

---

## Cron Jobs / Automatización

- Tareas programadas diarias:
  - Validar expiración de pruebas y gracia.
  - Enviar notificaciones.
  - Cambiar estado a trial\_expired y luego a suspended automáticamente.

---

## Seguridad y Escalabilidad

- Cada tenant con aislamiento de datos (multitenant seguro).
- Roles: admin, gestor financiero, responsable técnico.
- API Gateway para manejo de autenticación y validación de pagos.

---

## Métricas y Monitoreo

- Total de tenants activos.
- Total de tenants suspendidos.
- Tenants en periodo de gracia.
- Ingresos mensuales.
- Porcentaje de conversión de prueba a pago.
- Alertas por vencimiento masivo de pruebas o pagos fallidos.

---

## Escenarios Adicionales

- Renovación automática de plan.
- Cambios de plan (upgrade/downgrade).
- Periodos de gracia (configurables).
- Suspensión temporal voluntaria (pause).
- Cancelación de cuenta.
- Activación de módulos adicionales (add-ons).

---

## Consideraciones Finales

- El módulo debe ser desacoplado y escalable.
- Toda la lógica debe ser testeable con unit y integration tests.
- Soporte multi-idioma en notificaciones.
- Cumplimiento de normativas de datos en salud (ej. Ley 1581, HIPAA si aplica).

---

## Siguientes Pasos (Roadmap)

1. Definir estructura técnica multitenant (con namespaces/límites).
2. Desarrollar el sistema de notificación y cron jobs.
3. Integrar pasarela de pagos.
4. Crear UI del módulo de administración de lógica de negocio.
5. Lanzamiento beta con seguimiento de métricas.
6. Implementar auditoría de eventos comerciales.
7. Soporte para módulos adicionales por plan.
8. Integración con sistemas de facturación electrónica.

---

**Autor**: Arquitectura Técnica Plataforma Salud-SaaS\
**Fecha**: 2025-07-01\
**Versión**: 1.2

