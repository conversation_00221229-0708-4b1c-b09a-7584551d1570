# 🏥 Sistema Hospitalario Hipócrates - Frontend

Sistema integral de gestión hospitalaria desarrollado con tecnologías modernas, diseñado para hospitales de todos los tamaños con funcionalidades completas desde atención básica hasta servicios de alta complejidad.

## 🚀 Estado del Proyecto

**✅ FRONTEND COMPLETADO AL 100%** según auditoría de software

- ✅ **4 Módulos Administrativos Modernizados** (RRHH, Presupuesto, Proveedores, Contabilidad)
- ✅ **Sistema de Diagnósticos CIE-10/CIE-11** con integración API OMS
- ✅ **Sistema de Ambulancias Avanzado** con WebSocket y mapas en tiempo real
- ✅ **Testing Completo** con cobertura 70%+ (Vitest, tests unitarios e integración)
- ✅ **Seguridad Avanzada** (HTTPS, JWT refresh automático, CSP headers)
- ✅ **Optimización Performance** (Bundle analysis, Service Workers, PWA)
- ✅ **Documentación Completa** (APIs, componentes, estándares de código)

## 🛠️ Tecnologías y Arquitectura

### Stack Principal
- **React 18** - Biblioteca de interfaz de usuario con Concurrent Features
- **TypeScript** - Tipado estático para mayor robustez
- **Vite** - Herramienta de construcción ultrarrápida
- **Tailwind CSS** - Framework de CSS utilitario con diseño moderno
- **React Query (TanStack Query)** - Gestión avanzada de estado del servidor
- **React Router v6** - Enrutamiento con lazy loading
- **Zustand** - Gestión de estado global ligera y eficiente

### Funcionalidades Avanzadas
- **PWA (Progressive Web App)** - Funciona offline con Service Workers
- **WebSocket** - Comunicación en tiempo real para ambulancias y notificaciones
- **Mapas Interactivos** - React Leaflet para geolocalización
- **Autenticación JWT** - Con refresh automático y manejo de sesiones
- **Validación Robusta** - Zod schemas con validación client/server
- **Testing Integral** - Vitest + Testing Library + MSW para mocking

### Seguridad y Performance
- **HTTPS** - Certificados SSL para desarrollo y producción
- **CSP Headers** - Content Security Policy para prevenir XSS
- **Bundle Optimization** - Code splitting y lazy loading automático
- **Performance Monitoring** - Métricas en tiempo real y Core Web Vitals
- **Accessibility** - WCAG 2.1 AA compliance

## 🚀 Instalación y Configuración

### Prerrequisitos
- Node.js 18+ 
- npm 9+ o yarn 1.22+

### Instalación Rápida
```bash
# Clonar el repositorio
git clone https://github.com/tu-org/hipocrates-frontend.git
cd hipocrates-frontend

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env

# Generar certificados SSL para desarrollo (opcional)
npm run ssl:generate

# Ejecutar en modo desarrollo
npm run dev

# O con HTTPS
npm run dev:https
```

### Variables de Entorno
```env
# .env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WS_URL=ws://localhost:3001
VITE_CIE11_API_URL=https://id.who.int/icd/release/11/2024-01/mms
VITE_MAPS_API_KEY=tu_api_key_de_mapas
NODE_ENV=development
```

## 📋 Scripts Disponibles

### Desarrollo
```bash
npm run dev              # Servidor de desarrollo (HTTP)
npm run dev:https        # Servidor de desarrollo (HTTPS)
npm run build            # Construcción para producción
npm run preview          # Vista previa de la construcción
```

### Testing
```bash
npm run test             # Ejecutar tests
npm run test:watch       # Tests en modo watch
npm run test:coverage    # Tests con cobertura
npm run test:ci          # Tests para CI/CD
npm run test:integration # Tests de integración
npm run test:unit        # Tests unitarios
```

### Calidad de Código
```bash
npm run lint             # Linter ESLint
npm run lint:fix         # Corregir errores de linting
npm run type-check       # Verificación de tipos TypeScript
npm run format           # Formatear código con Prettier
```

### Seguridad
```bash
npm run security:check   # Auditoría de seguridad
npm run security:fix     # Corregir vulnerabilidades
npm run ssl:generate     # Generar certificados SSL
```

### Performance y Análisis
```bash
npm run analyze:bundle   # Analizar tamaño del bundle
npm run analyze:report   # Generar reporte detallado
npm run analyze:health   # Verificar salud del bundle
npm run performance:monitor # Monitoreo de performance
```

## 🏥 Módulos del Sistema

### 👥 Gestión de Pacientes
- Registro completo de pacientes
- Historial médico integrado
- Búsqueda avanzada y filtros
- Gestión de documentos

### 🩺 Consultas Médicas
- Agenda médica inteligente
- Diagnósticos CIE-10/CIE-11
- Prescripciones electrónicas
- Notas médicas estructuradas

### 🏨 Hospitalizaciones
- Gestión de camas y habitaciones
- Seguimiento de pacientes hospitalizados
- Órdenes médicas
- Evoluciones y notas de enfermería

### 💊 Inventario y Farmacia
- Control de medicamentos
- Gestión de lotes y vencimientos
- Alertas de stock mínimo
- Trazabilidad completa

### 💰 Facturación y Contabilidad
- Facturación electrónica
- Gestión de pagos
- Reportes financieros
- Integración contable

### 👨‍⚕️ Recursos Humanos
- Gestión de empleados
- Control de horarios
- Nómina y beneficios
- Evaluaciones de desempeño

### 🚑 Sistema de Ambulancias
- Seguimiento GPS en tiempo real
- Asignación automática de servicios
- Comunicación con centrales
- Reportes de servicios

### 🔬 Laboratorio
- Gestión de órdenes de laboratorio
- Resultados digitales
- Integración con equipos
- Control de calidad

## 🎨 Características de UI/UX

### Diseño Moderno
- **Glassmorphism** - Efectos de vidrio y transparencias
- **Gradientes Suaves** - Paleta de colores profesional
- **Micro-interacciones** - Animaciones fluidas y naturales
- **Dark/Light Mode** - Soporte para modo oscuro

### Responsividad Total
- **Mobile First** - Optimizado para dispositivos móviles
- **Tablet Friendly** - Interfaz adaptada para tablets
- **Desktop Enhanced** - Aprovecha pantallas grandes
- **PWA Ready** - Funciona como app nativa

### Accesibilidad
- **WCAG 2.1 AA** - Cumple estándares de accesibilidad
- **Navegación por Teclado** - Totalmente navegable sin mouse
- **Screen Readers** - Compatible con lectores de pantalla
- **Alto Contraste** - Opciones para usuarios con discapacidad visual

## 🔒 Seguridad Implementada

### Autenticación y Autorización
- **JWT con Refresh** - Tokens seguros con renovación automática
- **Roles y Permisos** - Control granular de acceso
- **Sesiones Seguras** - Timeout automático por inactividad
- **Multi-factor Auth** - Soporte para 2FA (próximamente)

### Protección de Datos
- **HTTPS Obligatorio** - Comunicación encriptada
- **CSP Headers** - Prevención de ataques XSS
- **Input Sanitization** - Validación y limpieza de datos
- **Audit Logging** - Registro de todas las operaciones

## 📚 Documentación

### Para Desarrolladores
- [📡 API Documentation](./docs/API_DOCUMENTATION.md) - Documentación completa de APIs
- [🧩 Components Guide](./docs/COMPONENTS_GUIDE.md) - Guía de componentes reutilizables
- [📝 Coding Standards](./docs/CODING_STANDARDS.md) - Estándares y mejores prácticas
- [🔒 Security Guide](./docs/SECURITY.md) - Guía de seguridad implementada

## 🤝 Contribución

### Proceso de Desarrollo
1. **Fork** el proyecto
2. **Crear rama** para tu feature (`git checkout -b feature/AmazingFeature`)
3. **Seguir estándares** de código establecidos
4. **Escribir tests** para nueva funcionalidad
5. **Commit** con mensajes descriptivos (`git commit -m 'feat: Add amazing feature'`)
6. **Push** a la rama (`git push origin feature/AmazingFeature`)
7. **Abrir Pull Request** con descripción detallada

### Estándares de Calidad
- ✅ Tests pasando al 100%
- ✅ Cobertura de código mantenida
- ✅ Sin errores de linting
- ✅ Documentación actualizada
- ✅ Performance verificada
- ✅ Accesibilidad validada

## 📞 Soporte y Contacto

### Equipo de Desarrollo
- **Tech Lead**: [<EMAIL>](mailto:<EMAIL>)
- **Frontend Team**: [<EMAIL>](mailto:<EMAIL>)
- **DevOps**: [<EMAIL>](mailto:<EMAIL>)

### Reportar Issues
- **Bugs**: [GitHub Issues](https://github.com/tu-org/hipocrates-frontend/issues)
- **Feature Requests**: [GitHub Discussions](https://github.com/tu-org/hipocrates-frontend/discussions)
- **Security**: [<EMAIL>](mailto:<EMAIL>)

## 📄 Licencia

Este proyecto está bajo la **Licencia MIT** - ver el archivo [LICENSE](LICENSE) para detalles completos.

---

**🏥 Sistema Hipócrates** - Transformando la gestión hospitalaria con tecnología de vanguardia.

*Desarrollado con ❤️ por el equipo de Hipócrates*
