# 📡 Documentación de APIs - Sistema Hipócrates

## Resumen General

El sistema Hipócrates utiliza una arquitectura RESTful con endpoints bien definidos para cada módulo. Todas las APIs requieren autenticación JWT y siguen estándares de respuesta consistentes.

## 🔐 Autenticación

### Base URL
```
Desarrollo: http://localhost:3001/api
Producción: https://api.hipocrates.com/api
```

### Headers Requeridos
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
```

### Endpoints de Autenticación

#### POST /auth/login
Autenticar usuario y obtener tokens JWT.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "contraseña_segura"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "usr-001",
      "email": "<EMAIL>",
      "nombre": "<PERSON>",
      "apellido": "Pérez",
      "rol": "DOCTOR",
      "hospital_id": 1,
      "activo": true
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
      "expires_in": 3600,
      "token_type": "Bearer"
    }
  }
}
```

#### POST /auth/refresh
Renovar access token usando refresh token.

#### POST /auth/logout
Cerrar sesión y revocar tokens.

## 👥 APIs de Pacientes

### GET /pacientes
Obtener lista de pacientes con paginación y filtros.

**Query Parameters:**
- `page`: Número de página (default: 1)
- `limit`: Elementos por página (default: 20)
- `search`: Búsqueda por nombre, documento o email
- `estado`: Filtrar por estado (activo, inactivo)
- `tipo_documento`: Filtrar por tipo de documento

**Response:**
```json
{
  "success": true,
  "data": {
    "pacientes": [
      {
        "id": "pac-001",
        "nombres": "María",
        "apellidos": "González",
        "tipo_documento": "CC",
        "numero_documento": "12345678",
        "fecha_nacimiento": "1985-03-15",
        "telefono": "3001234567",
        "email": "<EMAIL>",
        "direccion": "Calle 123 #45-67",
        "ciudad": "Bogotá",
        "estado": "activo",
        "fecha_registro": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### POST /pacientes
Crear nuevo paciente.

### GET /pacientes/:id
Obtener paciente específico.

### PUT /pacientes/:id
Actualizar paciente.

### DELETE /pacientes/:id
Eliminar paciente (soft delete).

## 🩺 APIs de Consultas

### GET /consultas
Obtener lista de consultas médicas.

### POST /consultas
Crear nueva consulta.

**Request:**
```json
{
  "paciente_id": "pac-001",
  "medico_id": "med-001",
  "fecha_consulta": "2024-01-20T14:30:00Z",
  "motivo_consulta": "Dolor abdominal",
  "diagnosticos": [
    {
      "codigo_cie": "K59.0",
      "descripcion": "Estreñimiento",
      "tipo": "principal"
    }
  ],
  "tratamiento": "Dieta rica en fibra y ejercicio",
  "observaciones": "Paciente refiere mejoría"
}
```

## 🏥 APIs de Hospitalizaciones

### GET /hospitalizaciones
Obtener lista de hospitalizaciones.

### POST /hospitalizaciones
Crear nueva hospitalización.

### GET /hospitalizaciones/:id/camas
Obtener camas disponibles para hospitalización.

## 💊 APIs de Inventario

### GET /inventario
Obtener inventario de medicamentos y suministros.

### POST /inventario/movimientos
Registrar movimiento de inventario.

### GET /inventario/reportes
Generar reportes de inventario.

## 👨‍⚕️ APIs de Recursos Humanos

### GET /empleados
Obtener lista de empleados.

### POST /empleados
Crear nuevo empleado.

### GET /departamentos
Obtener departamentos.

### GET /cargos
Obtener cargos disponibles.

## 💰 APIs de Facturación

### GET /facturas
Obtener facturas.

### POST /facturas
Crear nueva factura.

### GET /facturas/:id/pdf
Generar PDF de factura.

## 🚑 APIs de Ambulancias

### GET /ambulancias
Obtener lista de ambulancias.

### GET /ambulancias/ubicaciones
Obtener ubicaciones en tiempo real.

### POST /ambulancias/:id/servicios
Asignar servicio a ambulancia.

## 🔬 APIs de Diagnósticos CIE

### GET /diagnosticos/cie11/buscar
Buscar diagnósticos CIE-11.

**Query Parameters:**
- `termino`: Término de búsqueda
- `capitulo`: Filtrar por capítulo
- `solo_hojas`: Solo códigos finales (boolean)
- `limite`: Número máximo de resultados

### GET /diagnosticos/cie11/configuracion
Obtener configuración de CIE-11.

### POST /diagnosticos/cie11/sincronizar
Sincronizar con API de la OMS.

## 📊 Estándares de Respuesta

### Respuesta Exitosa
```json
{
  "success": true,
  "data": { /* datos solicitados */ },
  "message": "Operación exitosa",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### Respuesta de Error
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Datos de entrada inválidos",
    "details": {
      "email": ["El email es requerido"],
      "password": ["La contraseña debe tener al menos 8 caracteres"]
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### Códigos de Estado HTTP

- `200 OK`: Operación exitosa
- `201 Created`: Recurso creado exitosamente
- `400 Bad Request`: Datos de entrada inválidos
- `401 Unauthorized`: Token inválido o expirado
- `403 Forbidden`: Sin permisos para la operación
- `404 Not Found`: Recurso no encontrado
- `409 Conflict`: Conflicto con estado actual
- `422 Unprocessable Entity`: Error de validación
- `429 Too Many Requests`: Límite de rate limiting excedido
- `500 Internal Server Error`: Error interno del servidor

## 🔄 Paginación

Todas las APIs que retornan listas implementan paginación:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## 🔍 Filtros y Búsqueda

### Parámetros Comunes
- `search`: Búsqueda de texto libre
- `sort`: Campo para ordenar (ej: `nombre`, `-fecha_creacion`)
- `filter[campo]`: Filtro específico por campo
- `date_from` / `date_to`: Rango de fechas

### Ejemplo
```
GET /pacientes?search=juan&sort=-fecha_registro&filter[estado]=activo&date_from=2024-01-01
```

## 📡 WebSocket APIs

### Conexión
```javascript
const ws = new WebSocket('ws://localhost:3001/ws');
```

### Eventos de Ambulancias
```json
{
  "type": "ambulance_location_update",
  "data": {
    "ambulance_id": "amb-001",
    "latitude": 4.6097,
    "longitude": -74.0817,
    "timestamp": "2024-01-20T10:30:00Z"
  }
}
```

### Eventos de Notificaciones
```json
{
  "type": "notification",
  "data": {
    "id": "not-001",
    "type": "info",
    "title": "Nueva consulta",
    "message": "Se ha programado una nueva consulta",
    "user_id": "usr-001"
  }
}
```

## 🛡️ Seguridad

### Rate Limiting
- **Autenticación**: 5 intentos por minuto
- **APIs generales**: 100 requests por minuto
- **APIs de búsqueda**: 50 requests por minuto

### Validación
- Todos los inputs son validados y sanitizados
- Esquemas Zod para validación client-side
- Validación server-side independiente

### Auditoría
- Todas las operaciones son auditadas
- Logs incluyen usuario, timestamp y detalles de la operación
- Campos sensibles son enmascarados en logs

## 🧪 Testing de APIs

### Postman Collection
Disponible en: `/docs/postman/hipocrates-api.json`

### Ejemplos con cURL
```bash
# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Obtener pacientes
curl -X GET http://localhost:3001/api/pacientes \
  -H "Authorization: Bearer <token>" \
  -H "Accept: application/json"
```

## 📝 Notas de Desarrollo

### Mocking
- Todas las APIs tienen mocks para desarrollo
- Usar `NODE_ENV=development` para activar mocks
- Datos de prueba realistas incluidos

### Versionado
- APIs versionadas usando headers: `API-Version: v1`
- Retrocompatibilidad mantenida por 2 versiones
- Deprecación notificada con 6 meses de anticipación

---

**📞 Soporte**: Para dudas sobre las APIs, contactar al equipo de desarrollo en `<EMAIL>`
