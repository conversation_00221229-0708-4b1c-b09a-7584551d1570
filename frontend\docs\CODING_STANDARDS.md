# 📝 Estándares de Código - Sistema Hipócrates

## Principios Generales

El código del sistema Hipócrates debe ser **legible**, **mantenible**, **testeable** y **escalable**. Seguimos principios SOLID y patrones de diseño establecidos.

## 🎯 Filosofía de Desarrollo

### Clean Code
- **Nombres descriptivos**: Variables y funciones con nombres que expliquen su propósito
- **Funciones pequeñas**: Una función, una responsabilidad
- **Comentarios mínimos**: El código debe ser autoexplicativo
- **DRY (Don't Repeat Yourself)**: Evitar duplicación de código
- **KISS (Keep It Simple, Stupid)**: Soluciones simples y directas

### Principios SOLID
- **S**ingle Responsibility: Una clase/función, una responsabilidad
- **O**pen/Closed: Abierto para extensión, cerrado para modificación
- **L**iskov Substitution: Los subtipos deben ser sustituibles por sus tipos base
- **I**nterface Segregation: Interfaces específicas mejor que una general
- **D**ependency Inversion: Depender de abstracciones, no de concreciones

## 📁 Estructura de Archivos

### Convenciones de Nomenclatura
```
src/
├── components/
│   ├── ui/
│   │   ├── Button/
│   │   │   ├── Button.tsx          # PascalCase para componentes
│   │   │   ├── Button.test.tsx     # Tests con .test.tsx
│   │   │   ├── Button.stories.tsx  # Storybook con .stories.tsx
│   │   │   ├── index.ts           # Exportaciones
│   │   │   └── types.ts           # Tipos específicos
│   │   └── index.ts               # Barrel exports
├── hooks/
│   ├── useAuth.ts                 # camelCase con prefijo 'use'
│   └── useDebounce.ts
├── services/
│   ├── authService.ts             # camelCase con sufijo 'Service'
│   └── apiClient.ts
├── utils/
│   ├── formatters.ts              # camelCase para utilidades
│   └── validators.ts
└── types/
    ├── api.ts                     # Tipos de API
    ├── user.ts                    # Tipos de dominio
    └── common.ts                  # Tipos comunes
```

### Reglas de Importación
```typescript
// 1. Imports de librerías externas
import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';

// 2. Imports internos (ordenados por profundidad)
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { patientService } from '../../services/patientService';

// 3. Imports relativos
import './Component.css';
```

## 🔤 Convenciones de Nomenclatura

### Variables y Funciones
```typescript
// ✅ Correcto - camelCase descriptivo
const patientCount = 150;
const isLoading = false;
const handleSubmit = () => {};
const fetchPatientData = async () => {};

// ❌ Incorrecto
const pc = 150;
const loading = false;
const submit = () => {};
const getData = async () => {};
```

### Constantes
```typescript
// ✅ Correcto - SCREAMING_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.hipocrates.com';
const DEFAULT_PAGE_SIZE = 20;

// ❌ Incorrecto
const maxRetryAttempts = 3;
const apiBaseUrl = 'https://api.hipocrates.com';
```

### Tipos e Interfaces
```typescript
// ✅ Correcto - PascalCase
interface Patient {
  id: string;
  name: string;
  email: string;
}

type PatientStatus = 'active' | 'inactive' | 'pending';

// ❌ Incorrecto
interface patient {
  id: string;
  name: string;
}

type patientStatus = 'active' | 'inactive';
```

### Componentes
```typescript
// ✅ Correcto - PascalCase
const PatientCard: React.FC<PatientCardProps> = ({ patient }) => {
  return <div>{patient.name}</div>;
};

// ❌ Incorrecto
const patientCard = ({ patient }) => {
  return <div>{patient.name}</div>;
};
```

## 🏗️ Arquitectura de Componentes

### Estructura de Componente
```typescript
// PatientCard.tsx
import React from 'react';
import { Card, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Patient } from '../../types/patient';

// 1. Definir interfaces
interface PatientCardProps {
  patient: Patient;
  onEdit?: (patient: Patient) => void;
  onDelete?: (patientId: string) => void;
  showActions?: boolean;
}

// 2. Componente principal
export const PatientCard: React.FC<PatientCardProps> = ({
  patient,
  onEdit,
  onDelete,
  showActions = true
}) => {
  // 3. Hooks y estado local
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 4. Funciones auxiliares
  const handleEdit = () => {
    onEdit?.(patient);
  };
  
  const handleDelete = () => {
    if (window.confirm('¿Está seguro de eliminar este paciente?')) {
      onDelete?.(patient.id);
    }
  };
  
  // 5. Render
  return (
    <Card className="patient-card">
      <CardContent>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold">{patient.name}</h3>
            <p className="text-gray-600">{patient.email}</p>
            <Badge variant={patient.status === 'active' ? 'success' : 'warning'}>
              {patient.status}
            </Badge>
          </div>
          
          {showActions && (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleEdit}>
                Editar
              </Button>
              <Button variant="outline" size="sm" onClick={handleDelete}>
                Eliminar
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// 6. Export por defecto
export default PatientCard;
```

## 🎣 Custom Hooks

### Estructura de Hook
```typescript
// usePatients.ts
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { patientService } from '../services/patientService';
import { Patient, CreatePatientData } from '../types/patient';

interface UsePatients {
  patients: Patient[];
  isLoading: boolean;
  error: Error | null;
  createPatient: (data: CreatePatientData) => Promise<void>;
  updatePatient: (id: string, data: Partial<Patient>) => Promise<void>;
  deletePatient: (id: string) => Promise<void>;
  refetch: () => void;
}

export const usePatients = (): UsePatients => {
  const queryClient = useQueryClient();
  
  // Query para obtener pacientes
  const {
    data: patients = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['patients'],
    queryFn: patientService.getAll
  });
  
  // Mutation para crear paciente
  const createMutation = useMutation({
    mutationFn: patientService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    }
  });
  
  // Mutation para actualizar paciente
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Patient> }) =>
      patientService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    }
  });
  
  // Mutation para eliminar paciente
  const deleteMutation = useMutation({
    mutationFn: patientService.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    }
  });
  
  return {
    patients,
    isLoading,
    error,
    createPatient: createMutation.mutateAsync,
    updatePatient: (id, data) => updateMutation.mutateAsync({ id, data }),
    deletePatient: deleteMutation.mutateAsync,
    refetch
  };
};
```

## 🔧 Servicios y APIs

### Estructura de Servicio
```typescript
// patientService.ts
import { apiClient } from '../utils/apiClient';
import { Patient, CreatePatientData, UpdatePatientData } from '../types/patient';

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface PatientFilters {
  search?: string;
  status?: string;
  page?: number;
  limit?: number;
}

class PatientService {
  private readonly baseUrl = '/patients';
  
  async getAll(filters: PatientFilters = {}): Promise<Patient[]> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
    
    const response = await apiClient.get<PaginatedResponse<Patient>>(
      `${this.baseUrl}?${params}`
    );
    
    return response.data.data;
  }
  
  async getById(id: string): Promise<Patient> {
    const response = await apiClient.get<{ data: Patient }>(`${this.baseUrl}/${id}`);
    return response.data.data;
  }
  
  async create(data: CreatePatientData): Promise<Patient> {
    const response = await apiClient.post<{ data: Patient }>(this.baseUrl, data);
    return response.data.data;
  }
  
  async update(id: string, data: UpdatePatientData): Promise<Patient> {
    const response = await apiClient.put<{ data: Patient }>(`${this.baseUrl}/${id}`, data);
    return response.data.data;
  }
  
  async delete(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}`);
  }
}

export const patientService = new PatientService();
```

## 📊 Manejo de Estado

### Zustand Store
```typescript
// patientStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Patient } from '../types/patient';

interface PatientState {
  patients: Patient[];
  selectedPatient: Patient | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setPatients: (patients: Patient[]) => void;
  setSelectedPatient: (patient: Patient | null) => void;
  addPatient: (patient: Patient) => void;
  updatePatient: (id: string, updates: Partial<Patient>) => void;
  removePatient: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const usePatientStore = create<PatientState>()(
  devtools(
    (set, get) => ({
      patients: [],
      selectedPatient: null,
      isLoading: false,
      error: null,
      
      setPatients: (patients) => set({ patients }),
      
      setSelectedPatient: (patient) => set({ selectedPatient: patient }),
      
      addPatient: (patient) => set((state) => ({
        patients: [...state.patients, patient]
      })),
      
      updatePatient: (id, updates) => set((state) => ({
        patients: state.patients.map(p => 
          p.id === id ? { ...p, ...updates } : p
        )
      })),
      
      removePatient: (id) => set((state) => ({
        patients: state.patients.filter(p => p.id !== id),
        selectedPatient: state.selectedPatient?.id === id ? null : state.selectedPatient
      })),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setError: (error) => set({ error }),
      
      clearError: () => set({ error: null })
    }),
    { name: 'patient-store' }
  )
);
```

## 🧪 Testing

### Estructura de Test
```typescript
// PatientCard.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PatientCard } from './PatientCard';
import { Patient } from '../../types/patient';

// Mock data
const mockPatient: Patient = {
  id: 'patient-1',
  name: 'Juan Pérez',
  email: '<EMAIL>',
  status: 'active',
  createdAt: '2024-01-01T00:00:00Z'
};

// Helper para renderizar con providers
const renderWithProviders = (ui: React.ReactElement) => {
  return render(ui);
};

describe('PatientCard', () => {
  it('should render patient information correctly', () => {
    renderWithProviders(<PatientCard patient={mockPatient} />);
    
    expect(screen.getByText('Juan Pérez')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('active')).toBeInTheDocument();
  });
  
  it('should call onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    const onEdit = jest.fn();
    
    renderWithProviders(
      <PatientCard patient={mockPatient} onEdit={onEdit} />
    );
    
    const editButton = screen.getByText('Editar');
    await user.click(editButton);
    
    expect(onEdit).toHaveBeenCalledWith(mockPatient);
  });
  
  it('should not show actions when showActions is false', () => {
    renderWithProviders(
      <PatientCard patient={mockPatient} showActions={false} />
    );
    
    expect(screen.queryByText('Editar')).not.toBeInTheDocument();
    expect(screen.queryByText('Eliminar')).not.toBeInTheDocument();
  });
});
```

## 🎨 Estilos y CSS

### Tailwind CSS
```typescript
// ✅ Correcto - Clases organizadas y legibles
<div className={`
  flex items-center justify-between
  p-4 bg-white rounded-lg shadow-sm
  border border-gray-200
  hover:shadow-md transition-shadow
`}>
  <span className="text-lg font-semibold text-gray-900">
    {title}
  </span>
</div>

// ✅ Usando clsx para condicionales
import clsx from 'clsx';

<button className={clsx(
  'px-4 py-2 rounded-md font-medium transition-colors',
  {
    'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
    'bg-gray-200 text-gray-900 hover:bg-gray-300': variant === 'secondary',
    'opacity-50 cursor-not-allowed': disabled
  }
)}>
  {children}
</button>
```

## 🔍 Validación

### Zod Schemas
```typescript
// schemas/patient.ts
import { z } from 'zod';

export const createPatientSchema = z.object({
  name: z.string()
    .min(2, 'El nombre debe tener al menos 2 caracteres')
    .max(100, 'El nombre no puede exceder 100 caracteres'),
  
  email: z.string()
    .email('Email inválido')
    .optional(),
  
  phone: z.string()
    .regex(/^\+?[\d\s-()]+$/, 'Teléfono inválido')
    .optional(),
  
  birthDate: z.string()
    .datetime('Fecha inválida')
    .refine(
      (date) => new Date(date) <= new Date(),
      'La fecha de nacimiento no puede ser futura'
    ),
  
  documentType: z.enum(['CC', 'TI', 'CE', 'PP']),
  
  documentNumber: z.string()
    .min(6, 'Número de documento muy corto')
    .max(20, 'Número de documento muy largo')
});

export type CreatePatientData = z.infer<typeof createPatientSchema>;
```

## 📋 Checklist de Calidad

### Antes de Commit
- [ ] Código formateado con Prettier
- [ ] Sin errores de ESLint
- [ ] Tests pasando
- [ ] Tipos TypeScript correctos
- [ ] Nombres descriptivos
- [ ] Funciones pequeñas y enfocadas
- [ ] Comentarios solo donde sea necesario
- [ ] Imports organizados
- [ ] Sin console.log en producción

### Antes de PR
- [ ] Funcionalidad completamente implementada
- [ ] Tests de cobertura adecuada
- [ ] Documentación actualizada
- [ ] Performance verificada
- [ ] Accesibilidad validada
- [ ] Responsive design verificado
- [ ] Manejo de errores implementado

---

**🎯 Objetivo**: Mantener un código base consistente, legible y mantenible que facilite la colaboración y el crecimiento del proyecto.
