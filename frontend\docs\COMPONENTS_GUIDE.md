# 🧩 Guía de Componentes - Sistema Hipócrates

## Arquitectura de Componentes

El sistema Hipócrates utiliza una arquitectura de componentes modular basada en React con TypeScript, siguiendo principios de diseño atómico y reutilización.

## 📁 Estructura de Componentes

```
src/
├── components/           # Componentes reutilizables
│   ├── ui/              # Componentes base de UI
│   ├── layout/          # Componentes de layout
│   ├── forms/           # Componentes de formularios
│   ├── charts/          # Componentes de gráficos
│   ├── security/        # Componentes de seguridad
│   └── notifications/   # Componentes de notificaciones
├── modules/             # Módulos específicos del dominio
│   ├── pacientes/       # Módulo de pacientes
│   ├── consultas/       # Módulo de consultas
│   ├── inventario/      # Módulo de inventario
│   └── ...
└── hooks/               # Custom hooks reutilizables
```

## 🎨 Componentes UI Base

### Button
Componente de botón reutilizable con múltiples variantes.

```tsx
import { Button } from '../components/ui/Button';

// Uso básico
<Button>Guardar</Button>

// Con variantes
<Button variant="primary">Primario</Button>
<Button variant="secondary">Secundario</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>

// Con tamaños
<Button size="sm">Pequeño</Button>
<Button size="md">Mediano</Button>
<Button size="lg">Grande</Button>

// Con iconos
<Button>
  <Save className="w-4 h-4 mr-2" />
  Guardar
</Button>

// Estados
<Button disabled>Deshabilitado</Button>
<Button loading>Cargando...</Button>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost'
- `size`: 'sm' | 'md' | 'lg'
- `disabled`: boolean
- `loading`: boolean
- `onClick`: () => void
- `children`: ReactNode

### Card
Componente de tarjeta para agrupar contenido.

```tsx
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';

<Card>
  <CardHeader>
    <CardTitle>Título de la Tarjeta</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Contenido de la tarjeta</p>
  </CardContent>
</Card>
```

### Badge
Componente para mostrar etiquetas y estados.

```tsx
import { Badge } from '../components/ui/Badge';

<Badge variant="success">Activo</Badge>
<Badge variant="warning">Pendiente</Badge>
<Badge variant="error">Error</Badge>
<Badge variant="info">Información</Badge>
```

### Input
Componente de entrada de texto con validación.

```tsx
import { Input } from '../components/ui/Input';

<Input
  label="Nombre completo"
  placeholder="Ingrese el nombre"
  value={value}
  onChange={setValue}
  error={error}
  required
/>
```

### Select
Componente de selección con opciones.

```tsx
import { Select } from '../components/ui/Select';

<Select
  label="Estado"
  options={[
    { value: 'activo', label: 'Activo' },
    { value: 'inactivo', label: 'Inactivo' }
  ]}
  value={selectedValue}
  onChange={setSelectedValue}
/>
```

## 🏗️ Componentes de Layout

### Layout
Componente principal de layout con sidebar y header.

```tsx
import { Layout } from '../components/layout/Layout';

<Layout>
  <div>Contenido de la página</div>
</Layout>
```

### Sidebar
Barra lateral de navegación.

```tsx
import { Sidebar } from '../components/layout/Sidebar';

<Sidebar
  isOpen={sidebarOpen}
  onToggle={setSidebarOpen}
  menuItems={menuItems}
/>
```

### Header
Encabezado con navegación y perfil de usuario.

```tsx
import { Header } from '../components/layout/Header';

<Header
  user={currentUser}
  onLogout={handleLogout}
  notifications={notifications}
/>
```

## 📋 Componentes de Formularios

### FormField
Wrapper para campos de formulario con validación.

```tsx
import { FormField } from '../components/forms/FormField';

<FormField
  label="Email"
  error={errors.email}
  required
>
  <Input
    type="email"
    value={email}
    onChange={setEmail}
  />
</FormField>
```

### SearchInput
Campo de búsqueda con debounce automático.

```tsx
import { SearchInput } from '../components/forms/SearchInput';

<SearchInput
  placeholder="Buscar pacientes..."
  onSearch={handleSearch}
  debounceMs={300}
/>
```

### DatePicker
Selector de fechas.

```tsx
import { DatePicker } from '../components/forms/DatePicker';

<DatePicker
  label="Fecha de nacimiento"
  value={birthDate}
  onChange={setBirthDate}
  maxDate={new Date()}
/>
```

## 📊 Componentes de Datos

### DataTable
Tabla de datos con paginación, ordenamiento y filtros.

```tsx
import { DataTable } from '../components/ui/DataTable';

<DataTable
  columns={[
    { key: 'nombre', label: 'Nombre', sortable: true },
    { key: 'email', label: 'Email' },
    { key: 'estado', label: 'Estado', render: (value) => <Badge>{value}</Badge> }
  ]}
  data={patients}
  pagination={{
    page: currentPage,
    limit: pageSize,
    total: totalItems,
    onPageChange: setCurrentPage
  }}
  onSort={handleSort}
  loading={isLoading}
/>
```

### StatsCard
Tarjeta para mostrar estadísticas.

```tsx
import { StatsCard } from '../components/ui/StatsCard';

<StatsCard
  title="Total Pacientes"
  value={1250}
  change={+12}
  changeType="increase"
  icon={<Users className="w-6 h-6" />}
  color="blue"
/>
```

## 🔔 Componentes de Notificaciones

### Toast
Sistema de notificaciones toast.

```tsx
import toast from 'react-hot-toast';

// Éxito
toast.success('Paciente guardado exitosamente');

// Error
toast.error('Error al guardar paciente');

// Información
toast('Información importante');

// Personalizado
toast.custom((t) => (
  <div className="bg-white p-4 rounded-lg shadow-lg">
    <p>Notificación personalizada</p>
  </div>
));
```

### Alert
Componente de alerta para mensajes importantes.

```tsx
import { Alert, AlertDescription } from '../components/ui/Alert';

<Alert variant="warning">
  <AlertTriangle className="w-4 h-4" />
  <AlertDescription>
    Esta acción no se puede deshacer.
  </AlertDescription>
</Alert>
```

## 🔒 Componentes de Seguridad

### ProtectedRoute
Componente para proteger rutas que requieren autenticación.

```tsx
import { ProtectedRoute } from '../routes/ProtectedRoute';

<ProtectedRoute requiredRole="DOCTOR">
  <ConsultasPage />
</ProtectedRoute>
```

### SecurityNotifications
Notificaciones de estado de seguridad.

```tsx
import SecurityNotifications from '../components/security/SecurityNotifications';

// Se incluye automáticamente en el Layout
<Layout>
  <SecurityNotifications />
  {/* Contenido */}
</Layout>
```

## 🎯 Componentes Específicos del Dominio

### PatientCard
Tarjeta para mostrar información de paciente.

```tsx
import { PatientCard } from '../modules/pacientes/components/PatientCard';

<PatientCard
  patient={patient}
  onEdit={handleEdit}
  onView={handleView}
  showActions={true}
/>
```

### DiagnosisSearch
Buscador de diagnósticos CIE.

```tsx
import { DiagnosisSearch } from '../modules/diagnosticos-cie/components/DiagnosisSearch';

<DiagnosisSearch
  onSelect={handleDiagnosisSelect}
  placeholder="Buscar diagnóstico..."
  type="CIE11"
/>
```

### AmbulanceMap
Mapa de ambulancias en tiempo real.

```tsx
import AmbulanceMap from '../modules/ambulancias/AmbulanceMap';

<AmbulanceMap
  ambulances={ambulances}
  onAmbulanceSelect={handleSelect}
  center={[4.6097, -74.0817]}
  zoom={12}
/>
```

## 🎨 Sistema de Diseño

### Colores
```css
/* Colores principales */
--primary: #0059B2;
--primary-dark: #004494;
--primary-light: #3374C2;

/* Colores de estado */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #3B82F6;

/* Colores neutros */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-900: #111827;
```

### Tipografía
```css
/* Familias de fuente */
--font-sans: 'Inter', sans-serif;
--font-mono: 'JetBrains Mono', monospace;

/* Tamaños */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
```

### Espaciado
```css
/* Sistema de espaciado (múltiplos de 4px) */
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-4: 1rem;     /* 16px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
```

## 🔧 Custom Hooks

### useAuth
Hook para manejo de autenticación.

```tsx
import { useAuth } from '../hooks/useAuth';

const { user, isAuthenticated, login, logout } = useAuth();
```

### useDebounce
Hook para debounce de valores.

```tsx
import { useDebounce } from '../hooks/useDebounce';

const debouncedSearchTerm = useDebounce(searchTerm, 300);
```

### useDiagnosis
Hook para búsqueda de diagnósticos.

```tsx
import { useDiagnosis } from '../hooks/useDiagnosis';

const { searchDiagnosis, isLoading, results } = useDiagnosis();
```

### useServiceWorker
Hook para manejo de Service Worker.

```tsx
import { useServiceWorker } from '../hooks/useServiceWorker';

const { isRegistered, isUpdateAvailable, skipWaiting } = useServiceWorker();
```

## 📱 Responsividad

### Breakpoints
```css
/* Breakpoints del sistema */
sm: 640px
md: 768px
lg: 1024px
xl: 1280px
2xl: 1536px
```

### Clases Utilitarias
```tsx
// Responsive design con Tailwind
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Contenido */}
</div>

// Ocultar en móvil
<div className="hidden md:block">
  {/* Solo visible en desktop */}
</div>

// Mostrar solo en móvil
<div className="block md:hidden">
  {/* Solo visible en móvil */}
</div>
```

## ♿ Accesibilidad

### Principios WCAG 2.1 AA
- **Perceptible**: Contraste mínimo 4.5:1
- **Operable**: Navegación por teclado completa
- **Comprensible**: Etiquetas claras y mensajes de error
- **Robusto**: Compatible con lectores de pantalla

### Implementación
```tsx
// Etiquetas semánticas
<button aria-label="Cerrar modal" onClick={onClose}>
  <X className="w-4 h-4" />
</button>

// Estados de carga
<button disabled={isLoading} aria-busy={isLoading}>
  {isLoading ? 'Guardando...' : 'Guardar'}
</button>

// Navegación por teclado
<div
  role="button"
  tabIndex={0}
  onKeyDown={(e) => e.key === 'Enter' && onClick()}
  onClick={onClick}
>
  Elemento clickeable
</div>
```

## 🧪 Testing de Componentes

### Testing Library
```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

test('renders button with text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByText('Click me')).toBeInTheDocument();
});

test('calls onClick when clicked', () => {
  const handleClick = jest.fn();
  render(<Button onClick={handleClick}>Click me</Button>);
  
  fireEvent.click(screen.getByText('Click me'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

### Storybook (Recomendado)
```tsx
// Button.stories.tsx
export default {
  title: 'UI/Button',
  component: Button,
};

export const Primary = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
};

export const WithIcon = {
  args: {
    children: (
      <>
        <Save className="w-4 h-4 mr-2" />
        Save
      </>
    ),
  },
};
```

## 📋 Mejores Prácticas

### Nomenclatura
- **Componentes**: PascalCase (`PatientCard`)
- **Props**: camelCase (`isLoading`)
- **Archivos**: PascalCase para componentes (`Button.tsx`)
- **Hooks**: camelCase con prefijo `use` (`useAuth`)

### Estructura de Archivos
```
Button/
├── Button.tsx          # Componente principal
├── Button.test.tsx     # Tests
├── Button.stories.tsx  # Storybook stories
├── index.ts           # Exportaciones
└── types.ts           # Tipos específicos
```

### Performance
- Usar `React.memo` para componentes que reciben props complejas
- Implementar `useMemo` y `useCallback` cuando sea necesario
- Lazy loading para componentes pesados
- Optimizar re-renders con keys apropiadas

---

**📚 Recursos Adicionales**:
- [Storybook](http://localhost:6006) - Documentación interactiva de componentes
- [Figma Design System](https://figma.com/hipocrates) - Diseños y especificaciones
- [Component Library](./COMPONENT_LIBRARY.md) - Catálogo completo de componentes
