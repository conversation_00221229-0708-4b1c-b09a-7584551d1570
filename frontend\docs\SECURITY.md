# 🔒 Guía de Seguridad - Sistema Hipócrates

## Resumen de Seguridad Implementada

El sistema Hipócrates implementa múltiples capas de seguridad para proteger los datos médicos sensibles y cumplir con las regulaciones de salud.

## 🛡️ Medidas de Seguridad Implementadas

### 1. Autenticación y Autorización

#### JWT con Refresh Automático
- **Access Tokens**: Corta duración (15 min en producción, 1 hora en desarrollo)
- **Refresh Tokens**: Mayor duración (1 día en producción, 7 días en desarrollo)
- **Refresh Automático**: Se renueva automáticamente 5 minutos antes de expirar
- **Interceptores Axios**: Manejo automático de tokens expirados

#### Gestión de Sesiones
- **Timeout de Inactividad**: 30 minutos en producción, 60 en desarrollo
- **Sesiones Concurrentes**: Máximo 2 en producción, 5 en desarrollo
- **Logout Automático**: Por inactividad o token inválido

### 2. Comunicación Segura

#### HTTPS
- **Desarrollo**: Certificados autofirmados generados automáticamente
- **Producción**: HTTPS obligatorio con HSTS habilitado
- **Scripts**: `npm run ssl:generate` para generar certificados locales

#### Content Security Policy (CSP)
```
default-src 'self';
script-src 'self' [unsafe-inline en desarrollo];
style-src 'self' 'unsafe-inline';
img-src 'self' data: https:;
connect-src 'self' wss: https://icd.who.int;
```

### 3. Headers de Seguridad

- **X-Frame-Options**: DENY (previene clickjacking)
- **X-Content-Type-Options**: nosniff (previene MIME sniffing)
- **Referrer-Policy**: strict-origin-when-cross-origin
- **Permissions-Policy**: Restringe acceso a APIs sensibles

### 4. Validación y Sanitización

#### Validación Client-Side
- **Zod**: Esquemas de validación TypeScript
- **Sanitización**: Limpieza de inputs antes del envío
- **Rate Limiting**: Protección contra ataques de fuerza bruta

#### Políticas de Contraseñas
- **Desarrollo**: Mínimo 6 caracteres (relajado para testing)
- **Producción**: 
  - Mínimo 12 caracteres
  - Mayúsculas, minúsculas, números y caracteres especiales
  - Cambio obligatorio cada 90 días
  - No reutilización de últimas 12 contraseñas

## 🔧 Configuración por Entorno

### Desarrollo
```typescript
{
  https: { required: false },
  jwt: { accessTokenExpiry: 3600 }, // 1 hora
  session: { timeout: 60 }, // 1 hora
  password: { minLength: 6 }, // Relajado
  audit: { logLevel: 'debug' }
}
```

### Producción
```typescript
{
  https: { required: true, hsts: true },
  jwt: { accessTokenExpiry: 900 }, // 15 minutos
  session: { timeout: 30 }, // 30 minutos
  password: { minLength: 12 }, // Estricto
  audit: { logLevel: 'info' }
}
```

## 🚀 Comandos de Seguridad

### Generar Certificados SSL
```bash
npm run ssl:generate
```

### Ejecutar con HTTPS
```bash
npm run dev:https
```

### Auditoría de Seguridad
```bash
npm run security:check
npm run security:fix
```

### Testing de Seguridad
```bash
npm run test:security
```

## 📊 Monitoreo de Seguridad

### Componente SecurityNotifications
- **Indicador de Nivel**: Alto/Medio/Bajo
- **Estado HTTPS**: Verificación en tiempo real
- **Token Status**: Validez y expiración
- **Actividad**: Monitoreo de última actividad
- **Alertas**: Notificaciones automáticas

### Métricas Monitoreadas
- Estado de conexión HTTPS
- Validez de tokens JWT
- Tiempo hasta expiración
- Última actividad del usuario
- Intentos de login fallidos

## 🔍 Auditoría y Logging

### Eventos Auditados
- Login/Logout de usuarios
- Cambios de contraseña
- Acceso a datos sensibles
- Errores de autenticación
- Intentos de acceso no autorizado

### Campos Sensibles Protegidos
- Contraseñas
- Tokens de autenticación
- Números de seguridad social
- Información de tarjetas de crédito
- Registros médicos

## ⚠️ Consideraciones de Seguridad

### Para Desarrollo
1. **Certificados Autofirmados**: Solo para desarrollo local
2. **CSP Relajado**: Permite `unsafe-inline` para desarrollo
3. **Logging Detallado**: Nivel debug habilitado
4. **Contraseñas Simples**: Política relajada para testing

### Para Producción
1. **HTTPS Obligatorio**: Sin excepciones
2. **CSP Estricto**: Sin `unsafe-inline` o `unsafe-eval`
3. **Tokens Cortos**: Access tokens de 15 minutos
4. **Auditoría Completa**: Logging de todos los eventos de seguridad

## 🛠️ Implementación

### Integrar Autenticación Avanzada
```typescript
import { useAuthAdvanced } from './hooks/useAuthAdvanced';

function App() {
  const { user, isAuthenticated, login, logout } = useAuthAdvanced();
  // ...
}
```

### Agregar Notificaciones de Seguridad
```typescript
import SecurityNotifications from './components/security/SecurityNotifications';

function Layout() {
  return (
    <div>
      {/* Tu contenido */}
      <SecurityNotifications />
    </div>
  );
}
```

### Configurar Seguridad
```typescript
import { securityConfig } from './config/security';

// La configuración se aplica automáticamente según NODE_ENV
console.log('Security level:', securityConfig.https.required ? 'High' : 'Medium');
```

## 📋 Checklist de Seguridad

### Desarrollo
- [ ] Certificados SSL generados
- [ ] HTTPS configurado (opcional)
- [ ] CSP headers configurados
- [ ] Autenticación JWT implementada
- [ ] Validación client-side activa
- [ ] Logging de seguridad habilitado

### Producción
- [ ] HTTPS obligatorio configurado
- [ ] HSTS habilitado
- [ ] CSP estricto implementado
- [ ] Tokens JWT con duración corta
- [ ] Políticas de contraseña estrictas
- [ ] Auditoría completa habilitada
- [ ] Rate limiting configurado
- [ ] Monitoreo de seguridad activo

## 🆘 Respuesta a Incidentes

### Detección de Amenazas
1. **Múltiples intentos fallidos**: Bloqueo temporal
2. **Token comprometido**: Revocación inmediata
3. **Actividad sospechosa**: Logout forzado
4. **Vulnerabilidad detectada**: Parche inmediato

### Procedimientos de Emergencia
1. **Revocar todos los tokens**
2. **Forzar re-autenticación**
3. **Revisar logs de auditoría**
4. **Notificar a administradores**
5. **Aplicar parches de seguridad**

## 📞 Contacto de Seguridad

Para reportar vulnerabilidades de seguridad:
- **Email**: <EMAIL>
- **Proceso**: Divulgación responsable
- **Tiempo de respuesta**: 24-48 horas

---

**⚠️ IMPORTANTE**: Esta documentación debe mantenerse actualizada con cualquier cambio en las medidas de seguridad implementadas.
