{"name": "hipocrates-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --reporter=verbose", "test:integration": "vitest run --testNamePattern=integration", "test:unit": "vitest run --testNamePattern=unit", "test:debug": "vitest --inspect-brk --no-coverage", "test:ui": "vitest --ui", "ssl:generate": "node scripts/generate-ssl-cert.js", "dev:https": "npm run ssl:generate && vite --host", "security:check": "npm audit && npm run lint", "security:fix": "npm audit fix", "analyze:bundle": "npm run build && node scripts/analyze-bundle.js analyze", "analyze:report": "npm run build && node scripts/analyze-bundle.js report", "analyze:health": "npm run build && node scripts/analyze-bundle.js health", "performance:monitor": "node -e \"console.log('Performance monitoring habilitado en desarrollo')\"", "pwa:validate": "npm run build && npx pwa-asset-generator --help || echo 'Instalar pwa-asset-generator para validar PWA'"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.2", "@hookform/resolvers": "^3.3.4", "@mui/material": "^7.1.1", "@tanstack/react-query": "^5.17.19", "@types/leaflet": "^1.9.17", "@types/react-datepicker": "^6.2.0", "@types/recharts": "^1.8.29", "axios": "^1.6.5", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "msw": "^2.0.13", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.5.2", "react-leaflet": "^4.2.1", "react-router-dom": "^6.21.3", "react-select": "^5.10.1", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^22.15.3", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "msw": "^2.0.13", "postcss": "^8.4.33", "prettier": "^3.2.4", "ts-jest": "^29.1.2", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.2.1"}, "msw": {"workerDirectory": ["public"]}}