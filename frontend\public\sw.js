// Service Worker para Sistema Hipócrates
// Versión del cache - incrementar cuando se actualice
const CACHE_VERSION = 'hipocrates-v1.0.0';
const STATIC_CACHE = `${CACHE_VERSION}-static`;
const DYNAMIC_CACHE = `${CACHE_VERSION}-dynamic`;
const API_CACHE = `${CACHE_VERSION}-api`;

// Archivos estáticos para cachear
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  // Agregar otros assets estáticos críticos
];

// URLs de API que se pueden cachear
const CACHEABLE_API_PATTERNS = [
  /\/api\/diagnosticos\/cie11/,
  /\/api\/configuraciones/,
  /\/api\/estadisticas/,
  // Agregar otros endpoints que se puedan cachear
];

// URLs que nunca se deben cachear
const NEVER_CACHE_PATTERNS = [
  /\/api\/auth/,
  /\/api\/websocket/,
  /\/api\/upload/,
  // Endpoints sensibles o en tiempo real
];

// Estrategias de cache
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
};

// Configuración de timeouts
const NETWORK_TIMEOUT = 5000; // 5 segundos
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 horas

// Instalación del Service Worker
self.addEventListener('install', (event) => {
  console.log('[SW] Instalando Service Worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Cacheando archivos estáticos');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Service Worker instalado correctamente');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Error instalando Service Worker:', error);
      })
  );
});

// Activación del Service Worker
self.addEventListener('activate', (event) => {
  console.log('[SW] Activando Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // Eliminar caches antiguos
            if (cacheName.includes('hipocrates') && !cacheName.includes(CACHE_VERSION)) {
              console.log('[SW] Eliminando cache antiguo:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service Worker activado');
        return self.clients.claim();
      })
  );
});

// Interceptar requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Solo manejar requests HTTP/HTTPS
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // Determinar estrategia de cache
  const strategy = getCacheStrategy(request);
  
  if (strategy === CACHE_STRATEGIES.NETWORK_ONLY) {
    return; // No cachear
  }
  
  event.respondWith(handleRequest(request, strategy));
});

// Determinar estrategia de cache según el request
function getCacheStrategy(request) {
  const url = new URL(request.url);
  
  // Nunca cachear ciertos endpoints
  if (NEVER_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return CACHE_STRATEGIES.NETWORK_ONLY;
  }
  
  // Archivos estáticos - Cache First
  if (request.destination === 'script' || 
      request.destination === 'style' || 
      request.destination === 'image' ||
      request.destination === 'font') {
    return CACHE_STRATEGIES.CACHE_FIRST;
  }
  
  // APIs cacheables - Stale While Revalidate
  if (url.pathname.startsWith('/api/') && 
      CACHEABLE_API_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return CACHE_STRATEGIES.STALE_WHILE_REVALIDATE;
  }
  
  // APIs en general - Network First
  if (url.pathname.startsWith('/api/')) {
    return CACHE_STRATEGIES.NETWORK_FIRST;
  }
  
  // Páginas HTML - Network First con fallback
  if (request.destination === 'document') {
    return CACHE_STRATEGIES.NETWORK_FIRST;
  }
  
  // Por defecto - Network First
  return CACHE_STRATEGIES.NETWORK_FIRST;
}

// Manejar request según estrategia
async function handleRequest(request, strategy) {
  const cacheName = getCacheName(request);
  
  switch (strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request, cacheName);
    
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request, cacheName);
    
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request, cacheName);
    
    case CACHE_STRATEGIES.CACHE_ONLY:
      return cacheOnly(request, cacheName);
    
    default:
      return fetch(request);
  }
}

// Obtener nombre de cache según el tipo de request
function getCacheName(request) {
  const url = new URL(request.url);
  
  if (url.pathname.startsWith('/api/')) {
    return API_CACHE;
  }
  
  if (request.destination === 'document') {
    return DYNAMIC_CACHE;
  }
  
  return STATIC_CACHE;
}

// Estrategia Cache First
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse && !isExpired(cachedResponse)) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] Error en Cache First:', error);
    const cache = await caches.open(cacheName);
    return cache.match(request) || new Response('Offline', { status: 503 });
  }
}

// Estrategia Network First
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await Promise.race([
      fetch(request),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Network timeout')), NETWORK_TIMEOUT)
      )
    ]);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', error.message);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback para páginas HTML
    if (request.destination === 'document') {
      return cache.match('/') || new Response('Offline', { 
        status: 503,
        headers: { 'Content-Type': 'text/html' }
      });
    }
    
    throw error;
  }
}

// Estrategia Stale While Revalidate
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Actualizar en background
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch((error) => {
    console.error('[SW] Background fetch failed:', error);
  });
  
  // Retornar cache inmediatamente si existe
  if (cachedResponse && !isExpired(cachedResponse)) {
    return cachedResponse;
  }
  
  // Si no hay cache, esperar por la red
  return fetchPromise;
}

// Estrategia Cache Only
async function cacheOnly(request, cacheName) {
  const cache = await caches.open(cacheName);
  return cache.match(request) || new Response('Not in cache', { status: 404 });
}

// Verificar si una respuesta cacheada ha expirado
function isExpired(response) {
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const responseDate = new Date(dateHeader);
  const now = new Date();
  
  return (now.getTime() - responseDate.getTime()) > CACHE_EXPIRY;
}

// Manejar mensajes del cliente
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_VERSION });
      break;
    
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
    
    case 'CACHE_URLS':
      cacheUrls(payload.urls).then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
  }
});

// Limpiar todos los caches
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  return Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

// Cachear URLs específicas
async function cacheUrls(urls) {
  const cache = await caches.open(DYNAMIC_CACHE);
  return cache.addAll(urls);
}

// Notificar actualizaciones
self.addEventListener('updatefound', () => {
  console.log('[SW] Nueva versión disponible');
  
  // Notificar a todos los clientes
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'UPDATE_AVAILABLE',
        version: CACHE_VERSION
      });
    });
  });
});

console.log('[SW] Service Worker cargado - Versión:', CACHE_VERSION);
