const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundleSize() {
  log('📊 Analizando tamaño del bundle...', 'cyan');
  
  const distPath = path.join(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distPath)) {
    log('❌ Directorio dist no encontrado. Ejecuta "npm run build" primero.', 'red');
    process.exit(1);
  }
  
  const assets = [];
  
  function scanDirectory(dir, prefix = '') {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath, prefix + file + '/');
      } else {
        const relativePath = prefix + file;
        assets.push({
          name: relativePath,
          size: stat.size,
          type: path.extname(file).toLowerCase()
        });
      }
    });
  }
  
  scanDirectory(distPath);
  
  // Agrupar por tipo
  const byType = {};
  let totalSize = 0;
  
  assets.forEach(asset => {
    totalSize += asset.size;
    const type = asset.type || 'other';
    
    if (!byType[type]) {
      byType[type] = { files: [], totalSize: 0 };
    }
    
    byType[type].files.push(asset);
    byType[type].totalSize += asset.size;
  });
  
  // Mostrar resumen
  log('\n📈 Resumen del Bundle:', 'bright');
  log(`Tamaño total: ${formatBytes(totalSize)}`, 'green');
  log(`Número de archivos: ${assets.length}`, 'blue');
  
  // Mostrar por tipo
  log('\n📁 Por tipo de archivo:', 'bright');
  Object.entries(byType)
    .sort(([,a], [,b]) => b.totalSize - a.totalSize)
    .forEach(([type, data]) => {
      const percentage = ((data.totalSize / totalSize) * 100).toFixed(1);
      log(`${type.padEnd(8)} ${formatBytes(data.totalSize).padEnd(10)} (${percentage}%)`, 'yellow');
    });
  
  // Archivos más grandes
  log('\n🔍 Archivos más grandes:', 'bright');
  assets
    .sort((a, b) => b.size - a.size)
    .slice(0, 10)
    .forEach((asset, index) => {
      const percentage = ((asset.size / totalSize) * 100).toFixed(1);
      log(`${(index + 1).toString().padStart(2)}. ${asset.name.padEnd(40)} ${formatBytes(asset.size).padEnd(10)} (${percentage}%)`, 'magenta');
    });
  
  // Recomendaciones
  log('\n💡 Recomendaciones:', 'bright');
  
  const jsFiles = byType['.js'] || { totalSize: 0 };
  const cssFiles = byType['.css'] || { totalSize: 0 };
  
  if (jsFiles.totalSize > 1024 * 1024) { // > 1MB
    log('⚠️  Bundle JS grande (>1MB). Considera code splitting adicional.', 'yellow');
  }
  
  if (cssFiles.totalSize > 512 * 1024) { // > 512KB
    log('⚠️  Bundle CSS grande (>512KB). Considera purgar CSS no utilizado.', 'yellow');
  }
  
  const largeAssets = assets.filter(asset => asset.size > 100 * 1024); // > 100KB
  if (largeAssets.length > 0) {
    log(`⚠️  ${largeAssets.length} archivos grandes (>100KB). Considera optimización.`, 'yellow');
  }
  
  if (totalSize < 2 * 1024 * 1024) { // < 2MB
    log('✅ Tamaño del bundle está en un rango óptimo.', 'green');
  }
  
  return {
    totalSize,
    fileCount: assets.length,
    byType,
    largestFiles: assets.sort((a, b) => b.size - a.size).slice(0, 10)
  };
}

function generateBundleReport() {
  log('📝 Generando reporte detallado...', 'cyan');
  
  const analysis = analyzeBundleSize();
  const reportPath = path.join(__dirname, '..', 'bundle-analysis.json');
  
  const report = {
    timestamp: new Date().toISOString(),
    totalSize: analysis.totalSize,
    totalSizeFormatted: formatBytes(analysis.totalSize),
    fileCount: analysis.fileCount,
    byType: Object.fromEntries(
      Object.entries(analysis.byType).map(([type, data]) => [
        type,
        {
          fileCount: data.files.length,
          totalSize: data.totalSize,
          totalSizeFormatted: formatBytes(data.totalSize),
          percentage: ((data.totalSize / analysis.totalSize) * 100).toFixed(1)
        }
      ])
    ),
    largestFiles: analysis.largestFiles.map(file => ({
      name: file.name,
      size: file.size,
      sizeFormatted: formatBytes(file.size),
      percentage: ((file.size / analysis.totalSize) * 100).toFixed(1)
    }))
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`✅ Reporte guardado en: ${reportPath}`, 'green');
  
  return report;
}

function checkBundleHealth() {
  log('🏥 Verificando salud del bundle...', 'cyan');
  
  const analysis = analyzeBundleSize();
  const issues = [];
  const warnings = [];
  const recommendations = [];
  
  // Verificar tamaño total
  if (analysis.totalSize > 5 * 1024 * 1024) { // > 5MB
    issues.push('Bundle total muy grande (>5MB)');
  } else if (analysis.totalSize > 3 * 1024 * 1024) { // > 3MB
    warnings.push('Bundle total grande (>3MB)');
  }
  
  // Verificar archivos JS
  const jsSize = analysis.byType['.js']?.totalSize || 0;
  if (jsSize > 2 * 1024 * 1024) { // > 2MB
    issues.push('Bundle JavaScript muy grande (>2MB)');
  } else if (jsSize > 1 * 1024 * 1024) { // > 1MB
    warnings.push('Bundle JavaScript grande (>1MB)');
  }
  
  // Verificar número de archivos
  if (analysis.fileCount > 100) {
    warnings.push(`Muchos archivos (${analysis.fileCount})`);
  }
  
  // Verificar archivos individuales grandes
  const largeFiles = analysis.largestFiles.filter(f => f.size > 500 * 1024); // > 500KB
  if (largeFiles.length > 0) {
    warnings.push(`${largeFiles.length} archivos individuales muy grandes (>500KB)`);
  }
  
  // Recomendaciones
  if (jsSize > 1024 * 1024) {
    recommendations.push('Implementar code splitting más agresivo');
    recommendations.push('Usar lazy loading para rutas no críticas');
    recommendations.push('Analizar dependencias con bundle analyzer');
  }
  
  if (analysis.fileCount > 50) {
    recommendations.push('Considerar concatenación de archivos pequeños');
  }
  
  // Mostrar resultados
  log('\n🏥 Estado de Salud del Bundle:', 'bright');
  
  if (issues.length === 0 && warnings.length === 0) {
    log('✅ Bundle en excelente estado', 'green');
  } else {
    if (issues.length > 0) {
      log('❌ Problemas críticos:', 'red');
      issues.forEach(issue => log(`   • ${issue}`, 'red'));
    }
    
    if (warnings.length > 0) {
      log('⚠️  Advertencias:', 'yellow');
      warnings.forEach(warning => log(`   • ${warning}`, 'yellow'));
    }
  }
  
  if (recommendations.length > 0) {
    log('\n💡 Recomendaciones:', 'bright');
    recommendations.forEach(rec => log(`   • ${rec}`, 'cyan'));
  }
  
  return {
    healthy: issues.length === 0,
    issues,
    warnings,
    recommendations
  };
}

// Ejecutar análisis
if (require.main === module) {
  const command = process.argv[2] || 'analyze';
  
  switch (command) {
    case 'analyze':
      analyzeBundleSize();
      break;
    case 'report':
      generateBundleReport();
      break;
    case 'health':
      checkBundleHealth();
      break;
    default:
      log('Comandos disponibles: analyze, report, health', 'yellow');
  }
}

module.exports = {
  analyzeBundleSize,
  generateBundleReport,
  checkBundleHealth,
  formatBytes
};
