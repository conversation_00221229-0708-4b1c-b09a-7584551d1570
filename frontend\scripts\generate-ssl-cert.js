const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const certDir = path.join(__dirname, '..', 'ssl');

// Crear directorio SSL si no existe
if (!fs.existsSync(certDir)) {
  fs.mkdirSync(certDir, { recursive: true });
}

const keyPath = path.join(certDir, 'localhost-key.pem');
const certPath = path.join(certDir, 'localhost.pem');

// Verificar si ya existen los certificados
if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
  console.log('✅ Certificados SSL ya existen');
  process.exit(0);
}

try {
  console.log('🔐 Generando certificados SSL para desarrollo...');
  
  // Generar clave privada
  execSync(`openssl genrsa -out "${keyPath}" 2048`, { stdio: 'inherit' });
  
  // Generar certificado autofirmado
  const certCommand = `openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=CO/ST=Bogota/L=Bogota/O=Hipocrates/OU=Development/CN=localhost"`;
  execSync(certCommand, { stdio: 'inherit' });
  
  console.log('✅ Certificados SSL generados exitosamente');
  console.log(`📁 Clave privada: ${keyPath}`);
  console.log(`📁 Certificado: ${certPath}`);
  console.log('');
  console.log('⚠️  IMPORTANTE: Estos certificados son solo para desarrollo local');
  console.log('   NO los uses en producción');
  
} catch (error) {
  console.error('❌ Error generando certificados SSL:', error.message);
  console.log('');
  console.log('💡 Alternativas:');
  console.log('   1. Instalar OpenSSL: https://www.openssl.org/');
  console.log('   2. Usar mkcert: https://github.com/FiloSottile/mkcert');
  console.log('   3. Continuar sin HTTPS (menos seguro)');
  process.exit(1);
}
