import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import App from '../../App';

// Mock del contexto de autenticación
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user',
      hospital_id: 1,
      nombre: 'Test User',
      apellido: 'Admin',
      email: '<EMAIL>',
      rol: 'SUPER_ADMIN',
      activo: true
    },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn()
  })
}));

// Mock de servicios críticos
jest.mock('../../services/authService', () => ({
  login: jest.fn().mockResolvedValue({
    user: { id: 'test-user', nombre: 'Test User', rol: 'SUPER_ADMIN' },
    token: 'mock-token'
  }),
  logout: jest.fn(),
  getCurrentUser: jest.fn().mockResolvedValue({
    id: 'test-user',
    nombre: 'Test User',
    rol: 'SUPER_ADMIN'
  })
}));

// Mock de React Router para navegación
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  }),
}));

// Wrapper para providers
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Frontend Completo - Tests de Integración E2E', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Simular que el usuario está autenticado
    localStorage.setItem('auth_token', 'mock-token');
  });

  afterEach(() => {
    localStorage.clear();
  });

  describe('Navegación principal del sistema', () => {
    it('debe cargar la aplicación principal sin errores', async () => {
      render(<App />, { wrapper: createWrapper() });

      // Verificar que la aplicación se carga
      await waitFor(() => {
        // Buscar elementos que indiquen que la app se cargó
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe permitir navegación entre módulos principales', async () => {
      const user = userEvent.setup();
      render(<App />, { wrapper: createWrapper() });

      // Simular navegación a diferentes módulos
      // Nota: Esto dependería de la estructura real del menú de navegación
      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Módulos administrativos modernizados', () => {
    it('debe cargar el módulo de Recursos Humanos', async () => {
      // Mock específico para RRHH
      const mockRRHHData = {
        empleados: [
          {
            id: 1,
            nombres: 'Juan',
            apellidos: 'Pérez',
            cargo_nombre: 'Médico',
            departamento_nombre: 'Medicina',
            estado: 'Activo'
          }
        ],
        resumen: {
          total_empleados: 1,
          empleados_activos: 1,
          empleados_inactivos: 0
        }
      };

      // Simular navegación directa al módulo
      window.history.pushState({}, 'RRHH', '/recursos-humanos');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe cargar el módulo de Presupuesto', async () => {
      // Simular navegación al módulo de presupuesto
      window.history.pushState({}, 'Presupuesto', '/presupuesto');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe cargar el módulo de Proveedores', async () => {
      // Simular navegación al módulo de proveedores
      window.history.pushState({}, 'Proveedores', '/proveedores');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe cargar el módulo de Contabilidad', async () => {
      // Simular navegación al módulo de contabilidad
      window.history.pushState({}, 'Contabilidad', '/contabilidad');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe cargar el módulo de Diagnósticos CIE', async () => {
      // Simular navegación al módulo de diagnósticos
      window.history.pushState({}, 'Diagnósticos CIE', '/diagnosticos-cie');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Sistema de ambulancias avanzado', () => {
    it('debe cargar el dashboard de ambulancias', async () => {
      window.history.pushState({}, 'Ambulancias', '/ambulancias');
      
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });

    it('debe manejar WebSocket para tiempo real', async () => {
      // Mock del WebSocket
      const mockWebSocket = {
        send: jest.fn(),
        close: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        readyState: WebSocket.OPEN
      };

      global.WebSocket = jest.fn(() => mockWebSocket) as any;

      window.history.pushState({}, 'Ambulancias', '/ambulancias');
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Funcionalidades críticas del sistema', () => {
    it('debe manejar errores de red graciosamente', async () => {
      // Mock de error de red
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });

      // Verificar que la aplicación no se rompa
      expect(document.body).toBeInTheDocument();
    });

    it('debe mantener el estado de autenticación', async () => {
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(localStorage.getItem('auth_token')).toBe('mock-token');
      });
    });

    it('debe ser responsivo en diferentes tamaños de pantalla', async () => {
      // Simular diferentes tamaños de pantalla
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });

      // Cambiar a móvil
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      window.dispatchEvent(new Event('resize'));

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Performance y optimización', () => {
    it('debe cargar componentes de forma lazy', async () => {
      render(<App />, { wrapper: createWrapper() });

      // Verificar que la aplicación se carga sin bloqueos
      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    it('debe manejar múltiples consultas simultáneas', async () => {
      // Simular múltiples consultas
      const promises = Array.from({ length: 5 }, (_, i) => 
        new Promise(resolve => setTimeout(resolve, 100 * i))
      );

      render(<App />, { wrapper: createWrapper() });

      await Promise.all(promises);

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Accesibilidad y UX', () => {
    it('debe tener elementos accesibles', async () => {
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        // Verificar que existan elementos con roles ARIA
        const body = document.body;
        expect(body).toBeInTheDocument();
      });
    });

    it('debe manejar navegación por teclado', async () => {
      const user = userEvent.setup();
      render(<App />, { wrapper: createWrapper() });

      // Simular navegación por teclado
      await user.keyboard('{Tab}');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Integración con APIs externas', () => {
    it('debe conectar con la API de CIE-11 de la OMS', async () => {
      // Mock de la API de CIE-11
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          destinationEntities: [
            {
              id: 'http://id.who.int/icd/entity/455013390',
              title: 'Fever',
              definition: 'Elevation of body temperature'
            }
          ]
        })
      });

      window.history.pushState({}, 'Diagnósticos', '/diagnosticos-cie');
      render(<App />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });
});
