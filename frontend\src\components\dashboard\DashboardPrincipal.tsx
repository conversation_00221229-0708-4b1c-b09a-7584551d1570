import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { usePermissions } from '../../hooks/usePermissions';
import { 
  Building2, 
  Users, 
  Calendar, 
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Heart,
  Stethoscope,
  Bed,
  Truck,
  Shield,
  Settings
} from 'lucide-react';

const DashboardPrincipal: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isSystemAdmin, isTenantAdmin } = usePermissions();
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Datos de ejemplo para el dashboard principal
  const metricas = {
    // Métricas generales del hospital
    pacientes_activos: 1247,
    citas_hoy: 45,
    citas_semana: 312,
    ocupacion_camas: 78.5,
    personal_activo: 156,
    ambulancias_disponibles: 8,
    
    // Métricas financieras (solo para admins)
    ingresos_mes: 185000000,
    gastos_mes: 142000000,
    margen_operativo: 23.2,
    
    // Métricas de calidad
    satisfaccion_pacientes: 4.6,
    tiempo_espera_promedio: 18,
    
    // Alertas
    alertas_criticas: 3,
    alertas_pendientes: 12
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Buenos días';
    if (hour < 18) return 'Buenas tardes';
    return 'Buenas noches';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header de bienvenida */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getGreeting()}, {user?.nombre || 'Usuario'}
            </h1>
            <p className="text-gray-600 mt-2">
              Resumen general del sistema - {new Date().toLocaleDateString('es-CO', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select 
              value={selectedPeriod} 
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Últimos 7 días</option>
              <option value="30d">Últimos 30 días</option>
              <option value="90d">Últimos 90 días</option>
            </select>
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Configurar
            </Button>
          </div>
        </div>

        {/* Alertas importantes */}
        {metricas.alertas_criticas > 0 && (
          <Card className="border-orange-200 bg-orange-50/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-900">
                    Tienes {metricas.alertas_criticas} alertas críticas que requieren atención inmediata
                  </p>
                  <p className="text-sm text-orange-700">
                    {metricas.alertas_pendientes} alertas adicionales pendientes de revisión
                  </p>
                </div>
                <Button size="sm" className="ml-auto bg-orange-600 hover:bg-orange-700">
                  Ver Alertas
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Pacientes */}
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pacientes Activos</p>
                  <p className="text-3xl font-bold text-blue-700">{metricas.pacientes_activos.toLocaleString()}</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">+5.2% vs mes anterior</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Citas */}
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Citas Hoy</p>
                  <p className="text-3xl font-bold text-green-700">{metricas.citas_hoy}</p>
                  <div className="flex items-center mt-2">
                    <Calendar className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm text-blue-600">{metricas.citas_semana} esta semana</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                  <Calendar className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Ocupación de camas */}
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ocupación Camas</p>
                  <p className="text-3xl font-bold text-purple-700">{metricas.ocupacion_camas}%</p>
                  <div className="flex items-center mt-2">
                    <Activity className="w-4 h-4 text-purple-600 mr-1" />
                    <span className="text-sm text-purple-600">Nivel óptimo</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                  <Bed className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal activo */}
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Personal Activo</p>
                  <p className="text-3xl font-bold text-indigo-700">{metricas.personal_activo}</p>
                  <div className="flex items-center mt-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">100% disponibilidad</span>
                  </div>
                </div>
                <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                  <Stethoscope className="w-8 h-8 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Métricas financieras (solo para administradores) */}
        {(isSystemAdmin() || isTenantAdmin()) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ingresos del Mes</p>
                    <p className="text-2xl font-bold text-green-700">
                      {formatCurrency(metricas.ingresos_mes)}
                    </p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">+8.3% vs mes anterior</span>
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                    <DollarSign className="w-8 h-8 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Gastos del Mes</p>
                    <p className="text-2xl font-bold text-red-700">
                      {formatCurrency(metricas.gastos_mes)}
                    </p>
                    <div className="flex items-center mt-2">
                      <TrendingDown className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">-2.1% vs mes anterior</span>
                    </div>
                  </div>
                  <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                    <BarChart3 className="w-8 h-8 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Margen Operativo</p>
                    <p className="text-2xl font-bold text-blue-700">{metricas.margen_operativo}%</p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">Excelente</span>
                    </div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                    <Activity className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Accesos rápidos */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle>Accesos Rápidos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Users className="w-6 h-6" />
                <span className="text-sm">Pacientes</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Calendar className="w-6 h-6" />
                <span className="text-sm">Citas</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Stethoscope className="w-6 h-6" />
                <span className="text-sm">Consultas</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Bed className="w-6 h-6" />
                <span className="text-sm">Hospitalización</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Truck className="w-6 h-6" />
                <span className="text-sm">Ambulancias</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Heart className="w-6 h-6" />
                <span className="text-sm">Urgencias</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Acceso a administración SaaS (solo SUPER_ADMIN) */}
        {isSystemAdmin() && (
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Shield className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900">Administración SaaS</h3>
                    <p className="text-blue-700">Gestión de tenants, planes y métricas de negocio</p>
                  </div>
                </div>
                <Button
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={() => navigate('/administracion-negocio')}
                >
                  <Building2 className="w-4 h-4 mr-2" />
                  Acceder
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DashboardPrincipal;
