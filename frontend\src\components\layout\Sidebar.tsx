import { useState, CSSProperties } from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { Link, useLocation } from 'react-router-dom';
// import { useAuth } from '../../context/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { usePermissions } from '../../hooks/usePermissions';
import {
  faStethoscope,
  faAmbulance,
  faProcedures,
  faBed,
  faBoxes,
  faMedkit,
  faPills,
  faStethoscope as faStethoscopeIcon,
  faChevronDown,
  faChevronRight,
  faHospital,
  faUserMd,
  faCog,
  faWarehouse,
  faMoneyBill,
  faChartBar,
  faChartLine,
  faRecycle,
  faTruck,
  faCheckCircle,
  faExclamationTriangle,
  faDatabase,
  faSync,
  faChartPie,
  faScissors,
  faFlask,
  faTint,
  faBuilding,
  faCrown,
} from '@fortawesome/free-solid-svg-icons';

export interface SidebarProps {
  glassStyle?: CSSProperties;
  isOpen: boolean;
  onToggle: () => void;
}

export const Sidebar = ({ glassStyle, isOpen, onToggle }: SidebarProps) => {
  const location = useLocation();
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['atencion-medica']);

  const { filterMenuItems, userRole } = usePermissions(); // Removed isSystemAdmin as it's unused for now

interface SubMenuItem {
  path: string;
  label: string;
  icon: string;
}

interface MenuItem {
  path: string;
  label: string;
  icon: string;
  subitems?: SubMenuItem[];
}

interface MenuCategory {
  id: string;
  label: string;
  icon: IconDefinition;
  items: MenuItem[];
  permissions?: string[];
}

  // Categorías del menú
  const menuCategories: MenuCategory[] = [
    {
      id: 'principal',
      label: 'Principal',
      icon: faHospital,
      items: [
        { path: '/dashboard', label: 'Dashboard', icon: 'home' },
        { path: '/pacientes', label: 'Pacientes', icon: 'user' },
        { path: '/medicos', label: 'Médicos', icon: 'stethoscope' },
        { path: '/enfermeras', label: 'Enfermeras', icon: 'heart' },
        { path: '/farmacia', label: 'Farmacia', icon: 'pill' },
        { path: '/citas', label: 'Citas', icon: 'calendar' },
      ]
    },
    {
      id: 'atencion-medica',
      label: 'Atención Médica',
      icon: faUserMd,
      items: [
        { path: '/consultas', label: 'Consultas', icon: 'stethoscope' },
        { path: '/urgencias', label: 'Urgencias', icon: 'ambulance' },
        { path: '/ambulancias', label: 'Ambulancias', icon: 'ambulance' },
        { 
          path: '/quirofanos', 
          label: 'Quirófanos', 
          icon: 'procedures',
          subitems: [
            { path: '/quirofanos/cirugias', label: 'Cirugías', icon: 'scissors' }
          ]
        },
        { path: '/hospitalizaciones', label: 'Hospitalizaciones', icon: 'bed' },
        { path: '/historias-clinicas', label: 'Historias Clínicas', icon: 'clipboard' },
        { path: '/laboratorio', label: 'Laboratorio Clínico', icon: 'flask' },
        { path: '/imagenes-diagnosticas', label: 'Imágenes Diagnósticas', icon: 'scan' },
        { path: '/banco-sangre', label: 'Banco de Sangre', icon: 'droplet' },
        { path: '/telemedicina', label: 'Telemedicina', icon: 'video' },
      ]
    },
    {
      id: 'farmacia-inventario',
      label: 'Farmacia e Inventario',
      icon: faMedkit,
      items: [
        { path: '/medicamentos', label: 'Medicamentos', icon: 'medkit' },
        { path: '/dispensaciones', label: 'Dispensaciones', icon: 'pills' },
        { path: '/activos', label: 'Activos', icon: 'boxes' },
        { path: '/inventario', label: 'Inventario General', icon: 'warehouse' },
        { path: '/residuosHospitalarios', label: 'Residuos Hospitalarios', icon: 'recycle' },
      ]
    },
    {
      id: 'financiero',
      label: 'Gestión Financiera',
      icon: faMoneyBill,
      items: [
        { path: '/facturacion', label: 'Facturación', icon: 'file-invoice' },
        { path: '/contabilidad', label: 'Contabilidad', icon: 'file-invoice-dollar' },
        { path: '/presupuesto', label: 'Presupuesto', icon: 'money-bill' },
        { path: '/reportes', label: 'Reportes Financieros', icon: 'chart-bar' },
      ]
    },
    {
      id: 'gestion-administrativa',
      label: 'Gestión Administrativa',
      icon: faCog,
      items: [
        { path: '/recursos-humanos', label: 'Recursos Humanos', icon: 'users' },
        { path: '/proveedores', label: 'Proveedores', icon: 'truck' },
        { path: '/autorizaciones', label: 'Autorizaciones', icon: 'check-circle' },
        { path: '/incidentes-adversos', label: 'Incidentes Adversos', icon: 'exclamation-triangle' },
      ]
    },
    {
      id: 'analitica',
      label: 'Analítica y Reportes',
      icon: faChartLine,
      items: [
        { path: '/analitica-predictiva', label: 'Analítica Predictiva', icon: 'chart-line' },
        { path: '/dashboard-ejecutivo', label: 'Dashboard Ejecutivo', icon: 'chart-pie' },
        { path: '/dashboard-negocio', label: 'Dashboard de Negocio', icon: 'crown' },
        { path: '/reportes-generales', label: 'Reportes Generales', icon: 'chart-bar' },
      ]
    },
    {
      id: 'gestion-ips',
      label: 'Gestión de IPS',
      icon: faBuilding,
      items: [
        { path: '/gestion-ips', label: 'Mi Suscripción', icon: 'building' },
        { path: '/metodos-pago', label: 'Métodos de Pago', icon: 'credit-card' },
        { path: '/configuracion-alertas', label: 'Configurar Alertas', icon: 'bell' },
      ]
    },
    {
      id: 'cie11',
      label: 'CIE-11',
      icon: faDatabase,
      items: [
        { path: '/configuraciones/cie11', label: 'Configuración CIE-11', icon: 'sync' },
        { path: '/reportes/cie11', label: 'Estadísticas CIE-11', icon: 'chart-pie' },
      ]
    },
    {
      id: 'admin',
      label: 'Administración',
      icon: faCog,
      items: [
        { path: '/administracion', label: 'Panel de Administración', icon: 'settings' },
        { path: '/admin/users', label: 'Usuarios', icon: 'users' },
        { path: '/admin/roles', label: 'Roles', icon: 'shield' },
        { path: '/admin/settings', label: 'Configuración', icon: 'cog' },
        { path: '/administracion-negocio', label: 'Administración SaaS', icon: 'business-time' },
      ]
    },
  ];

  // Filtrar menú según permisos del usuario
  const filteredMenuCategories = menuCategories.filter(category => filterMenuItems(category.items)).map((category: MenuCategory) => category);

  // Fallback: si no hay categorías filtradas, mostrar todas (para evitar menú vacío)
  const displayMenuCategories = filteredMenuCategories.length > 0 ? filteredMenuCategories : menuCategories;

  // Función para expandir/colapsar categorías
  const toggleCategory = (categoryId: string) => {
    if (isOpen) {
      setExpandedCategories(prev =>
        prev.includes(categoryId)
          ? prev.filter(id => id !== categoryId)
          : [...prev, categoryId]
      );
    }
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'home':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
        );
      case 'users':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
        );
      case 'shield':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l4 4m0 4l4-4m4-4H5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4v-3a2 2 0 00-2-2H9z"></path>
          </svg>
        );
      case 'settings':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
        );
      case 'business-time':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        );
      case 'user':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        );
      case 'calendar':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        );
      case 'file-invoice':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        );
      case 'clipboard':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
        );
      case 'boxes':
        return <FontAwesomeIcon icon={faBoxes} className="w-5 h-5" />;
      case 'warehouse':
        return <FontAwesomeIcon icon={faWarehouse} className="w-5 h-5" />;
      case 'video':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        );
      case 'stethoscope':
        return <FontAwesomeIcon icon={faStethoscope} className="w-5 h-5" />;
      case 'ambulance':
        return <FontAwesomeIcon icon={faAmbulance} className="w-5 h-5" />;
      case 'scalpel':
        return <FontAwesomeIcon icon={faProcedures} className="w-5 h-5" />;
      case 'medical-equipment':
        return <FontAwesomeIcon icon={faStethoscopeIcon} className="w-5 h-5" />;
      case 'medkit':
        return <FontAwesomeIcon icon={faMedkit} className="w-5 h-5" />;
      case 'pill':
        return <FontAwesomeIcon icon={faPills} className="w-5 h-5" />;
      case 'pills':
        return <FontAwesomeIcon icon={faPills} className="w-5 h-5" />;
      case 'bed':
        return <FontAwesomeIcon icon={faBed} className="w-5 h-5" />;
      case 'money-bill':
        return <FontAwesomeIcon icon={faMoneyBill} className="w-5 h-5" />;
      case 'chart-bar':
        return <FontAwesomeIcon icon={faChartBar} className="w-5 h-5" />;
      case 'chart-line':
        return <FontAwesomeIcon icon={faChartLine} className="w-5 h-5" />;
      case 'recycle':
        return <FontAwesomeIcon icon={faRecycle} className="w-5 h-5" />;
      case 'sync':
        return <FontAwesomeIcon icon={faSync} className="w-5 h-5" />;
      case 'chart-pie':
        return <FontAwesomeIcon icon={faChartPie} className="w-5 h-5" />;
      case 'truck':
        return <FontAwesomeIcon icon={faTruck} className="w-5 h-5" />;
      case 'file-invoice-dollar':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        );
      case 'check-circle':
        return <FontAwesomeIcon icon={faCheckCircle} className="w-5 h-5" />;
      case 'exclamation-triangle':
        return <FontAwesomeIcon icon={faExclamationTriangle} className="w-5 h-5" />;
      case 'scissors':
        return <FontAwesomeIcon icon={faScissors} className="w-5 h-5" />;
      case 'procedures':
        return <FontAwesomeIcon icon={faProcedures} className="w-5 h-5" />;
      case 'flask':
        return <FontAwesomeIcon icon={faFlask} className="w-5 h-5" />;
      case 'droplet':
        return <FontAwesomeIcon icon={faTint} className="w-5 h-5" />;
      case 'building':
        return <FontAwesomeIcon icon={faBuilding} className="w-5 h-5" />;
      case 'crown':
        return <FontAwesomeIcon icon={faCrown} className="w-5 h-5" />;
      default:
        return null;
    }
  };

  return (
    <div
      className={`glass-sidebar fixed top-16 left-0 bottom-0 transition-all duration-300 overflow-hidden z-40 flex flex-col ${
        !isOpen ? 'w-20' : 'w-64'
      }`}
      style={glassStyle}
    >
      <div className="flex items-center justify-between p-4 border-b border-white/10 dark:border-black/10 flex-shrink-0">
        {isOpen ? (
          // Full logo and text when sidebar is open
          <div className={`flex items-center ${!isOpen ? 'w-full justify-center' : 'space-x-3'}`}>
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg" style={{
              background: 'linear-gradient(135deg, #0059B2, #4F46E5, #7C3AED)'
            }}>
              <span className="text-white font-bold text-xl">H</span>
            </div>
            <div>
              <h1 className="text-adaptive text-lg font-bold tracking-tight">Hipócrates</h1>
              <p className="text-adaptive-subtle text-xs">Sistema Integral</p>
              <div className="text-xs text-yellow-300 mt-1">
                👑 {userRole}
              </div>
            </div>
          </div>
        ) : (
          // Collapsed logo (just 'H')
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg mx-auto" style={{
            background: 'linear-gradient(135deg, #0059B2, #4F46E5, #7C3AED)'
          }}>
            <span className="text-white font-bold text-lg">H</span>
          </div>
        )}

        <button
          onClick={onToggle}
          className="glass-card p-2 rounded-lg text-adaptive-muted hover:text-adaptive focus:outline-none transition-all duration-200 transform hover:scale-105 ml-auto"
        >
          {!isOpen ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
            </svg>
          )}
        </button>
      </div>

      <div className="flex-1 py-4 overflow-y-auto custom-scrollbar">
        <ul className="space-y-2 px-3 pb-4">
          {displayMenuCategories.map((category: MenuCategory) => (
            <li key={category.id} className="mb-3">
              <button
                onClick={() => toggleCategory(category.id)}
                className={`w-full flex items-center ${!isOpen ? 'justify-center px-0' : 'justify-between px-4'} py-3 rounded-xl glass-card hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] text-adaptive`}
              >
                <div className={`flex items-center ${!isOpen ? 'w-full justify-center' : 'space-x-3'}`}>
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center" style={{
                    background: 'linear-gradient(135deg, rgba(0, 89, 178, 0.2), rgba(124, 58, 237, 0.2))'
                  }}>
                    <FontAwesomeIcon icon={category.icon} className="w-4 h-4" style={{ color: '#0059B2' }} />
                  </div>
                  {isOpen && <span className="font-medium text-sm">{category.label}</span>}
                </div>
                {isOpen && (
                  <FontAwesomeIcon
                    icon={expandedCategories.includes(category.id) ? faChevronDown : faChevronRight}
                    className={`w-3 h-3 text-adaptive-muted transition-transform duration-200 ${
                      expandedCategories.includes(category.id) ? 'rotate-0' : 'rotate-0'
                    }`}
                  />
                )}
              </button>

              {isOpen && expandedCategories.includes(category.id) && (
                <ul className={'ml-4 mt-2 space-y-1'}>
                  {category.items.map((item: MenuItem) => (
                    <li key={item.path}>
                      {/* Si el item tiene subitems, lo manejamos diferente */}
                      {item.subitems ? (
                        <div>
                          <Link
                            to={item.path}
                            className={`flex items-center ${!isOpen ? 'justify-center px-0' : 'px-3'} py-2.5 rounded-lg ${
                              location.pathname === item.path
                                ? 'shadow-lg'
                                : 'text-white hover:text-blue-200 hover:bg-white/10 dark:hover:bg-black/10'
                            } transition-all duration-200 transform hover:scale-[1.02]`}
                            style={location.pathname === item.path ? {
                              background: 'linear-gradient(135deg, rgba(0, 89, 178, 0.2), rgba(124, 58, 237, 0.2))',
                              color: '#0059B2'
                            } : {}}
                          >
                            <div className={`w-6 h-6 rounded-md bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center ${!isOpen ? '' : 'mr-3'}`}>
                              <div className="text-blue-400">{getIcon(item.icon)}</div>
                            </div>
                            {isOpen && <span className="text-sm font-medium text-white">{item.label}</span>}
                            {isOpen && item.subitems && (
                              <FontAwesomeIcon
                                icon={location.pathname.startsWith(item.path) ? faChevronDown : faChevronRight}
                                className="w-3 h-3 ml-auto text-white opacity-70"
                              />
                            )}
                          </Link>
                          
                          {/* Renderizar subitems si estamos en la ruta principal */}
                          {isOpen && location.pathname.startsWith(item.path) && item.subitems && (
                            <ul className="pl-6 mt-2 space-y-1">
                              {item.subitems.map((subitem: SubMenuItem) => (
                                <li key={subitem.path}>
                                  <Link
                                    to={subitem.path}
                                    className={`flex items-center py-2.5 rounded-lg ${!isOpen ? 'justify-center px-0' : 'px-3'} ${
                                      location.pathname === subitem.path || location.pathname.startsWith(`${subitem.path}/`)
                                        ? ''
                                        : 'text-gray-200 hover:text-blue-200 hover:bg-white/10 dark:hover:bg-black/10'
                                    } transition-all duration-200 transform hover:scale-[1.02]`}
                                    style={(location.pathname === subitem.path || location.pathname.startsWith(`${subitem.path}/`)) ? {
                                      background: 'linear-gradient(135deg, rgba(0, 89, 178, 0.15), rgba(124, 58, 237, 0.15))',
                                      color: '#0059B2'
                                    } : {}}
                                  >
                                    <div className={`w-5 h-5 rounded-md bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center ${!isOpen ? '' : 'mr-3'}`}>
                                      <div className="text-blue-400">{getIcon(subitem.icon)}</div>
                                    </div>
                                    {isOpen && <span className="text-xs font-medium text-white">{subitem.label}</span>}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      ) : (
                        <Link
                          to={item.path}
                          className={`flex items-center py-2.5 rounded-lg ${!isOpen ? 'justify-center px-0' : 'px-3'} ${
                            location.pathname === item.path || location.pathname.startsWith(`${item.path}/`)
                              ? 'shadow-lg'
                              : 'text-white hover:text-blue-200 hover:bg-white/10 dark:hover:bg-black/10'
                          } transition-all duration-200 transform hover:scale-[1.02]`}
                          style={(location.pathname === item.path || location.pathname.startsWith(`${item.path}/`)) ? {
                            background: 'linear-gradient(135deg, rgba(0, 89, 178, 0.2), rgba(124, 58, 237, 0.2))',
                            color: '#0059B2'
                          } : {}}
                        >
                          <div className={`w-6 h-6 rounded-md bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center ${!isOpen ? '' : 'mr-3'}`}>
                            <div className="text-blue-400">{getIcon(item.icon)}</div>
                          </div>
                          {isOpen && <span className="text-sm font-medium text-white">{item.label}</span>}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>


    </div>
  );
};
