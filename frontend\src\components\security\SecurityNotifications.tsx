import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '../ui/Alert';
import { Button } from '../ui/Button';
import { useAuthAdvanced } from '../../hooks/useAuthAdvanced';
import { 
  Shield, 
  AlertTriangle, 
  Clock, 
  RefreshCw, 
  Lock,
  Unlock,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';

interface SecurityStatus {
  isHttps: boolean;
  hasValidToken: boolean;
  tokenExpiringSoon: boolean;
  lastActivity: Date | null;
  sessionTimeout: number; // minutos
}

const SecurityNotifications: React.FC = () => {
  const { isAuthenticated, isTokenExpiringSoon, refreshToken, logout } = useAuthAdvanced();
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus>({
    isHttps: window.location.protocol === 'https:',
    hasValidToken: isAuthenticated,
    tokenExpiringSoon: isTokenExpiringSoon,
    lastActivity: new Date(),
    sessionTimeout: 30 // 30 minutos por defecto
  });
  
  const [showSecurityPanel, setShowSecurityPanel] = useState(false);
  const [inactivityTimer, setInactivityTimer] = useState<NodeJS.Timeout | null>(null);

  // Actualizar estado de seguridad
  useEffect(() => {
    setSecurityStatus(prev => ({
      ...prev,
      hasValidToken: isAuthenticated,
      tokenExpiringSoon: isTokenExpiringSoon,
      isHttps: window.location.protocol === 'https:'
    }));
  }, [isAuthenticated, isTokenExpiringSoon]);

  // Monitor de actividad del usuario
  useEffect(() => {
    const updateActivity = () => {
      setSecurityStatus(prev => ({
        ...prev,
        lastActivity: new Date()
      }));
    };

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, []);

  // Timer de inactividad
  useEffect(() => {
    if (!isAuthenticated) return;

    if (inactivityTimer) {
      clearTimeout(inactivityTimer);
    }

    const timer = setTimeout(() => {
      logout();
    }, securityStatus.sessionTimeout * 60 * 1000);

    setInactivityTimer(timer);

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [securityStatus.lastActivity, securityStatus.sessionTimeout, isAuthenticated, logout]);

  const handleRefreshToken = async () => {
    try {
      await refreshToken();
    } catch (error) {
      console.error('Error refreshing token:', error);
    }
  };

  const getSecurityLevel = (): 'high' | 'medium' | 'low' => {
    if (!securityStatus.isHttps) return 'low';
    if (securityStatus.tokenExpiringSoon) return 'medium';
    if (securityStatus.hasValidToken && securityStatus.isHttps) return 'high';
    return 'low';
  };

  const securityLevel = getSecurityLevel();

  const SecurityLevelIndicator = () => {
    const configs = {
      high: {
        icon: <Shield className="w-5 h-5 text-green-600" />,
        text: 'Seguridad Alta',
        color: 'bg-green-100 text-green-800 border-green-200'
      },
      medium: {
        icon: <AlertTriangle className="w-5 h-5 text-yellow-600" />,
        text: 'Seguridad Media',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200'
      },
      low: {
        icon: <XCircle className="w-5 h-5 text-red-600" />,
        text: 'Seguridad Baja',
        color: 'bg-red-100 text-red-800 border-red-200'
      }
    };

    const config = configs[securityLevel];

    return (
      <div className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${config.color}`}>
        {config.icon}
        <span className="text-sm font-medium">{config.text}</span>
      </div>
    );
  };

  if (!isAuthenticated) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {/* Indicador de nivel de seguridad */}
      <div className="flex items-center gap-2">
        <SecurityLevelIndicator />
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowSecurityPanel(!showSecurityPanel)}
        >
          <Info className="w-4 h-4" />
        </Button>
      </div>

      {/* Notificación de token próximo a expirar */}
      {securityStatus.tokenExpiringSoon && (
        <Alert className="bg-yellow-50 border-yellow-200">
          <Clock className="w-4 h-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Tu sesión expirará pronto.
            <Button
              variant="link"
              size="sm"
              onClick={handleRefreshToken}
              className="ml-2 text-yellow-700 hover:text-yellow-900"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Renovar
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Notificación de conexión no segura */}
      {!securityStatus.isHttps && (
        <Alert className="bg-red-50 border-red-200">
          <Unlock className="w-4 h-4 text-red-600" />
          <AlertDescription className="text-red-800">
            Conexión no segura (HTTP). Se recomienda usar HTTPS en producción.
          </AlertDescription>
        </Alert>
      )}

      {/* Panel de seguridad detallado */}
      {showSecurityPanel && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Estado de Seguridad
          </h3>
          
          <div className="space-y-3">
            {/* HTTPS Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Conexión HTTPS</span>
              <div className="flex items-center gap-1">
                {securityStatus.isHttps ? (
                  <>
                    <Lock className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-green-600">Segura</span>
                  </>
                ) : (
                  <>
                    <Unlock className="w-4 h-4 text-red-600" />
                    <span className="text-sm text-red-600">No segura</span>
                  </>
                )}
              </div>
            </div>

            {/* Token Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Token de sesión</span>
              <div className="flex items-center gap-1">
                {securityStatus.hasValidToken ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-green-600">Válido</span>
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 text-red-600" />
                    <span className="text-sm text-red-600">Inválido</span>
                  </>
                )}
              </div>
            </div>

            {/* Token Expiry */}
            {securityStatus.tokenExpiringSoon && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Expiración</span>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-600">Próxima</span>
                </div>
              </div>
            )}

            {/* Last Activity */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Última actividad</span>
              <span className="text-sm text-gray-900">
                {securityStatus.lastActivity?.toLocaleTimeString() || 'N/A'}
              </span>
            </div>

            {/* Session Timeout */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Timeout de sesión</span>
              <span className="text-sm text-gray-900">
                {securityStatus.sessionTimeout} min
              </span>
            </div>
          </div>

          <div className="mt-4 pt-3 border-t border-gray-200">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSecurityPanel(false)}
              className="w-full"
            >
              Cerrar
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityNotifications;
