import React from 'react';
import { twMerge } from 'tailwind-merge';

interface BadgeProps {
  children: React.ReactNode;
  color?: 'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'purple' | 'orange';
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  color = 'gray',
  variant = 'default',
  className = ''
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'secondary':
        return 'bg-gray-100 text-gray-800 border border-gray-200';
      case 'destructive':
        return 'bg-red-100 text-red-800 border border-red-200';
      case 'outline':
        return 'bg-transparent text-gray-700 border border-gray-300';
      case 'default':
      default:
        return getColorClasses();
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'red':
        return 'bg-red-500 text-white';
      case 'yellow':
        return 'bg-yellow-500 text-black';
      case 'green':
        return 'bg-green-500 text-white';
      case 'blue':
        return 'text-white';
      case 'purple':
        return 'bg-purple-500 text-white';
      case 'orange':
        return 'bg-orange-500 text-white';
      case 'gray':
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getStyle = () => {
    if (color === 'blue') {
      return {
        background: '#0059B2'
      };
    }
    return {};
  };

  return (
    <span
      className={twMerge(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        getVariantClasses(),
        className
      )}
      style={getStyle()}
    >
      {children}
    </span>
  );
};

export default Badge;
