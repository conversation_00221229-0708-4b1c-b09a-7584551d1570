import { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'elevated' | 'bordered';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
}

export const Card = ({
  children,
  className = '',
  variant = 'glass',
  padding = 'lg',
  hover = true
}: CardProps) => {
  const baseClasses = 'rounded-xl transition-all duration-300';

  const variantClasses = {
    default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
    glass: 'glass-card backdrop-blur-md',
    elevated: 'bg-white dark:bg-gray-800 shadow-xl border border-gray-100 dark:border-gray-700',
    bordered: 'bg-white dark:bg-gray-800 border-2 border-blue-200 dark:border-blue-800'
  };

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };

  const hoverClasses = hover ? 'hover:shadow-lg hover:scale-[1.02] transform' : '';

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${paddingClasses[padding]} ${hoverClasses} ${className}`}
    >
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: ReactNode;
  className?: string;
  icon?: ReactNode;
  title?: string;
  subtitle?: string;
}

export const CardHeader = ({
  children,
  className = '',
  icon,
  title,
  subtitle
}: CardHeaderProps) => {
  if (title || subtitle || icon) {
    return (
      <div className={`flex items-center space-x-4 mb-6 ${className}`}>
        {icon && (
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center">
            {icon}
          </div>
        )}
        <div className="flex-1">
          {title && (
            <h3 className="text-xl font-bold text-adaptive">{title}</h3>
          )}
          {subtitle && (
            <p className="text-adaptive-muted text-sm">{subtitle}</p>
          )}
        </div>
        {children}
      </div>
    );
  }

  return (
    <div className={`mb-6 ${className}`}>
      {children}
    </div>
  );
};

interface CardContentProps {
  children: ReactNode;
  className?: string;
}

export const CardContent = ({
  children,
  className = ''
}: CardContentProps) => {
  return (
    <div className={className}>
      {children}
    </div>
  );
};

interface CardTitleProps {
  children: ReactNode;
  className?: string;
}

export const CardTitle = ({
  children,
  className = ''
}: CardTitleProps) => {
  return (
    <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>
      {children}
    </h3>
  );
};

interface CardFooterProps {
  children: ReactNode;
  className?: string;
  bordered?: boolean;
}

export const CardFooter = ({
  children,
  className = '',
  bordered = true
}: CardFooterProps) => {
  const borderClass = bordered ? 'border-t border-white/10 dark:border-black/10 pt-6 mt-6' : '';

  return (
    <div className={`${borderClass} ${className}`}>
      {children}
    </div>
  );
};

// Componente compuesto
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Title = CardTitle;
Card.Footer = CardFooter;
