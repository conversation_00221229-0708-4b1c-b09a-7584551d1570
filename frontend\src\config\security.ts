// Configuración de seguridad por entorno
export interface SecurityConfig {
  // JWT Configuration
  jwt: {
    accessTokenExpiry: number; // segundos
    refreshTokenExpiry: number; // segundos
    refreshBufferTime: number; // milisegundos
    algorithm: string;
  };
  
  // Session Configuration
  session: {
    timeout: number; // minutos
    maxConcurrentSessions: number;
    requireReauth: boolean;
  };
  
  // HTTPS Configuration
  https: {
    required: boolean;
    hsts: boolean;
    hstsMaxAge: number; // segundos
  };
  
  // Content Security Policy
  csp: {
    enabled: boolean;
    directives: {
      defaultSrc: string[];
      scriptSrc: string[];
      styleSrc: string[];
      imgSrc: string[];
      connectSrc: string[];
      fontSrc: string[];
      objectSrc: string[];
      mediaSrc: string[];
      frameSrc: string[];
    };
  };
  
  // Rate Limiting
  rateLimit: {
    enabled: boolean;
    windowMs: number; // milisegundos
    maxRequests: number;
  };
  
  // Password Policy
  password: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // días
    preventReuse: number; // últimas N contraseñas
  };
  
  // Audit Logging
  audit: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    sensitiveFields: string[];
  };
}

// Configuración para desarrollo
const developmentConfig: SecurityConfig = {
  jwt: {
    accessTokenExpiry: 3600, // 1 hora
    refreshTokenExpiry: 604800, // 7 días
    refreshBufferTime: 300000, // 5 minutos
    algorithm: 'HS256'
  },
  session: {
    timeout: 60, // 1 hora
    maxConcurrentSessions: 5,
    requireReauth: false
  },
  https: {
    required: false, // Permitir HTTP en desarrollo
    hsts: false,
    hstsMaxAge: 0
  },
  csp: {
    enabled: true,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Relajado para desarrollo
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:", "https://icd.who.int", "https://id.who.int"],
      fontSrc: ["'self'", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  rateLimit: {
    enabled: false, // Deshabilitado en desarrollo
    windowMs: 900000, // 15 minutos
    maxRequests: 1000
  },
  password: {
    minLength: 6, // Relajado para desarrollo
    requireUppercase: false,
    requireLowercase: false,
    requireNumbers: false,
    requireSpecialChars: false,
    maxAge: 365, // 1 año
    preventReuse: 3
  },
  audit: {
    enabled: true,
    logLevel: 'debug',
    sensitiveFields: ['password', 'token', 'ssn', 'credit_card']
  }
};

// Configuración para producción
const productionConfig: SecurityConfig = {
  jwt: {
    accessTokenExpiry: 900, // 15 minutos
    refreshTokenExpiry: 86400, // 1 día
    refreshBufferTime: 300000, // 5 minutos
    algorithm: 'RS256'
  },
  session: {
    timeout: 30, // 30 minutos
    maxConcurrentSessions: 2,
    requireReauth: true
  },
  https: {
    required: true,
    hsts: true,
    hstsMaxAge: 31536000 // 1 año
  },
  csp: {
    enabled: true,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"], // Solo para estilos inline necesarios
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https://icd.who.int", "https://id.who.int"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  rateLimit: {
    enabled: true,
    windowMs: 900000, // 15 minutos
    maxRequests: 100
  },
  password: {
    minLength: 12,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAge: 90, // 3 meses
    preventReuse: 12
  },
  audit: {
    enabled: true,
    logLevel: 'info',
    sensitiveFields: ['password', 'token', 'ssn', 'credit_card', 'medical_record']
  }
};

// Configuración para testing
const testConfig: SecurityConfig = {
  ...developmentConfig,
  jwt: {
    ...developmentConfig.jwt,
    accessTokenExpiry: 60, // 1 minuto para tests rápidos
    refreshTokenExpiry: 300 // 5 minutos
  },
  session: {
    ...developmentConfig.session,
    timeout: 5 // 5 minutos
  },
  audit: {
    enabled: false,
    logLevel: 'error',
    sensitiveFields: []
  }
};

// Función para obtener configuración según el entorno
export const getSecurityConfig = (): SecurityConfig => {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
};

// Función para generar CSP header string
export const generateCSPHeader = (config: SecurityConfig): string => {
  const { directives } = config.csp;
  
  const cspParts = Object.entries(directives).map(([directive, sources]) => {
    const kebabDirective = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
    return `${kebabDirective} ${sources.join(' ')}`;
  });
  
  return cspParts.join('; ');
};

// Función para validar configuración de seguridad
export const validateSecurityConfig = (config: SecurityConfig): string[] => {
  const errors: string[] = [];
  
  // Validar JWT
  if (config.jwt.accessTokenExpiry <= 0) {
    errors.push('JWT access token expiry debe ser mayor a 0');
  }
  
  if (config.jwt.refreshTokenExpiry <= config.jwt.accessTokenExpiry) {
    errors.push('JWT refresh token expiry debe ser mayor al access token expiry');
  }
  
  // Validar sesión
  if (config.session.timeout <= 0) {
    errors.push('Session timeout debe ser mayor a 0');
  }
  
  // Validar contraseña
  if (config.password.minLength < 6) {
    errors.push('Password min length debe ser al menos 6');
  }
  
  // Validar HTTPS en producción
  if (process.env.NODE_ENV === 'production' && !config.https.required) {
    errors.push('HTTPS es requerido en producción');
  }
  
  return errors;
};

// Configuración actual
export const securityConfig = getSecurityConfig();

// Validar configuración al cargar
const configErrors = validateSecurityConfig(securityConfig);
if (configErrors.length > 0) {
  console.warn('⚠️ Errores en configuración de seguridad:', configErrors);
}

export default securityConfig;
