import { useState, useEffect, useCallback } from 'react';
import { authServiceAdvanced, User } from '../services/authServiceAdvanced';
import toast from 'react-hot-toast';

interface UseAuthAdvancedReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  confirmPasswordReset: (token: string, newPassword: string) => Promise<void>;
  isTokenExpiringSoon: boolean;
}

export const useAuthAdvanced = (): UseAuthAdvancedReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTokenExpiringSoon, setIsTokenExpiringSoon] = useState(false);

  // Inicializar estado de autenticación
  useEffect(() => {
    const initializeAuth = () => {
      const currentUser = authServiceAdvanced.getCurrentUser();
      const isAuth = authServiceAdvanced.isAuthenticated();
      const isExpiring = authServiceAdvanced.isTokenExpiringSoon();

      setUser(currentUser);
      setIsTokenExpiringSoon(isExpiring);
      setIsLoading(false);

      // Si el token está próximo a expirar, intentar refresh
      if (isAuth && isExpiring) {
        authServiceAdvanced.refreshAccessToken().catch((error) => {
          console.error('Error en refresh inicial:', error);
          logout();
        });
      }
    };

    initializeAuth();
  }, []);

  // Verificar periódicamente el estado del token
  useEffect(() => {
    const checkTokenStatus = () => {
      if (authServiceAdvanced.isAuthenticated()) {
        const isExpiring = authServiceAdvanced.isTokenExpiringSoon();
        setIsTokenExpiringSoon(isExpiring);
      }
    };

    // Verificar cada minuto
    const interval = setInterval(checkTokenStatus, 60000);
    return () => clearInterval(interval);
  }, []);

  const login = useCallback(async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      const loggedUser = await authServiceAdvanced.login(email, password);
      setUser(loggedUser);
      setIsTokenExpiringSoon(false);
      
      toast.success(`¡Bienvenido, ${loggedUser.nombre}!`);
    } catch (error: any) {
      toast.error(error.message || 'Error en el login');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      await authServiceAdvanced.logout();
      setUser(null);
      setIsTokenExpiringSoon(false);
      
      toast.success('Sesión cerrada exitosamente');
    } catch (error: any) {
      console.error('Error en logout:', error);
      // Limpiar estado local incluso si hay error
      setUser(null);
      setIsTokenExpiringSoon(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshToken = useCallback(async (): Promise<void> => {
    try {
      await authServiceAdvanced.refreshAccessToken();
      setIsTokenExpiringSoon(false);
      
      // Actualizar usuario si es necesario
      const currentUser = authServiceAdvanced.getCurrentUser();
      setUser(currentUser);
    } catch (error: any) {
      console.error('Error en refresh token:', error);
      toast.error('Sesión expirada. Por favor, inicia sesión nuevamente.');
      await logout();
      throw error;
    }
  }, [logout]);

  const changePassword = useCallback(async (
    currentPassword: string, 
    newPassword: string
  ): Promise<void> => {
    try {
      await authServiceAdvanced.changePassword(currentPassword, newPassword);
      toast.success('Contraseña cambiada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error cambiando contraseña');
      throw error;
    }
  }, []);

  const requestPasswordReset = useCallback(async (email: string): Promise<void> => {
    try {
      await authServiceAdvanced.requestPasswordReset(email);
      toast.success('Se ha enviado un enlace de recuperación a tu email');
    } catch (error: any) {
      toast.error(error.message || 'Error solicitando recuperación');
      throw error;
    }
  }, []);

  const confirmPasswordReset = useCallback(async (
    token: string, 
    newPassword: string
  ): Promise<void> => {
    try {
      await authServiceAdvanced.confirmPasswordReset(token, newPassword);
      toast.success('Contraseña restablecida exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error restableciendo contraseña');
      throw error;
    }
  }, []);

  return {
    user,
    isAuthenticated: !!user && authServiceAdvanced.isAuthenticated(),
    isLoading,
    login,
    logout,
    refreshToken,
    changePassword,
    requestPasswordReset,
    confirmPasswordReset,
    isTokenExpiringSoon,
  };
};

export default useAuthAdvanced;
