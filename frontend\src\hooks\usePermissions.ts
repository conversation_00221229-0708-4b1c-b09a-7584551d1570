import { useAuth } from '../context/AuthContext';

// Tipos de roles del sistema
export type UserRole =
  | 'SUPER_ADMIN'        // Administrador del sistema (acceso total)
  | 'HOSPITAL_ADMIN'     // Administrador de hospital (tenant)
  | 'ADMIN_FINANCIERO'   // Administrador financiero (contabilidad, nómina, presupuesto)
  | 'DOCTOR'             // Médico
  | 'NURSE'              // Enfermero/a
  | 'PHARMACIST'         // Farmaceuta
  | 'LAB_TECH'           // Técnico de laboratorio
  | 'RECEPTIONIST'       // Recepcionista
  | 'ACCOUNTANT'         // Contador
  | 'VIEWER';            // Solo lectura

// Módulos del sistema
export type SystemModule =
  | 'dashboard'
  | 'pacientes'
  | 'medicos'
  | 'enfermeras'
  | 'farmacia'
  | 'citas'
  | 'consultas'
  | 'urgencias'
  | 'hospitalizaciones'
  | 'quirofanos'
  | 'cirugias'
  | 'laboratorio'
  | 'imagenes-diagnosticas'
  | 'banco-sangre'
  | 'medicamentos'
  | 'dispensaciones'
  | 'inventario'
  | 'activos'
  | 'residuosHospitalarios'
  | 'administracion'
  | 'facturacion'
  | 'contabilidad'
  | 'presupuesto'
  | 'reportes'
  | 'analitica-predictiva'
  | 'dashboard-negocio'
  | 'gestion-ips'
  | 'administracion-negocio'
  | 'proveedores'
  | 'recursos-humanos'
  | 'autorizaciones'
  | 'incidentes-adversos'
  | 'teleconsultas'
  | 'telemedicina'
  | 'historias-clinicas'
  | 'configuraciones'
  | 'admin';

// Acciones posibles
export type Permission = 'read' | 'write' | 'delete' | 'admin';

// Configuración de permisos por rol
const ROLE_PERMISSIONS: Record<UserRole, {
  modules: Partial<Record<SystemModule, Permission[]>>;
  isSystemAdmin: boolean;
  isTenantAdmin: boolean;
}> = {
  SUPER_ADMIN: {
    isSystemAdmin: true,
    isTenantAdmin: false,
    modules: {
      // Acceso completo a todo
      'dashboard': ['read', 'write', 'admin'],
      'pacientes': ['read', 'write', 'delete', 'admin'],
      'medicos': ['read', 'write', 'delete', 'admin'],
      'enfermeras': ['read', 'write', 'delete', 'admin'],
      'farmacia': ['read', 'write', 'delete', 'admin'],
      'citas': ['read', 'write', 'delete', 'admin'],
      'consultas': ['read', 'write', 'delete', 'admin'],
      'urgencias': ['read', 'write', 'delete', 'admin'],
      'hospitalizaciones': ['read', 'write', 'delete', 'admin'],
      'quirofanos': ['read', 'write', 'delete', 'admin'],
      'cirugias': ['read', 'write', 'delete', 'admin'],
      'laboratorio': ['read', 'write', 'delete', 'admin'],
      'imagenes-diagnosticas': ['read', 'write', 'delete', 'admin'],
      'banco-sangre': ['read', 'write', 'delete', 'admin'],
      'medicamentos': ['read', 'write', 'delete', 'admin'],
      'dispensaciones': ['read', 'write', 'delete', 'admin'],
      'inventario': ['read', 'write', 'delete', 'admin'],
      'activos': ['read', 'write', 'delete', 'admin'],
      'residuosHospitalarios': ['read', 'write', 'delete', 'admin'],
      'administracion': ['read', 'write', 'delete', 'admin'],
      'facturacion': ['read', 'write', 'delete', 'admin'],
      'contabilidad': ['read', 'write', 'delete', 'admin'],
      'presupuesto': ['read', 'write', 'delete', 'admin'],
      'reportes': ['read', 'write', 'admin'],
      'analitica-predictiva': ['read', 'write', 'admin'],
      'dashboard-negocio': ['read', 'write', 'admin'], // Solo SUPER_ADMIN
      'gestion-ips': ['read', 'write', 'delete', 'admin'],
      'administracion-negocio': ['read', 'write', 'delete', 'admin'], // Solo SUPER_ADMIN
      'proveedores': ['read', 'write', 'delete', 'admin'],
      'recursos-humanos': ['read', 'write', 'delete', 'admin'],
      'autorizaciones': ['read', 'write', 'delete', 'admin'],
      'incidentes-adversos': ['read', 'write', 'delete', 'admin'],
      'teleconsultas': ['read', 'write', 'delete', 'admin'],
      'telemedicina': ['read', 'write', 'delete', 'admin'],
      'historias-clinicas': ['read', 'write', 'delete', 'admin'],
      'configuraciones': ['read', 'write', 'admin'],
      'admin': ['read', 'write', 'delete', 'admin']
    }
  },
  
  HOSPITAL_ADMIN: {
    isSystemAdmin: false,
    isTenantAdmin: true,
    modules: {
      'dashboard': ['read', 'write'],
      'pacientes': ['read', 'write', 'delete'],
      'medicos': ['read', 'write', 'delete'],
      'enfermeras': ['read', 'write', 'delete'],
      'farmacia': ['read', 'write', 'delete'],
      'citas': ['read', 'write', 'delete'],
      'consultas': ['read', 'write', 'delete'],
      'urgencias': ['read', 'write', 'delete'],
      'hospitalizaciones': ['read', 'write', 'delete'],
      'quirofanos': ['read', 'write', 'delete'],
      'cirugias': ['read', 'write', 'delete'],
      'laboratorio': ['read', 'write', 'delete'],
      'banco-sangre': ['read', 'write', 'delete'],
      'medicamentos': ['read', 'write', 'delete'],
      'dispensaciones': ['read', 'write', 'delete'],
      'inventario': ['read', 'write', 'delete'],
      'activos': ['read', 'write', 'delete'],
      'residuosHospitalarios': ['read', 'write', 'delete'],
      'administracion': ['read'], // Solo lectura para hospital admin
      'facturacion': ['read', 'write', 'delete'],
      'contabilidad': ['read', 'write'],
      'presupuesto': ['read', 'write'],
      'reportes': ['read', 'write'],
      'analitica-predictiva': ['read'],
      // NO tiene acceso a dashboard-negocio ni gestion-ips
      'proveedores': ['read', 'write', 'delete'],
      'recursos-humanos': ['read', 'write', 'delete'],
      'autorizaciones': ['read', 'write'],
      'incidentes-adversos': ['read', 'write', 'delete'],
      'teleconsultas': ['read', 'write', 'delete'],
      'telemedicina': ['read', 'write', 'delete'],
      'historias-clinicas': ['read', 'write', 'delete'],
      'configuraciones': ['read', 'write']
    }
  },

  ADMIN_FINANCIERO: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'pacientes': ['read'], // Solo lectura para datos de facturación
      'medicos': ['read'], // Solo lectura para datos de facturación
      'enfermeras': ['read'], // Solo lectura para datos de facturación
      'farmacia': ['read'], // Solo lectura para costos de medicamentos
      'citas': ['read'], // Solo lectura para datos de facturación
      'facturacion': ['read', 'write', 'delete', 'admin'],
      'contabilidad': ['read', 'write', 'delete', 'admin'],
      'presupuesto': ['read', 'write', 'delete', 'admin'],
      'recursos-humanos': ['read', 'write', 'delete'], // Para gestión de nómina
      'proveedores': ['read', 'write', 'delete'],
      'reportes': ['read', 'write'], // Reportes financieros
      'administracion': ['read'], // Solo lectura
      'inventario': ['read'], // Para control de costos
      'activos': ['read', 'write'], // Gestión de activos fijos
      'configuraciones': ['read'] // Solo lectura de configuraciones
    }
  },

  DOCTOR: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'pacientes': ['read', 'write'],
      'medicos': ['read', 'write'],
      'enfermeras': ['read', 'write'],
      'farmacia': ['read'], // Solo lectura para verificar medicamentos
      'citas': ['read', 'write'],
      'consultas': ['read', 'write'],
      'urgencias': ['read', 'write'],
      'hospitalizaciones': ['read', 'write'],
      'quirofanos': ['read', 'write'],
      'cirugias': ['read', 'write'],
      'laboratorio': ['read', 'write'],
      'imagenes-diagnosticas': ['read', 'write'],
      'banco-sangre': ['read'],
      'medicamentos': ['read'],
      'dispensaciones': ['read'],
      'historias-clinicas': ['read', 'write'],
      'teleconsultas': ['read', 'write'],
      'telemedicina': ['read', 'write'],
      'reportes': ['read']
    }
  },

  NURSE: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'pacientes': ['read', 'write'],
      'medicos': ['read'],
      'enfermeras': ['read', 'write'], // Enfermeras pueden gestionar a sus colegas
      'farmacia': ['read', 'write'], // Enfermeras dispensan medicamentos
      'citas': ['read'],
      'urgencias': ['read', 'write'],
      'hospitalizaciones': ['read', 'write'],
      'medicamentos': ['read'],
      'dispensaciones': ['read', 'write'],
      'historias-clinicas': ['read']
    }
  },

  PHARMACIST: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'medicamentos': ['read', 'write', 'delete'],
      'dispensaciones': ['read', 'write', 'delete'],
      'inventario': ['read', 'write'],
      'pacientes': ['read']
    }
  },

  LAB_TECH: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'laboratorio': ['read', 'write'],
      'imagenes-diagnosticas': ['read', 'write'],
      'banco-sangre': ['read', 'write'],
      'pacientes': ['read']
    }
  },

  RECEPTIONIST: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'pacientes': ['read', 'write'],
      'medicos': ['read'],
      'enfermeras': ['read'], // Solo lectura para coordinar citas
      'farmacia': ['read'], // Solo lectura para verificar disponibilidad
      'citas': ['read', 'write', 'delete']
    }
  },

  ACCOUNTANT: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'facturacion': ['read', 'write'],
      'contabilidad': ['read', 'write'],
      'presupuesto': ['read', 'write'],
      'reportes': ['read'],
      'proveedores': ['read']
    }
  },

  VIEWER: {
    isSystemAdmin: false,
    isTenantAdmin: false,
    modules: {
      'dashboard': ['read'],
      'reportes': ['read']
    }
  }
};

export const usePermissions = () => {
  const { user } = useAuth();

  // Para desarrollo: usar SUPER_ADMIN por defecto si no hay usuario (para ver todos los módulos)
  const userRole = (user?.rol as UserRole) || (user?.role as UserRole) || 'SUPER_ADMIN';

  // Debug: mostrar información del usuario
  console.log('usePermissions - Usuario actual:', {
    user,
    userRole,
    userRolFromUser: user?.rol,
    userRoleFromUser: user?.role
  });

  // Validar que el rol existe en ROLE_PERMISSIONS
  const roleConfig = ROLE_PERMISSIONS[userRole];

  // Si no se encuentra la configuración del rol, usar SUPER_ADMIN como fallback
  const safeRoleConfig = roleConfig || ROLE_PERMISSIONS['SUPER_ADMIN'];

  console.log('usePermissions - Debug:', {
    user,
    userRole,
    roleConfig: !!roleConfig,
    safeRoleConfig: !!safeRoleConfig
  });

  // Verificar si el usuario tiene acceso a un módulo
  const hasModuleAccess = (module: SystemModule): boolean => {
    const modulePermissions = safeRoleConfig.modules[module];
    return modulePermissions !== undefined && modulePermissions.length > 0;
  };

  // Verificar si el usuario tiene un permiso específico en un módulo
  const hasPermission = (module: SystemModule, permission: Permission): boolean => {
    const modulePermissions = safeRoleConfig.modules[module];
    return modulePermissions?.includes(permission) || false;
  };

  // Verificar si es administrador del sistema
  const isSystemAdmin = (): boolean => {
    const result = safeRoleConfig.isSystemAdmin;
    console.log('isSystemAdmin - Verificando:', {
      userRole,
      safeRoleConfig: safeRoleConfig,
      isSystemAdmin: result
    });
    return result;
  };

  // Verificar si es administrador de tenant
  const isTenantAdmin = (): boolean => {
    return safeRoleConfig.isTenantAdmin;
  };

  // Obtener módulos accesibles para el usuario
  const getAccessibleModules = (): SystemModule[] => {
    return Object.keys(safeRoleConfig.modules) as SystemModule[];
  };

  // Verificar si puede ver módulos de administración del sistema
  const canViewSystemAdminModules = (): boolean => {
    return isSystemAdmin();
  };

  // Filtrar elementos del menú según permisos
  const filterMenuItems = (menuItems: any[]): any[] => {
    return menuItems.filter(category => {
      // Filtrar items dentro de cada categoría
      const filteredItems = category.items?.filter((item: any) => {
        const moduleName = item.path?.replace('/', '') as SystemModule;

        // Módulos exclusivos para SUPER_ADMIN
        if (['dashboard-negocio', 'gestion-ips'].includes(moduleName)) {
          return isSystemAdmin();
        }

        return hasModuleAccess(moduleName);
      });

      // Solo mostrar la categoría si tiene items accesibles
      return filteredItems && filteredItems.length > 0;
    }).map(category => ({
      ...category,
      items: category.items?.filter((item: any) => {
        const moduleName = item.path?.replace('/', '') as SystemModule;

        // Módulos exclusivos para SUPER_ADMIN
        if (['dashboard-negocio', 'gestion-ips', 'administracion-negocio'].includes(moduleName)) {
          return isSystemAdmin();
        }

        return hasModuleAccess(moduleName);
      })
    }));
  };

  return {
    userRole,
    hasModuleAccess,
    hasPermission,
    isSystemAdmin,
    isTenantAdmin,
    getAccessibleModules,
    canViewSystemAdminModules,
    filterMenuItems
  };
};

export default usePermissions;
