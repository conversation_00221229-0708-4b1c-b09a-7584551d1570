import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';

interface ServiceWorkerState {
  isSupported: boolean;
  isRegistered: boolean;
  isUpdateAvailable: boolean;
  isOffline: boolean;
  registration: ServiceWorkerRegistration | null;
  error: string | null;
}

interface UseServiceWorkerReturn extends ServiceWorkerState {
  register: () => Promise<void>;
  unregister: () => Promise<void>;
  update: () => Promise<void>;
  skipWaiting: () => void;
  clearCache: () => Promise<void>;
  cacheUrls: (urls: string[]) => Promise<void>;
}

export const useServiceWorker = (swUrl: string = '/sw.js'): UseServiceWorkerReturn => {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: 'serviceWorker' in navigator,
    isRegistered: false,
    isUpdateAvailable: false,
    isOffline: !navigator.onLine,
    registration: null,
    error: null,
  });

  // Detectar cambios en el estado de conexión
  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOffline: false }));
      toast.success('Conexión restaurada');
    };

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOffline: true }));
      toast.error('Sin conexión a internet. Trabajando en modo offline.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Registrar Service Worker
  const register = useCallback(async (): Promise<void> => {
    if (!state.isSupported) {
      const error = 'Service Workers no son soportados en este navegador';
      setState(prev => ({ ...prev, error }));
      throw new Error(error);
    }

    try {
      console.log('[SW] Registrando Service Worker...');
      
      const registration = await navigator.serviceWorker.register(swUrl, {
        scope: '/',
        updateViaCache: 'none'
      });

      setState(prev => ({
        ...prev,
        isRegistered: true,
        registration,
        error: null
      }));

      // Configurar listeners para el Service Worker
      setupServiceWorkerListeners(registration);

      console.log('[SW] Service Worker registrado exitosamente');
      toast.success('Aplicación lista para trabajar offline');

    } catch (error: any) {
      const errorMessage = `Error registrando Service Worker: ${error.message}`;
      setState(prev => ({ ...prev, error: errorMessage }));
      console.error('[SW]', errorMessage);
      throw error;
    }
  }, [swUrl, state.isSupported]);

  // Configurar listeners del Service Worker
  const setupServiceWorkerListeners = useCallback((registration: ServiceWorkerRegistration) => {
    // Listener para actualizaciones
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (!newWorker) return;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          setState(prev => ({ ...prev, isUpdateAvailable: true }));
          toast('Nueva versión disponible', {
            duration: 0,
            icon: '🔄',
          });
        }
      });
    });

    // Listener para mensajes del Service Worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, version } = event.data;

      switch (type) {
        case 'UPDATE_AVAILABLE':
          setState(prev => ({ ...prev, isUpdateAvailable: true }));
          toast(`Nueva versión ${version} disponible`, {
            duration: 0,
            icon: '🆕',
          });
          break;

        case 'CACHE_UPDATED':
          toast.success('Contenido actualizado');
          break;

        case 'OFFLINE_READY':
          toast.success('Aplicación lista para uso offline');
          break;
      }
    });

    // Verificar actualizaciones periódicamente
    setInterval(() => {
      registration.update();
    }, 60000); // Cada minuto

  }, []);

  // Desregistrar Service Worker
  const unregister = useCallback(async (): Promise<void> => {
    if (!state.registration) {
      throw new Error('No hay Service Worker registrado');
    }

    try {
      const result = await state.registration.unregister();
      
      if (result) {
        setState(prev => ({
          ...prev,
          isRegistered: false,
          registration: null,
          isUpdateAvailable: false
        }));
        
        console.log('[SW] Service Worker desregistrado');
        toast.success('Service Worker desregistrado');
      }
    } catch (error: any) {
      const errorMessage = `Error desregistrando Service Worker: ${error.message}`;
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, [state.registration]);

  // Actualizar Service Worker
  const update = useCallback(async (): Promise<void> => {
    if (!state.registration) {
      throw new Error('No hay Service Worker registrado');
    }

    try {
      await state.registration.update();
      console.log('[SW] Verificando actualizaciones...');
    } catch (error: any) {
      console.error('[SW] Error verificando actualizaciones:', error);
      throw error;
    }
  }, [state.registration]);

  // Saltar espera y activar nueva versión
  const skipWaiting = useCallback((): void => {
    if (!state.registration?.waiting) {
      return;
    }

    state.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    
    // Recargar página después de activar
    state.registration.waiting.addEventListener('statechange', (event) => {
      const target = event.target as ServiceWorker;
      if (target.state === 'activated') {
        window.location.reload();
      }
    });

    setState(prev => ({ ...prev, isUpdateAvailable: false }));
  }, [state.registration]);

  // Limpiar cache
  const clearCache = useCallback(async (): Promise<void> => {
    if (!state.registration?.active) {
      throw new Error('No hay Service Worker activo');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        if (event.data.success) {
          toast.success('Cache limpiado exitosamente');
          resolve();
        } else {
          reject(new Error('Error limpiando cache'));
        }
      };

      state.registration.active.postMessage(
        { type: 'CLEAR_CACHE' },
        [messageChannel.port2]
      );
    });
  }, [state.registration]);

  // Cachear URLs específicas
  const cacheUrls = useCallback(async (urls: string[]): Promise<void> => {
    if (!state.registration?.active) {
      throw new Error('No hay Service Worker activo');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        if (event.data.success) {
          toast.success(`${urls.length} URLs cacheadas`);
          resolve();
        } else {
          reject(new Error('Error cacheando URLs'));
        }
      };

      state.registration.active.postMessage(
        { type: 'CACHE_URLS', payload: { urls } },
        [messageChannel.port2]
      );
    });
  }, [state.registration]);

  // Auto-registrar en producción
  useEffect(() => {
    if (process.env.NODE_ENV === 'production' && state.isSupported && !state.isRegistered) {
      register().catch(console.error);
    }
  }, [register, state.isSupported, state.isRegistered]);

  return {
    ...state,
    register,
    unregister,
    update,
    skipWaiting,
    clearCache,
    cacheUrls,
  };
};

export default useServiceWorker;
