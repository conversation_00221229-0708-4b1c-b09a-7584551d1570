import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/Card';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { 
  Building2, 
  CreditCard, 
  Bell, 
  BarChart3, 
  Settings, 
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

// Componentes de pestañas
import DashboardNegocio from './components/DashboardNegocio';
import GestionPlanes from './components/GestionPlanes';
import GestionTenants from './components/GestionTenants';
import GestionPagos from './components/GestionPagos';
import GestionNotificaciones from './components/GestionNotificaciones';
import ConfiguracionNegocio from './components/ConfiguracionNegocio';

const AdministracionNegocio: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Datos de resumen para las tarjetas superiores
  const resumenMetricas = {
    tenants_activos: 1,
    tenants_prueba: 1,
    tenants_suspendidos: 1,
    ingresos_mes: 400000,
    conversion_rate: 66.7,
    alertas_pendientes: 1
  };

  const getEstadoBadge = (estado: string) => {
    const estados = {
      activo: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      prueba: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      suspendido: { color: 'bg-red-100 text-red-800', icon: XCircle },
      vencido: { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle }
    };
    
    const config = estados[estado as keyof typeof estados] || estados.activo;
    const IconComponent = config.icon;
    
    return (
      <Badge className={config.color}>
        <IconComponent className="w-3 h-3 mr-1" />
        {estado.charAt(0).toUpperCase() + estado.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building2 className="w-8 h-8 text-blue-600" />
              Administración de Negocio SaaS
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de suscripciones, planes y métricas de negocio
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Configuración
            </Button>
          </div>
        </div>

        {/* Tarjetas de métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tenants Activos</p>
                  <p className="text-2xl font-bold text-green-600">{resumenMetricas.tenants_activos}</p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Prueba</p>
                  <p className="text-2xl font-bold text-yellow-600">{resumenMetricas.tenants_prueba}</p>
                </div>
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Suspendidos</p>
                  <p className="text-2xl font-bold text-red-600">{resumenMetricas.tenants_suspendidos}</p>
                </div>
                <div className="p-2 bg-red-100 rounded-lg">
                  <XCircle className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Mes</p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatCurrency(resumenMetricas.ingresos_mes)}
                  </p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversión</p>
                  <p className="text-2xl font-bold text-purple-600">{resumenMetricas.conversion_rate}%</p>
                </div>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alertas</p>
                  <p className="text-2xl font-bold text-orange-600">{resumenMetricas.alertas_pendientes}</p>
                </div>
                <div className="p-2 bg-orange-100 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contenido principal con pestañas */}
        <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
          <CardContent className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-6 mb-6">
                <TabsTrigger value="dashboard" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Dashboard
                </TabsTrigger>
                <TabsTrigger value="planes" className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Planes
                </TabsTrigger>
                <TabsTrigger value="tenants" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Tenants
                </TabsTrigger>
                <TabsTrigger value="pagos" className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Pagos
                </TabsTrigger>
                <TabsTrigger value="notificaciones" className="flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Notificaciones
                </TabsTrigger>
                <TabsTrigger value="configuracion" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configuración
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard" className="space-y-6">
                <DashboardNegocio />
              </TabsContent>

              <TabsContent value="planes" className="space-y-6">
                <GestionPlanes />
              </TabsContent>

              <TabsContent value="tenants" className="space-y-6">
                <GestionTenants />
              </TabsContent>

              <TabsContent value="pagos" className="space-y-6">
                <GestionPagos />
              </TabsContent>

              <TabsContent value="notificaciones" className="space-y-6">
                <GestionNotificaciones />
              </TabsContent>

              <TabsContent value="configuracion" className="space-y-6">
                <ConfiguracionNegocio />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdministracionNegocio;
