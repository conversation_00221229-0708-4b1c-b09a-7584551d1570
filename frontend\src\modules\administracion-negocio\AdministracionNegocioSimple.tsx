import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/Card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { 
  Building2, 
  CreditCard, 
  Bell, 
  BarChart3, 
  Settings, 
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

const AdministracionNegocioSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Datos de ejemplo sin React Query
  const resumenMetricas = {
    tenants_activos: 1,
    tenants_prueba: 1,
    tenants_suspendidos: 1,
    ingresos_mes: 400000,
    conversion_rate: 66.7,
    alertas_pendientes: 1
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building2 className="w-8 h-8 text-blue-600" />
              Administración de Negocio SaaS
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de suscripciones, planes y métricas de negocio
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Configuración
            </Button>
          </div>
        </div>

        {/* Tarjetas de métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tenants Activos</p>
                  <p className="text-2xl font-bold text-green-700">{resumenMetricas.tenants_activos}</p>
                </div>
                <div className="p-2 bg-green-50 rounded-lg border border-green-100">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Prueba</p>
                  <p className="text-2xl font-bold text-amber-700">{resumenMetricas.tenants_prueba}</p>
                </div>
                <div className="p-2 bg-amber-50 rounded-lg border border-amber-100">
                  <Clock className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Suspendidos</p>
                  <p className="text-2xl font-bold text-red-700">{resumenMetricas.tenants_suspendidos}</p>
                </div>
                <div className="p-2 bg-red-50 rounded-lg border border-red-100">
                  <XCircle className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Mes</p>
                  <p className="text-xl font-bold text-blue-700">
                    {formatCurrency(resumenMetricas.ingresos_mes)}
                  </p>
                </div>
                <div className="p-2 bg-blue-50 rounded-lg border border-blue-100">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversión</p>
                  <p className="text-2xl font-bold text-purple-700">{resumenMetricas.conversion_rate}%</p>
                </div>
                <div className="p-2 bg-purple-50 rounded-lg border border-purple-100">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alertas</p>
                  <p className="text-2xl font-bold text-orange-700">{resumenMetricas.alertas_pendientes}</p>
                </div>
                <div className="p-2 bg-orange-50 rounded-lg border border-orange-100">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contenido principal con pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-6 mb-6">
                <TabsTrigger value="dashboard" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Dashboard
                </TabsTrigger>
                <TabsTrigger value="planes" className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Planes
                </TabsTrigger>
                <TabsTrigger value="tenants" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Tenants
                </TabsTrigger>
                <TabsTrigger value="pagos" className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Pagos
                </TabsTrigger>
                <TabsTrigger value="notificaciones" className="flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Notificaciones
                </TabsTrigger>
                <TabsTrigger value="configuracion" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configuración
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard" className="space-y-6">
                {/* Métricas Detalladas */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card className="bg-white/90 backdrop-blur-sm border border-blue-100 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg text-blue-700">Crecimiento Mensual</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-blue-900 mb-2">+15%</div>
                      <p className="text-sm text-gray-600">Nuevos tenants este mes</p>
                      <div className="mt-4 bg-blue-50 rounded-lg p-3">
                        <div className="flex justify-between text-sm">
                          <span>Meta: 20%</span>
                          <span>75% completado</span>
                        </div>
                        <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{width: '75%'}}></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-green-100 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg text-green-700">Retención de Clientes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-green-900 mb-2">94%</div>
                      <p className="text-sm text-gray-600">Tasa de retención mensual</p>
                      <div className="mt-4 bg-green-50 rounded-lg p-3">
                        <div className="flex justify-between text-sm">
                          <span>Meta: 90%</span>
                          <span>Superado ✅</span>
                        </div>
                        <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{width: '94%'}}></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-purple-100 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg text-purple-700">Valor Promedio</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-purple-900 mb-2">$450.000</div>
                      <p className="text-sm text-gray-600">Ingreso promedio por tenant</p>
                      <div className="mt-4 bg-purple-50 rounded-lg p-3">
                        <div className="flex justify-between text-sm">
                          <span>Mes anterior: $420.000</span>
                          <span className="text-green-600">+7%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Alertas Recientes */}
                <Card className="bg-white/90 backdrop-blur-sm border border-orange-100 shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg text-orange-700 flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5" />
                      Alertas Recientes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-100">
                        <div className="flex items-center gap-3">
                          <Clock className="w-5 h-5 text-orange-600" />
                          <div>
                            <p className="font-medium text-gray-900">Hospital San José</p>
                            <p className="text-sm text-gray-600">Suscripción vence en 3 días</p>
                          </div>
                        </div>
                        <Badge variant="destructive">Urgente</Badge>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                        <div className="flex items-center gap-3">
                          <AlertTriangle className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium text-gray-900">Clínica del Norte</p>
                            <p className="text-sm text-gray-600">Pago pendiente desde hace 5 días</p>
                          </div>
                        </div>
                        <Badge variant="secondary">Pendiente</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="planes" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Planes de Suscripción</h3>
                  <Button className="bg-green-600 hover:bg-green-700">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Nuevo Plan
                  </Button>
                </div>

                {/* Planes Disponibles */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="bg-white/90 backdrop-blur-sm border border-blue-200 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-center text-blue-700">Plan Básico</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-blue-900">$200.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Hasta 50 usuarios
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Módulos básicos
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte por email
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          5GB almacenamiento
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">2 tenants activos</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-purple-200 shadow-sm ring-2 ring-purple-200">
                    <CardHeader>
                      <div className="text-center">
                        <Badge className="bg-purple-100 text-purple-800 mb-2">Más Popular</Badge>
                      </div>
                      <CardTitle className="text-center text-purple-700">Plan Premium</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-purple-900">$450.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Hasta 200 usuarios
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Todos los módulos
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte prioritario
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          50GB almacenamiento
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Reportes avanzados
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">5 tenants activos</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gold-200 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-center text-yellow-700">Plan Enterprise</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-yellow-900">$800.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Usuarios ilimitados
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Módulos personalizados
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte 24/7
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Almacenamiento ilimitado
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          API personalizada
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">1 tenant activo</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="tenants" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Gestión de Tenants</h3>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Users className="w-4 h-4 mr-2" />
                    Nuevo Tenant
                  </Button>
                </div>

                {/* Lista de Tenants */}
                <div className="space-y-4">
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-green-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Hospital San José</h4>
                            <p className="text-sm text-gray-600">Plan Premium • 150 usuarios</p>
                            <p className="text-xs text-gray-500">Activo desde: Enero 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-green-100 text-green-800">Activo</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$450.000/mes</p>
                          <p className="text-xs text-gray-500">Vence: 15 Dic 2024</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-yellow-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Clínica del Norte</h4>
                            <p className="text-sm text-gray-600">Plan Básico • 50 usuarios</p>
                            <p className="text-xs text-gray-500">Activo desde: Marzo 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-yellow-100 text-yellow-800">Prueba</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$200.000/mes</p>
                          <p className="text-xs text-gray-500">Vence: 20 Dic 2024</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-red-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Centro Médico Sur</h4>
                            <p className="text-sm text-gray-600">Plan Premium • 200 usuarios</p>
                            <p className="text-xs text-gray-500">Suspendido desde: Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-red-100 text-red-800">Suspendido</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$600.000/mes</p>
                          <p className="text-xs text-red-500">Pago vencido</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="pagos" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Gestión de Pagos</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Exportar
                    </Button>
                    <Button className="bg-blue-600 hover:bg-blue-700" size="sm">
                      Registrar Pago
                    </Button>
                  </div>
                </div>

                {/* Resumen de Pagos */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <Card className="bg-green-50 border-green-200">
                    <CardContent className="p-4 text-center">
                      <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Pagos Recibidos</p>
                      <p className="text-xl font-bold text-green-700">$1.250.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-yellow-50 border-yellow-200">
                    <CardContent className="p-4 text-center">
                      <Clock className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Pendientes</p>
                      <p className="text-xl font-bold text-yellow-700">$600.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-red-50 border-red-200">
                    <CardContent className="p-4 text-center">
                      <XCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Vencidos</p>
                      <p className="text-xl font-bold text-red-700">$200.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-blue-50 border-blue-200">
                    <CardContent className="p-4 text-center">
                      <BarChart3 className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Total Mes</p>
                      <p className="text-xl font-bold text-blue-700">$2.050.000</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Transacciones Recientes */}
                <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardHeader>
                    <CardTitle>Transacciones Recientes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-gray-900">Hospital San José</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Premium</p>
                            <p className="text-xs text-gray-500">15 Nov 2024, 10:30 AM</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-700">+$450.000</p>
                          <Badge className="bg-green-100 text-green-800">Completado</Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                        <div className="flex items-center gap-3">
                          <Clock className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium text-gray-900">Clínica del Norte</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Básico</p>
                            <p className="text-xs text-gray-500">Vence: 20 Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-yellow-700">$200.000</p>
                          <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                        <div className="flex items-center gap-3">
                          <XCircle className="w-5 h-5 text-red-600" />
                          <div>
                            <p className="font-medium text-gray-900">Centro Médico Sur</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Enterprise</p>
                            <p className="text-xs text-gray-500">Vencido desde: 1 Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-red-700">$800.000</p>
                          <Badge className="bg-red-100 text-red-800">Vencido</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notificaciones" className="space-y-6">
                <div className="text-center py-12">
                  <Bell className="w-16 h-16 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestión de Notificaciones</h3>
                  <p className="text-gray-600">
                    Sistema de notificaciones automáticas
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="configuracion" className="space-y-6">
                <div className="text-center py-12">
                  <Settings className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Configuración</h3>
                  <p className="text-gray-600">
                    Configuración del sistema SaaS
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdministracionNegocioSimple;
