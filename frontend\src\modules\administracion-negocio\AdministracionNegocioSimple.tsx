import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/Card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { 
  Building2, 
  CreditCard, 
  Bell, 
  BarChart3, 
  Settings, 
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

const AdministracionNegocioSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Datos de ejemplo sin React Query
  const resumenMetricas = {
    tenants_activos: 1,
    tenants_prueba: 1,
    tenants_suspendidos: 1,
    ingresos_mes: 400000,
    conversion_rate: 66.7,
    alertas_pendientes: 1
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building2 className="w-8 h-8 text-blue-600" />
              Administración de Negocio SaaS
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de suscripciones, planes y métricas de negocio
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Configuración
            </Button>
          </div>
        </div>

        {/* Tarjetas de métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tenants Activos</p>
                  <p className="text-2xl font-bold text-green-700">{resumenMetricas.tenants_activos}</p>
                </div>
                <div className="p-2 bg-green-50 rounded-lg border border-green-100">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Prueba</p>
                  <p className="text-2xl font-bold text-amber-700">{resumenMetricas.tenants_prueba}</p>
                </div>
                <div className="p-2 bg-amber-50 rounded-lg border border-amber-100">
                  <Clock className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Suspendidos</p>
                  <p className="text-2xl font-bold text-red-700">{resumenMetricas.tenants_suspendidos}</p>
                </div>
                <div className="p-2 bg-red-50 rounded-lg border border-red-100">
                  <XCircle className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Mes</p>
                  <p className="text-xl font-bold text-blue-700">
                    {formatCurrency(resumenMetricas.ingresos_mes)}
                  </p>
                </div>
                <div className="p-2 bg-blue-50 rounded-lg border border-blue-100">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversión</p>
                  <p className="text-2xl font-bold text-purple-700">{resumenMetricas.conversion_rate}%</p>
                </div>
                <div className="p-2 bg-purple-50 rounded-lg border border-purple-100">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alertas</p>
                  <p className="text-2xl font-bold text-orange-700">{resumenMetricas.alertas_pendientes}</p>
                </div>
                <div className="p-2 bg-orange-50 rounded-lg border border-orange-100">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contenido principal con pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-6 mb-6">
                <TabsTrigger value="dashboard" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Dashboard
                </TabsTrigger>
                <TabsTrigger value="planes" className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Planes
                </TabsTrigger>
                <TabsTrigger value="tenants" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Tenants
                </TabsTrigger>
                <TabsTrigger value="pagos" className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Pagos
                </TabsTrigger>
                <TabsTrigger value="notificaciones" className="flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Notificaciones
                </TabsTrigger>
                <TabsTrigger value="configuracion" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configuración
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard" className="space-y-6">
                <div className="text-center py-12">
                  <BarChart3 className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Dashboard de Métricas</h3>
                  <p className="text-gray-600">
                    Aquí se mostrarán las métricas detalladas del negocio SaaS
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="planes" className="space-y-6">
                <div className="text-center py-12">
                  <CreditCard className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestión de Planes</h3>
                  <p className="text-gray-600">
                    Administración de planes de suscripción y precios
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="tenants" className="space-y-6">
                <div className="text-center py-12">
                  <Users className="w-16 h-16 text-purple-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestión de Tenants</h3>
                  <p className="text-gray-600">
                    Administración de hospitales y sus suscripciones
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="pagos" className="space-y-6">
                <div className="text-center py-12">
                  <DollarSign className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestión de Pagos</h3>
                  <p className="text-gray-600">
                    Control de pagos y transacciones
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="notificaciones" className="space-y-6">
                <div className="text-center py-12">
                  <Bell className="w-16 h-16 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestión de Notificaciones</h3>
                  <p className="text-gray-600">
                    Sistema de notificaciones automáticas
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="configuracion" className="space-y-6">
                <div className="text-center py-12">
                  <Settings className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Configuración</h3>
                  <p className="text-gray-600">
                    Configuración del sistema SaaS
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdministracionNegocioSimple;
