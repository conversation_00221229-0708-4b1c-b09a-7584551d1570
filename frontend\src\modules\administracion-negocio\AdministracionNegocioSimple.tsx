import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import AuditoriaEventos from './components/AuditoriaEventos';
import GestionEstadosTenant from './components/GestionEstadosTenant';
import MetricasAvanzadas from './components/MetricasAvanzadas';
import SistemaFacturacion from './components/SistemaFacturacion';
import {
  Building2,
  CreditCard,
  Bell,
  BarChart3,
  Settings,
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Activity,
  FileText
} from 'lucide-react';

const AdministracionNegocioSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Datos de ejemplo sin React Query
  const resumenMetricas = {
    tenants_activos: 1,
    tenants_prueba: 1,
    tenants_suspendidos: 1,
    ingresos_mes: 400000,
    conversion_rate: 66.7,
    alertas_pendientes: 1
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building2 className="w-8 h-8 text-blue-600" />
              Administración de Negocio SaaS
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de suscripciones, planes y métricas de negocio
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Configuración
            </Button>
          </div>
        </div>

        {/* Tarjetas de métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tenants Activos</p>
                  <p className="text-2xl font-bold text-green-700">{resumenMetricas.tenants_activos}</p>
                </div>
                <div className="p-2 bg-green-50 rounded-lg border border-green-100">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Prueba</p>
                  <p className="text-2xl font-bold text-amber-700">{resumenMetricas.tenants_prueba}</p>
                </div>
                <div className="p-2 bg-amber-50 rounded-lg border border-amber-100">
                  <Clock className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Suspendidos</p>
                  <p className="text-2xl font-bold text-red-700">{resumenMetricas.tenants_suspendidos}</p>
                </div>
                <div className="p-2 bg-red-50 rounded-lg border border-red-100">
                  <XCircle className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Mes</p>
                  <p className="text-xl font-bold text-blue-700">
                    {formatCurrency(resumenMetricas.ingresos_mes)}
                  </p>
                </div>
                <div className="p-2 bg-blue-50 rounded-lg border border-blue-100">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversión</p>
                  <p className="text-2xl font-bold text-purple-700">{resumenMetricas.conversion_rate}%</p>
                </div>
                <div className="p-2 bg-purple-50 rounded-lg border border-purple-100">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alertas</p>
                  <p className="text-2xl font-bold text-orange-700">{resumenMetricas.alertas_pendientes}</p>
                </div>
                <div className="p-2 bg-orange-50 rounded-lg border border-orange-100">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contenido principal con pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-9 mb-6">
                <TabsTrigger value="dashboard" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Dashboard
                </TabsTrigger>
                <TabsTrigger value="planes" className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Planes
                </TabsTrigger>
                <TabsTrigger value="tenants" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Tenants
                </TabsTrigger>
                <TabsTrigger value="estados" className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Estados
                </TabsTrigger>
                <TabsTrigger value="pagos" className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Pagos
                </TabsTrigger>
                <TabsTrigger value="facturacion" className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Facturación
                </TabsTrigger>
                <TabsTrigger value="notificaciones" className="flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Notificaciones
                </TabsTrigger>
                <TabsTrigger value="auditoria" className="flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Auditoría
                </TabsTrigger>
                <TabsTrigger value="configuracion" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configuración
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard" className="space-y-6">
                <MetricasAvanzadas />
              </TabsContent>

              <TabsContent value="planes" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Planes de Suscripción</h3>
                  <Button className="bg-green-600 hover:bg-green-700">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Nuevo Plan
                  </Button>
                </div>

                {/* Planes Disponibles */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="bg-white/90 backdrop-blur-sm border border-blue-200 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-center text-blue-700">Plan Básico</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-blue-900">$200.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Hasta 50 usuarios
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Módulos básicos
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte por email
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          5GB almacenamiento
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">2 tenants activos</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-purple-200 shadow-sm ring-2 ring-purple-200">
                    <CardHeader>
                      <div className="text-center">
                        <Badge className="bg-purple-100 text-purple-800 mb-2">Más Popular</Badge>
                      </div>
                      <CardTitle className="text-center text-purple-700">Plan Premium</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-purple-900">$450.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Hasta 200 usuarios
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Todos los módulos
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte prioritario
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          50GB almacenamiento
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Reportes avanzados
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">5 tenants activos</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gold-200 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-center text-yellow-700">Plan Enterprise</CardTitle>
                      <div className="text-center">
                        <span className="text-3xl font-bold text-yellow-900">$800.000</span>
                        <span className="text-gray-600">/mes</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Usuarios ilimitados
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Módulos personalizados
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Soporte 24/7
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Almacenamiento ilimitado
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          API personalizada
                        </li>
                      </ul>
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-xs text-gray-500">1 tenant activo</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="estados" className="space-y-6">
                <GestionEstadosTenant />
              </TabsContent>

              <TabsContent value="tenants" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Gestión de Tenants</h3>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Users className="w-4 h-4 mr-2" />
                    Nuevo Tenant
                  </Button>
                </div>

                {/* Lista de Tenants */}
                <div className="space-y-4">
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-green-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Hospital San José</h4>
                            <p className="text-sm text-gray-600">Plan Premium • 150 usuarios</p>
                            <p className="text-xs text-gray-500">Activo desde: Enero 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-green-100 text-green-800">Activo</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$450.000/mes</p>
                          <p className="text-xs text-gray-500">Vence: 15 Dic 2024</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-yellow-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Clínica del Norte</h4>
                            <p className="text-sm text-gray-600">Plan Básico • 50 usuarios</p>
                            <p className="text-xs text-gray-500">Activo desde: Marzo 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-yellow-100 text-yellow-800">Prueba</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$200.000/mes</p>
                          <p className="text-xs text-gray-500">Vence: 20 Dic 2024</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-red-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Centro Médico Sur</h4>
                            <p className="text-sm text-gray-600">Plan Premium • 200 usuarios</p>
                            <p className="text-xs text-gray-500">Suspendido desde: Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-red-100 text-red-800">Suspendido</Badge>
                          <p className="text-sm font-semibold text-gray-900 mt-1">$600.000/mes</p>
                          <p className="text-xs text-red-500">Pago vencido</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="pagos" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Gestión de Pagos</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Exportar
                    </Button>
                    <Button className="bg-blue-600 hover:bg-blue-700" size="sm">
                      Registrar Pago
                    </Button>
                  </div>
                </div>

                {/* Resumen de Pagos */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <Card className="bg-green-50 border-green-200">
                    <CardContent className="p-4 text-center">
                      <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Pagos Recibidos</p>
                      <p className="text-xl font-bold text-green-700">$1.250.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-yellow-50 border-yellow-200">
                    <CardContent className="p-4 text-center">
                      <Clock className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Pendientes</p>
                      <p className="text-xl font-bold text-yellow-700">$600.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-red-50 border-red-200">
                    <CardContent className="p-4 text-center">
                      <XCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Vencidos</p>
                      <p className="text-xl font-bold text-red-700">$200.000</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-blue-50 border-blue-200">
                    <CardContent className="p-4 text-center">
                      <BarChart3 className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Total Mes</p>
                      <p className="text-xl font-bold text-blue-700">$2.050.000</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Transacciones Recientes */}
                <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardHeader>
                    <CardTitle>Transacciones Recientes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-gray-900">Hospital San José</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Premium</p>
                            <p className="text-xs text-gray-500">15 Nov 2024, 10:30 AM</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-700">+$450.000</p>
                          <Badge className="bg-green-100 text-green-800">Completado</Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                        <div className="flex items-center gap-3">
                          <Clock className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium text-gray-900">Clínica del Norte</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Básico</p>
                            <p className="text-xs text-gray-500">Vence: 20 Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-yellow-700">$200.000</p>
                          <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                        <div className="flex items-center gap-3">
                          <XCircle className="w-5 h-5 text-red-600" />
                          <div>
                            <p className="font-medium text-gray-900">Centro Médico Sur</p>
                            <p className="text-sm text-gray-600">Pago mensual - Plan Enterprise</p>
                            <p className="text-xs text-gray-500">Vencido desde: 1 Nov 2024</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-red-700">$800.000</p>
                          <Badge className="bg-red-100 text-red-800">Vencido</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="facturacion" className="space-y-6">
                <SistemaFacturacion />
              </TabsContent>

              <TabsContent value="notificaciones" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Sistema de Notificaciones</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4 mr-2" />
                      Configurar Plantillas
                    </Button>
                    <Button className="bg-orange-600 hover:bg-orange-700" size="sm">
                      <Bell className="w-4 h-4 mr-2" />
                      Enviar Notificación
                    </Button>
                  </div>
                </div>

                {/* Configuración de Notificaciones */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Configuración de Alertas</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">30 días antes del vencimiento</p>
                            <p className="text-sm text-gray-600">Notificación temprana</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-blue-100 text-blue-800">Activa</Badge>
                            <Button variant="outline" size="sm">Editar</Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">15 días antes del vencimiento</p>
                            <p className="text-sm text-gray-600">Recordatorio intermedio</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-yellow-100 text-yellow-800">Activa</Badge>
                            <Button variant="outline" size="sm">Editar</Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">7 días antes del vencimiento</p>
                            <p className="text-sm text-gray-600">Alerta urgente</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-orange-100 text-orange-800">Activa</Badge>
                            <Button variant="outline" size="sm">Editar</Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">1 día antes del vencimiento</p>
                            <p className="text-sm text-gray-600">Última oportunidad</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-red-100 text-red-800">Activa</Badge>
                            <Button variant="outline" size="sm">Editar</Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Estadísticas de Envío</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Notificaciones enviadas hoy</span>
                          <span className="font-semibold text-blue-600">12</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Emails abiertos</span>
                          <span className="font-semibold text-green-600">8 (67%)</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Clicks en enlaces</span>
                          <span className="font-semibold text-purple-600">3 (25%)</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Emails rebotados</span>
                          <span className="font-semibold text-red-600">1 (8%)</span>
                        </div>

                        <div className="pt-4 border-t">
                          <div className="text-center">
                            <p className="text-2xl font-bold text-gray-900">94%</p>
                            <p className="text-sm text-gray-600">Tasa de entrega</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Log de Notificaciones Recientes */}
                <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Notificaciones Recientes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-gray-900">Hospital San José</p>
                            <p className="text-sm text-gray-600">Notificación de vencimiento en 7 días</p>
                            <p className="text-xs text-gray-500">Enviado: Hoy, 09:30 AM • Abierto: Sí</p>
                          </div>
                        </div>
                        <Badge className="bg-green-100 text-green-800">Entregado</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                        <div className="flex items-center gap-3">
                          <Bell className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium text-gray-900">Clínica del Norte</p>
                            <p className="text-sm text-gray-600">Recordatorio de pago pendiente</p>
                            <p className="text-xs text-gray-500">Enviado: Ayer, 02:15 PM • Abierto: No</p>
                          </div>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800">Enviado</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                        <div className="flex items-center gap-3">
                          <Clock className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium text-gray-900">Centro Médico Sur</p>
                            <p className="text-sm text-gray-600">Notificación de suspensión inminente</p>
                            <p className="text-xs text-gray-500">Programado: Mañana, 10:00 AM</p>
                          </div>
                        </div>
                        <Badge className="bg-yellow-100 text-yellow-800">Programado</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                        <div className="flex items-center gap-3">
                          <XCircle className="w-5 h-5 text-red-600" />
                          <div>
                            <p className="font-medium text-gray-900">IPS Salud Total</p>
                            <p className="text-sm text-gray-600">Error en entrega - Email inválido</p>
                            <p className="text-xs text-gray-500">Falló: Hace 2 horas</p>
                          </div>
                        </div>
                        <Badge className="bg-red-100 text-red-800">Error</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="auditoria" className="space-y-6">
                <AuditoriaEventos />
              </TabsContent>

              <TabsContent value="configuracion" className="space-y-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Configuración del Sistema</h3>
                  <Button className="bg-gray-600 hover:bg-gray-700" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Guardar Cambios
                  </Button>
                </div>

                {/* Configuraciones Principales */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Configuración de Periodos */}
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Configuración de Periodos</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Duración del Periodo de Prueba
                        </label>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            defaultValue="30"
                            className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <span className="text-gray-600">días</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Periodo de Gracia
                        </label>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            defaultValue="7"
                            className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <span className="text-gray-600">días después del vencimiento</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Renovación Automática
                        </label>
                        <div className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked className="rounded" />
                          <span className="text-gray-600">Activar renovación automática</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Descuento por Pago Anual
                        </label>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            defaultValue="15"
                            className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <span className="text-gray-600">% de descuento</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Configuración de Métodos de Pago */}
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Métodos de Pago</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CreditCard className="w-5 h-5 text-green-600" />
                            <div>
                              <p className="font-medium text-gray-900">Tarjeta de Crédito</p>
                              <p className="text-sm text-gray-600">Stripe, Wompi, ePayco</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-green-100 text-green-800">Activo</Badge>
                            <Button variant="outline" size="sm">Config</Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Building2 className="w-5 h-5 text-blue-600" />
                            <div>
                              <p className="font-medium text-gray-900">Transferencia Bancaria</p>
                              <p className="text-sm text-gray-600">Validación manual</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-blue-100 text-blue-800">Activo</Badge>
                            <Button variant="outline" size="sm">Config</Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <DollarSign className="w-5 h-5 text-gray-600" />
                            <div>
                              <p className="font-medium text-gray-900">Débito Automático</p>
                              <p className="text-sm text-gray-600">PSE, ACH</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-gray-100 text-gray-800">Inactivo</Badge>
                            <Button variant="outline" size="sm">Config</Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Configuración de Facturación */}
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Facturación Electrónica</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Proveedor de Facturación
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <option>DIAN - Facturación Electrónica</option>
                          <option>Siigo</option>
                          <option>Alegra</option>
                          <option>Facturama</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          NIT de la Empresa
                        </label>
                        <input
                          type="text"
                          defaultValue="900123456-1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Resolución DIAN
                        </label>
                        <input
                          type="text"
                          defaultValue="18764007689817"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-gray-600">Generar facturas automáticamente</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Configuración de Notificaciones */}
                  <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Configuración de Emails</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Remitente
                        </label>
                        <input
                          type="email"
                          defaultValue="<EMAIL>"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Nombre del Remitente
                        </label>
                        <input
                          type="text"
                          defaultValue="Sistema Hipócrates"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Proveedor SMTP
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <option>SendGrid</option>
                          <option>Mailgun</option>
                          <option>Amazon SES</option>
                          <option>SMTP Personalizado</option>
                        </select>
                      </div>

                      <div className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-gray-600">Tracking de apertura de emails</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Configuraciones Avanzadas */}
                <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Configuraciones Avanzadas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Límite de Usuarios por Tenant
                        </label>
                        <input
                          type="number"
                          defaultValue="1000"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Límite de Almacenamiento (GB)
                        </label>
                        <input
                          type="number"
                          defaultValue="100"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Retención de Logs (días)
                        </label>
                        <input
                          type="number"
                          defaultValue="90"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">Modo de Mantenimiento</h4>
                          <p className="text-sm text-gray-600">Suspender temporalmente todos los servicios</p>
                        </div>
                        <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50">
                          Activar Mantenimiento
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdministracionNegocioSimple;
