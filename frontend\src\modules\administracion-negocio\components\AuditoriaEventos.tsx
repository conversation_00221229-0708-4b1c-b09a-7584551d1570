import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import { 
  Shield, 
  Clock, 
  User, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Eye,
  Download,
  Filter,
  Search
} from 'lucide-react';

interface EventoAuditoria {
  id: string;
  timestamp: string;
  tenant_name: string;
  event_type: string;
  description: string;
  user_email?: string;
  ip_address?: string;
  status: 'success' | 'warning' | 'error' | 'info';
  metadata?: any;
}

const AuditoriaEventos: React.FC = () => {
  const [filtroTipo, setFiltroTipo] = useState('todos');
  const [filtroFecha, setFiltroFecha] = useState('7d');

  // Datos de ejemplo para eventos de auditoría
  const eventos: EventoAuditoria[] = [
    {
      id: '1',
      timestamp: '2024-11-15 14:30:25',
      tenant_name: 'Hospital San José',
      event_type: 'PAYMENT_CONFIRMED',
      description: 'Pago confirmado para Plan Premium',
      user_email: '<EMAIL>',
      ip_address: '*************',
      status: 'success',
      metadata: { amount: 450000, plan: 'Premium' }
    },
    {
      id: '2',
      timestamp: '2024-11-15 12:15:10',
      tenant_name: 'Clínica del Norte',
      event_type: 'TRIAL_WARNING_SENT',
      description: 'Notificación de vencimiento enviada (7 días)',
      status: 'info',
      metadata: { days_remaining: 7, notification_type: 'email' }
    },
    {
      id: '3',
      timestamp: '2024-11-15 09:45:33',
      tenant_name: 'Centro Médico Sur',
      event_type: 'TENANT_SUSPENDED',
      description: 'Tenant suspendido por falta de pago',
      user_email: '<EMAIL>',
      status: 'warning',
      metadata: { reason: 'payment_overdue', days_overdue: 15 }
    },
    {
      id: '4',
      timestamp: '2024-11-15 08:20:15',
      tenant_name: 'IPS Salud Total',
      event_type: 'LOGIN_FAILED',
      description: 'Intento de acceso fallido - Tenant suspendido',
      user_email: '<EMAIL>',
      ip_address: '************',
      status: 'error',
      metadata: { attempts: 3, reason: 'tenant_suspended' }
    },
    {
      id: '5',
      timestamp: '2024-11-14 16:55:42',
      tenant_name: 'Hospital San José',
      event_type: 'PLAN_UPGRADED',
      description: 'Plan actualizado de Básico a Premium',
      user_email: '<EMAIL>',
      ip_address: '*************',
      status: 'success',
      metadata: { old_plan: 'Básico', new_plan: 'Premium' }
    },
    {
      id: '6',
      timestamp: '2024-11-14 14:30:18',
      tenant_name: 'Clínica del Norte',
      event_type: 'PAYMENT_FAILED',
      description: 'Pago rechazado - Tarjeta sin fondos',
      user_email: '<EMAIL>',
      status: 'error',
      metadata: { amount: 200000, error_code: 'insufficient_funds' }
    }
  ];

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'PAYMENT_CONFIRMED':
      case 'PAYMENT_FAILED':
        return <CreditCard className="w-5 h-5" />;
      case 'TENANT_SUSPENDED':
      case 'TENANT_ACTIVATED':
        return <Shield className="w-5 h-5" />;
      case 'LOGIN_FAILED':
      case 'LOGIN_SUCCESS':
        return <User className="w-5 h-5" />;
      case 'TRIAL_WARNING_SENT':
        return <AlertTriangle className="w-5 h-5" />;
      default:
        return <Clock className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'info':
        return <Eye className="w-4 h-4 text-blue-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Auditoría y Eventos</h3>
          <p className="text-gray-600">Registro completo de actividad del sistema</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filtros
          </Button>
        </div>
      </div>

      {/* Filtros rápidos */}
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-gray-500" />
              <input 
                type="text" 
                placeholder="Buscar eventos..." 
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select 
              value={filtroTipo} 
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todos">Todos los eventos</option>
              <option value="PAYMENT_CONFIRMED">Pagos confirmados</option>
              <option value="PAYMENT_FAILED">Pagos fallidos</option>
              <option value="TENANT_SUSPENDED">Suspensiones</option>
              <option value="TRIAL_WARNING_SENT">Notificaciones</option>
              <option value="LOGIN_FAILED">Accesos fallidos</option>
            </select>

            <select 
              value={filtroFecha} 
              onChange={(e) => setFiltroFecha(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1d">Último día</option>
              <option value="7d">Últimos 7 días</option>
              <option value="30d">Últimos 30 días</option>
              <option value="90d">Últimos 90 días</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4 text-center">
            <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Eventos Exitosos</p>
            <p className="text-xl font-bold text-green-700">156</p>
          </CardContent>
        </Card>
        
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4 text-center">
            <AlertTriangle className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Advertencias</p>
            <p className="text-xl font-bold text-yellow-700">23</p>
          </CardContent>
        </Card>
        
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4 text-center">
            <XCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Errores</p>
            <p className="text-xl font-bold text-red-700">8</p>
          </CardContent>
        </Card>
        
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4 text-center">
            <Eye className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Total Eventos</p>
            <p className="text-xl font-bold text-blue-700">187</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de eventos */}
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle>Eventos Recientes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {eventos.map((evento) => (
              <div key={evento.id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className={`p-2 rounded-lg ${getStatusColor(evento.status)}`}>
                  {getEventIcon(evento.event_type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{evento.tenant_name}</h4>
                      <p className="text-sm text-gray-600">{evento.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span>{evento.timestamp}</span>
                        {evento.user_email && <span>Usuario: {evento.user_email}</span>}
                        {evento.ip_address && <span>IP: {evento.ip_address}</span>}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(evento.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(evento.status)}
                          {evento.status}
                        </div>
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Eye className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  
                  {evento.metadata && (
                    <div className="mt-2 p-2 bg-white rounded border text-xs">
                      <pre className="text-gray-600 overflow-x-auto">
                        {JSON.stringify(evento.metadata, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuditoriaEventos;
