import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { But<PERSON> } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { Switch } from '../../../components/ui/switch';
import { 
  Settings, 
  Save, 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { ConfiguracionNotificaciones } from '../../../types/saasNegocio';

const ConfiguracionNegocio: React.FC = () => {
  const [activeSection, setActiveSection] = useState('notificaciones');
  const queryClient = useQueryClient();

  const { data: configuracion, isLoading } = useQuery({
    queryKey: ['configuracion-notificaciones'],
    queryFn: () => saasNegocioService.obtenerConfiguracionNotificaciones()
  });

  const [formData, setFormData] = useState<ConfiguracionNotificaciones>({
    trial_warning_days: [30, 15, 7, 1],
    subscription_warning_days: [7, 1],
    grace_period_days: 7,
    email_templates: {},
    canales_activos: ['email', 'in_app']
  });

  React.useEffect(() => {
    if (configuracion) {
      setFormData(configuracion);
    }
  }, [configuracion]);

  const updateConfigMutation = useMutation({
    mutationFn: (config: ConfiguracionNotificaciones) => 
      saasNegocioService.actualizarConfiguracionNotificaciones(config),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['configuracion-notificaciones'] });
      alert('Configuración actualizada exitosamente');
    },
    onError: (error) => {
      console.error('Error al actualizar configuración:', error);
      alert('Error al actualizar la configuración');
    }
  });

  const handleSave = () => {
    updateConfigMutation.mutate(formData);
  };

  const handleCanalToggle = (canal: string) => {
    setFormData(prev => ({
      ...prev,
      canales_activos: prev.canales_activos.includes(canal)
        ? prev.canales_activos.filter(c => c !== canal)
        : [...prev.canales_activos, canal]
    }));
  };

  const handleTrialDaysChange = (index: number, value: string) => {
    const newDays = [...formData.trial_warning_days];
    newDays[index] = parseInt(value) || 0;
    setFormData(prev => ({
      ...prev,
      trial_warning_days: newDays.sort((a, b) => b - a)
    }));
  };

  const handleSubscriptionDaysChange = (index: number, value: string) => {
    const newDays = [...formData.subscription_warning_days];
    newDays[index] = parseInt(value) || 0;
    setFormData(prev => ({
      ...prev,
      subscription_warning_days: newDays.sort((a, b) => b - a)
    }));
  };

  const handleTemplateChange = (templateKey: string, field: 'asunto' | 'contenido', value: string) => {
    setFormData(prev => ({
      ...prev,
      email_templates: {
        ...prev.email_templates,
        [templateKey]: {
          ...prev.email_templates[templateKey],
          [field]: value
        }
      }
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Configuración de Negocio</h2>
          <p className="text-gray-600">Configure los parámetros del sistema SaaS</p>
        </div>
        <Button 
          onClick={handleSave} 
          disabled={updateConfigMutation.isPending}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Save className="w-4 h-4 mr-2" />
          {updateConfigMutation.isPending ? 'Guardando...' : 'Guardar Cambios'}
        </Button>
      </div>

      {/* Navegación de secciones */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4">
            <Button
              variant={activeSection === 'notificaciones' ? 'default' : 'outline'}
              onClick={() => setActiveSection('notificaciones')}
              className="flex items-center gap-2"
            >
              <Bell className="w-4 h-4" />
              Notificaciones
            </Button>
            <Button
              variant={activeSection === 'periodos' ? 'default' : 'outline'}
              onClick={() => setActiveSection('periodos')}
              className="flex items-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              Períodos
            </Button>
            <Button
              variant={activeSection === 'plantillas' ? 'default' : 'outline'}
              onClick={() => setActiveSection('plantillas')}
              className="flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Plantillas
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Configuración de Notificaciones */}
      {activeSection === 'notificaciones' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5 text-blue-600" />
                Canales de Notificación
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-sm text-gray-600">Notificaciones por correo electrónico</p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.canales_activos.includes('email')}
                    onCheckedChange={() => handleCanalToggle('email')}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Smartphone className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-medium">SMS</p>
                      <p className="text-sm text-gray-600">Mensajes de texto</p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.canales_activos.includes('sms')}
                    onCheckedChange={() => handleCanalToggle('sms')}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Bell className="w-5 h-5 text-purple-600" />
                    <div>
                      <p className="font-medium">Push</p>
                      <p className="text-sm text-gray-600">Notificaciones push</p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.canales_activos.includes('push')}
                    onCheckedChange={() => handleCanalToggle('push')}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <MessageSquare className="w-5 h-5 text-orange-600" />
                    <div>
                      <p className="font-medium">In-App</p>
                      <p className="text-sm text-gray-600">Notificaciones dentro de la aplicación</p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.canales_activos.includes('in_app')}
                    onCheckedChange={() => handleCanalToggle('in_app')}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Configuración de Períodos */}
      {activeSection === 'periodos' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                Días de Advertencia - Período de Prueba
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.trial_warning_days.map((days, index) => (
                  <div key={index}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Advertencia {index + 1}
                    </label>
                    <Input
                      type="number"
                      value={days}
                      onChange={(e) => handleTrialDaysChange(index, e.target.value)}
                      placeholder="Días"
                      min="1"
                      max="365"
                    />
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-600 mt-3">
                Configure los días antes del vencimiento del período de prueba en los que se enviarán las notificaciones de advertencia.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                Días de Advertencia - Suscripción
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.subscription_warning_days.map((days, index) => (
                  <div key={index}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Advertencia {index + 1}
                    </label>
                    <Input
                      type="number"
                      value={days}
                      onChange={(e) => handleSubscriptionDaysChange(index, e.target.value)}
                      placeholder="Días"
                      min="1"
                      max="30"
                    />
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-600 mt-3">
                Configure los días antes del vencimiento de la suscripción en los que se enviarán las notificaciones de advertencia.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-green-600" />
                Período de Gracia
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="max-w-xs">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Días de gracia después del vencimiento
                </label>
                <Input
                  type="number"
                  value={formData.grace_period_days}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    grace_period_days: parseInt(e.target.value) || 0
                  }))}
                  placeholder="Días"
                  min="0"
                  max="30"
                />
                <p className="text-sm text-gray-600 mt-2">
                  Número de días que los tenants pueden seguir usando el servicio después del vencimiento antes de ser suspendidos.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Configuración de Plantillas */}
      {activeSection === 'plantillas' && (
        <div className="space-y-6">
          {Object.entries(formData.email_templates).map(([templateKey, template]) => (
            <Card key={templateKey}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Edit className="w-5 h-5 text-blue-600" />
                  {templateKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Asunto
                  </label>
                  <Input
                    value={template.asunto || ''}
                    onChange={(e) => handleTemplateChange(templateKey, 'asunto', e.target.value)}
                    placeholder="Asunto del email"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contenido
                  </label>
                  <Textarea
                    value={template.contenido || ''}
                    onChange={(e) => handleTemplateChange(templateKey, 'contenido', e.target.value)}
                    placeholder="Contenido del email"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          ))}

          {Object.keys(formData.email_templates).length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <Edit className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No hay plantillas configuradas</h3>
                <p className="text-gray-600">
                  Las plantillas de email se cargarán automáticamente cuando estén disponibles en el sistema.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default ConfiguracionNegocio;
