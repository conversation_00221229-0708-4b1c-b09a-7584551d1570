import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/badge';
import { Button } from '../../../components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart,
  Calendar,
  RefreshCw
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { MetricasNegocio } from '../../../types/saasNegocio';

const DashboardNegocio: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('12m');

  const { data: metricas, isLoading, refetch } = useQuery({
    queryKey: ['metricas-negocio'],
    queryFn: () => saasNegocioService.obtenerMetricasNegocio(),
    refetchInterval: 30000 // Actualizar cada 30 segundos
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Colores para gráficos
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  // Datos para el gráfico de distribución de planes
  const planDistributionData = metricas?.distribucion_planes.map((plan, index) => ({
    ...plan,
    color: COLORS[index % COLORS.length]
  })) || [];

  // Datos para el gráfico de ingresos mensuales
  const ingresosData = metricas?.ingresos_por_mes.map(item => ({
    ...item,
    mes_corto: item.mes.split('-')[1]
  })) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Dashboard de Negocio</h2>
          <p className="text-gray-600">Métricas y análisis en tiempo real del negocio SaaS</p>
        </div>
        <div className="flex items-center gap-3">
          <select 
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="1m">Último mes</option>
            <option value="3m">Últimos 3 meses</option>
            <option value="6m">Últimos 6 meses</option>
            <option value="12m">Último año</option>
          </select>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-white/90 backdrop-blur-sm border border-blue-100 shadow-sm hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tenants Activos</p>
                <p className="text-3xl font-bold text-blue-700">{metricas?.total_tenants_activos || 0}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">+12% vs mes anterior</span>
                </div>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                <CheckCircle className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/90 backdrop-blur-sm border border-green-100 shadow-sm hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ingresos Mensuales</p>
                <p className="text-2xl font-bold text-green-700">
                  {formatCurrency(metricas?.ingresos_mensuales || 0)}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">+8% vs mes anterior</span>
                </div>
              </div>
              <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/90 backdrop-blur-sm border border-purple-100 shadow-sm hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tasa de Conversión</p>
                <p className="text-3xl font-bold text-purple-700">
                  {formatPercentage(metricas?.porcentaje_conversion || 0)}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">+5% vs mes anterior</span>
                </div>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                <BarChart3 className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/90 backdrop-blur-sm border border-orange-100 shadow-sm hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tenants Suspendidos</p>
                <p className="text-3xl font-bold text-orange-700">{metricas?.total_tenants_suspendidos || 0}</p>
                <div className="flex items-center mt-2">
                  <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                  <span className="text-sm text-red-600">-2% vs mes anterior</span>
                </div>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg border border-orange-100">
                <AlertTriangle className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos principales */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Gráfico de ingresos mensuales */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              Ingresos Mensuales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={ingresosData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="mes_corto" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip 
                    formatter={(value) => [formatCurrency(Number(value)), 'Ingresos']}
                    labelFormatter={(label) => `Mes ${label}`}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="ingresos" 
                    stroke="#3B82F6" 
                    fill="#3B82F6" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Distribución por planes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-green-600" />
              Distribución por Planes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={planDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="cantidad_tenants"
                    label={({ plan_nombre, cantidad_tenants }) => `${plan_nombre}: ${cantidad_tenants}`}
                  >
                    {planDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertas de vencimiento */}
      {metricas?.alertas_vencimiento && metricas.alertas_vencimiento.length > 0 && (
        <Card className="border border-orange-200/50 bg-orange-50/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="w-5 h-5" />
              Alertas de Vencimiento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metricas.alertas_vencimiento.map((alerta, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white/80 rounded-lg border border-orange-100 shadow-sm">
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-orange-600" />
                    <div>
                      <p className="font-medium text-gray-900">{alerta.tenant_nombre}</p>
                      <p className="text-sm text-gray-600">
                        {alerta.tipo_alerta === 'trial_warning' ? 'Período de prueba' : 'Suscripción'} vence en {alerta.dias_restantes} días
                      </p>
                    </div>
                  </div>
                  <Badge variant={alerta.dias_restantes <= 3 ? 'destructive' : 'secondary'}>
                    {alerta.dias_restantes} días
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DashboardNegocio;
