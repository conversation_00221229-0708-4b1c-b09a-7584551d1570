import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import { 
  Building2, 
  Play, 
  Pause, 
  Square, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  Mail,
  CreditCard,
  Shield
} from 'lucide-react';

interface TenantDetallado {
  id: string;
  name: string;
  email: string;
  status: 'trial_active' | 'trial_expired' | 'active' | 'suspended' | 'paused' | 'cancelled';
  plan: string;
  start_trial_date: string;
  end_trial_date: string;
  subscription_start_date?: string;
  subscription_end_date?: string;
  last_payment_date?: string;
  next_payment_date?: string;
  users_count: number;
  storage_used_gb: number;
  monthly_amount: number;
  grace_period_days: number;
}

const GestionEstadosTenant: React.FC = () => {
  const [selectedTenant, setSelectedTenant] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState<'activate' | 'suspend' | 'pause' | 'cancel' | null>(null);

  // Datos de ejemplo
  const tenants: TenantDetallado[] = [
    {
      id: '1',
      name: 'Hospital San José',
      email: '<EMAIL>',
      status: 'active',
      plan: 'Premium',
      start_trial_date: '2024-01-15',
      end_trial_date: '2024-02-14',
      subscription_start_date: '2024-02-10',
      subscription_end_date: '2024-12-10',
      last_payment_date: '2024-11-10',
      next_payment_date: '2024-12-10',
      users_count: 150,
      storage_used_gb: 45,
      monthly_amount: 450000,
      grace_period_days: 7
    },
    {
      id: '2',
      name: 'Clínica del Norte',
      email: '<EMAIL>',
      status: 'trial_active',
      plan: 'Básico',
      start_trial_date: '2024-10-20',
      end_trial_date: '2024-11-20',
      users_count: 25,
      storage_used_gb: 8,
      monthly_amount: 200000,
      grace_period_days: 7
    },
    {
      id: '3',
      name: 'Centro Médico Sur',
      email: '<EMAIL>',
      status: 'suspended',
      plan: 'Enterprise',
      start_trial_date: '2024-08-01',
      end_trial_date: '2024-08-31',
      subscription_start_date: '2024-08-25',
      subscription_end_date: '2024-11-25',
      last_payment_date: '2024-10-25',
      next_payment_date: '2024-11-25',
      users_count: 200,
      storage_used_gb: 85,
      monthly_amount: 800000,
      grace_period_days: 7
    }
  ];

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'trial_active':
        return { 
          label: 'Prueba Activa', 
          color: 'bg-blue-100 text-blue-800', 
          icon: <Clock className="w-4 h-4" /> 
        };
      case 'trial_expired':
        return { 
          label: 'Prueba Vencida', 
          color: 'bg-yellow-100 text-yellow-800', 
          icon: <AlertTriangle className="w-4 h-4" /> 
        };
      case 'active':
        return { 
          label: 'Activo', 
          color: 'bg-green-100 text-green-800', 
          icon: <CheckCircle className="w-4 h-4" /> 
        };
      case 'suspended':
        return { 
          label: 'Suspendido', 
          color: 'bg-red-100 text-red-800', 
          icon: <XCircle className="w-4 h-4" /> 
        };
      case 'paused':
        return { 
          label: 'Pausado', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <Pause className="w-4 h-4" /> 
        };
      case 'cancelled':
        return { 
          label: 'Cancelado', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <Square className="w-4 h-4" /> 
        };
      default:
        return { 
          label: 'Desconocido', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <AlertTriangle className="w-4 h-4" /> 
        };
    }
  };

  const getAvailableActions = (status: string) => {
    switch (status) {
      case 'trial_active':
        return [
          { action: 'activate', label: 'Activar Suscripción', icon: <Play className="w-4 h-4" />, color: 'bg-green-600 hover:bg-green-700' },
          { action: 'suspend', label: 'Suspender', icon: <XCircle className="w-4 h-4" />, color: 'bg-red-600 hover:bg-red-700' }
        ];
      case 'trial_expired':
        return [
          { action: 'activate', label: 'Activar Suscripción', icon: <Play className="w-4 h-4" />, color: 'bg-green-600 hover:bg-green-700' },
          { action: 'cancel', label: 'Cancelar', icon: <Square className="w-4 h-4" />, color: 'bg-gray-600 hover:bg-gray-700' }
        ];
      case 'active':
        return [
          { action: 'pause', label: 'Pausar', icon: <Pause className="w-4 h-4" />, color: 'bg-yellow-600 hover:bg-yellow-700' },
          { action: 'suspend', label: 'Suspender', icon: <XCircle className="w-4 h-4" />, color: 'bg-red-600 hover:bg-red-700' }
        ];
      case 'suspended':
        return [
          { action: 'activate', label: 'Reactivar', icon: <Play className="w-4 h-4" />, color: 'bg-green-600 hover:bg-green-700' },
          { action: 'cancel', label: 'Cancelar', icon: <Square className="w-4 h-4" />, color: 'bg-gray-600 hover:bg-gray-700' }
        ];
      case 'paused':
        return [
          { action: 'activate', label: 'Reanudar', icon: <Play className="w-4 h-4" />, color: 'bg-green-600 hover:bg-green-700' },
          { action: 'cancel', label: 'Cancelar', icon: <Square className="w-4 h-4" />, color: 'bg-gray-600 hover:bg-gray-700' }
        ];
      default:
        return [];
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleAction = (tenantId: string, action: string) => {
    setSelectedTenant(tenantId);
    setActionType(action as any);
    setShowModal(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Gestión de Estados de Tenant</h3>
          <p className="text-gray-600">Control de activación, suspensión y cancelación de tenants</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Settings className="w-4 h-4 mr-2" />
          Configurar Automatización
        </Button>
      </div>

      {/* Resumen de Estados */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4 text-center">
            <Clock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">En Prueba</p>
            <p className="text-xl font-bold text-blue-700">1</p>
          </CardContent>
        </Card>
        
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4 text-center">
            <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Activos</p>
            <p className="text-xl font-bold text-green-700">1</p>
          </CardContent>
        </Card>
        
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4 text-center">
            <XCircle className="w-6 h-6 text-red-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Suspendidos</p>
            <p className="text-xl font-bold text-red-700">1</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="p-4 text-center">
            <Pause className="w-6 h-6 text-gray-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Pausados</p>
            <p className="text-xl font-bold text-gray-700">0</p>
          </CardContent>
        </Card>
        
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4 text-center">
            <AlertTriangle className="w-6 h-6 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Vencidos</p>
            <p className="text-xl font-bold text-yellow-700">0</p>
          </CardContent>
        </Card>
        
        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-4 text-center">
            <Building2 className="w-6 h-6 text-purple-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Total</p>
            <p className="text-xl font-bold text-purple-700">3</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Tenants */}
      <div className="space-y-4">
        {tenants.map((tenant) => {
          const statusInfo = getStatusInfo(tenant.status);
          const actions = getAvailableActions(tenant.status);
          
          return (
            <Card key={tenant.id} className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-blue-600" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-lg font-semibold text-gray-900">{tenant.name}</h4>
                        <Badge className={statusInfo.color}>
                          <div className="flex items-center gap-1">
                            {statusInfo.icon}
                            {statusInfo.label}
                          </div>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Email: {tenant.email}</p>
                          <p className="text-gray-600">Plan: {tenant.plan}</p>
                          <p className="text-gray-600">Usuarios: {tenant.users_count}</p>
                        </div>
                        
                        <div>
                          <p className="text-gray-600">Almacenamiento: {tenant.storage_used_gb} GB</p>
                          <p className="text-gray-600">Monto mensual: {formatCurrency(tenant.monthly_amount)}</p>
                          {tenant.last_payment_date && (
                            <p className="text-gray-600">Último pago: {formatDate(tenant.last_payment_date)}</p>
                          )}
                        </div>
                        
                        <div>
                          {tenant.status === 'trial_active' && (
                            <p className="text-gray-600">Prueba vence: {formatDate(tenant.end_trial_date)}</p>
                          )}
                          {tenant.subscription_end_date && (
                            <p className="text-gray-600">Suscripción vence: {formatDate(tenant.subscription_end_date)}</p>
                          )}
                          {tenant.next_payment_date && (
                            <p className="text-gray-600">Próximo pago: {formatDate(tenant.next_payment_date)}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-2">
                    {actions.map((action) => (
                      <Button
                        key={action.action}
                        size="sm"
                        className={`${action.color} text-white`}
                        onClick={() => handleAction(tenant.id, action.action)}
                      >
                        {action.icon}
                        <span className="ml-2">{action.label}</span>
                      </Button>
                    ))}
                    
                    <div className="flex gap-1 mt-2">
                      <Button variant="outline" size="sm">
                        <Mail className="w-3 h-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <CreditCard className="w-3 h-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Shield className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Modal de confirmación (placeholder) */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Confirmar Acción</h3>
            <p className="text-gray-600 mb-6">
              ¿Está seguro que desea {actionType} el tenant seleccionado?
            </p>
            <div className="flex gap-3 justify-end">
              <Button variant="outline" onClick={() => setShowModal(false)}>
                Cancelar
              </Button>
              <Button 
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  // Aquí iría la lógica para ejecutar la acción
                  setShowModal(false);
                  setSelectedTenant(null);
                  setActionType(null);
                }}
              >
                Confirmar
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionEstadosTenant;
