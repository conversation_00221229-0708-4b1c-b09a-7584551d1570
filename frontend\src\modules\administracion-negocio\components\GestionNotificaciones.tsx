import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Search, 
  Filter, 
  Eye, 
  Send,
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Building2
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { NotificacionLog } from '../../../types/saasNegocio';

const GestionNotificaciones: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [tipoFilter, setTipoFilter] = useState('all');
  const [canal<PERSON>ilter, setCanalFilter] = useState('all');
  const [estadoFilter, setEstadoFilter] = useState('all');

  const { data: notificacionesResponse, isLoading } = useQuery({
    queryKey: ['notificaciones-log'],
    queryFn: () => saasNegocioService.obtenerNotificaciones()
  });

  const notificaciones = notificacionesResponse?.data || [];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTipoNotificacionBadge = (tipo: string) => {
    const tipos = {
      trial_warning: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        icon: AlertTriangle, 
        label: 'Advertencia Prueba' 
      },
      trial_expired: { 
        color: 'bg-orange-100 text-orange-800 border-orange-200', 
        icon: Clock, 
        label: 'Prueba Vencida' 
      },
      payment_reminder: { 
        color: 'bg-blue-100 text-blue-800 border-blue-200', 
        icon: Bell, 
        label: 'Recordatorio Pago' 
      },
      payment_failed: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        icon: XCircle, 
        label: 'Pago Fallido' 
      },
      subscription_renewed: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        icon: CheckCircle, 
        label: 'Suscripción Renovada' 
      }
    };
    
    const config = tipos[tipo as keyof typeof tipos] || tipos.trial_warning;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} border`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getCanalBadge = (canal: string) => {
    const canales = {
      email: { color: 'bg-blue-100 text-blue-800', icon: Mail, label: 'Email' },
      sms: { color: 'bg-green-100 text-green-800', icon: Smartphone, label: 'SMS' },
      push: { color: 'bg-purple-100 text-purple-800', icon: Bell, label: 'Push' },
      in_app: { color: 'bg-orange-100 text-orange-800', icon: MessageSquare, label: 'In-App' }
    };
    
    const config = canales[canal as keyof typeof canales] || canales.email;
    const IconComponent = config.icon;
    
    return (
      <Badge className={config.color}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getEstadoEnvioBadge = (estado: string) => {
    const estados = {
      pending: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        icon: Clock, 
        label: 'Pendiente' 
      },
      sent: { 
        color: 'bg-blue-100 text-blue-800 border-blue-200', 
        icon: Send, 
        label: 'Enviado' 
      },
      delivered: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        icon: CheckCircle, 
        label: 'Entregado' 
      },
      failed: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        icon: XCircle, 
        label: 'Fallido' 
      },
      bounced: { 
        color: 'bg-gray-100 text-gray-800 border-gray-200', 
        icon: AlertTriangle, 
        label: 'Rebotado' 
      }
    };
    
    const config = estados[estado as keyof typeof estados] || estados.pending;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} border`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const filteredNotificaciones = notificaciones.filter(notif => {
    const matchesSearch = 
      notif.destinatario.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notif.asunto?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notif.tenant?.nombre.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTipo = tipoFilter === 'all' || notif.tipo_notificacion === tipoFilter;
    const matchesCanal = canalFilter === 'all' || notif.canal === canalFilter;
    const matchesEstado = estadoFilter === 'all' || notif.estado_envio === estadoFilter;
    
    return matchesSearch && matchesTipo && matchesCanal && matchesEstado;
  });

  // Calcular estadísticas
  const totalNotificaciones = notificaciones.length;
  const notificacionesEnviadas = notificaciones.filter(n => n.estado_envio === 'sent' || n.estado_envio === 'delivered').length;
  const notificacionesFallidas = notificaciones.filter(n => n.estado_envio === 'failed' || n.estado_envio === 'bounced').length;
  const tasaExito = totalNotificaciones > 0 ? (notificacionesEnviadas / totalNotificaciones) * 100 : 0;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión de Notificaciones</h2>
          <p className="text-gray-600">Administre las notificaciones enviadas a los tenants</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Send className="w-4 h-4 mr-2" />
          Enviar Notificación
        </Button>
      </div>

      {/* Estadísticas de notificaciones */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Enviadas</p>
                <p className="text-2xl font-bold text-blue-600">{totalNotificaciones}</p>
              </div>
              <Bell className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Exitosas</p>
                <p className="text-2xl font-bold text-green-600">{notificacionesEnviadas}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Fallidas</p>
                <p className="text-2xl font-bold text-red-600">{notificacionesFallidas}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tasa de Éxito</p>
                <p className="text-2xl font-bold text-purple-600">{tasaExito.toFixed(1)}%</p>
              </div>
              <Send className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros y búsqueda */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar notificaciones..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={tipoFilter}
              onChange={(e) => setTipoFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los tipos</option>
              <option value="trial_warning">Advertencia Prueba</option>
              <option value="trial_expired">Prueba Vencida</option>
              <option value="payment_reminder">Recordatorio Pago</option>
              <option value="payment_failed">Pago Fallido</option>
              <option value="subscription_renewed">Suscripción Renovada</option>
            </select>
            <select
              value={canalFilter}
              onChange={(e) => setCanalFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los canales</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="push">Push</option>
              <option value="in_app">In-App</option>
            </select>
            <select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los estados</option>
              <option value="pending">Pendiente</option>
              <option value="sent">Enviado</option>
              <option value="delivered">Entregado</option>
              <option value="failed">Fallido</option>
              <option value="bounced">Rebotado</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de notificaciones */}
      <Card>
        <CardHeader>
          <CardTitle>Historial de Notificaciones</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredNotificaciones.map((notificacion) => (
              <div key={notificacion.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getTipoNotificacionBadge(notificacion.tipo_notificacion)}
                      {getCanalBadge(notificacion.canal)}
                      {getEstadoEnvioBadge(notificacion.estado_envio)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-1">
                          {notificacion.asunto || 'Sin asunto'}
                        </h4>
                        <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                          <Building2 className="w-4 h-4" />
                          <span>{notificacion.tenant?.nombre || `Tenant ${notificacion.tenant_id}`}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Mail className="w-4 h-4" />
                          <span>{notificacion.destinatario}</span>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-600">
                        <div className="flex items-center gap-2 mb-1">
                          <Calendar className="w-4 h-4" />
                          <span>Creada: {formatDate(notificacion.created_at)}</span>
                        </div>
                        {notificacion.fecha_enviada && (
                          <div className="flex items-center gap-2">
                            <Send className="w-4 h-4" />
                            <span>Enviada: {formatDate(notificacion.fecha_enviada)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {notificacion.contenido && (
                      <div className="mt-3 p-3 bg-gray-50 rounded text-sm text-gray-700">
                        {notificacion.contenido.length > 200 
                          ? `${notificacion.contenido.substring(0, 200)}...`
                          : notificacion.contenido
                        }
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      Ver
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredNotificaciones.length === 0 && (
            <div className="text-center py-12">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay notificaciones disponibles</h3>
              <p className="text-gray-600">
                {searchTerm || tipoFilter !== 'all' || canalFilter !== 'all' || estadoFilter !== 'all'
                  ? 'No se encontraron notificaciones que coincidan con los filtros aplicados.'
                  : 'No hay notificaciones registradas en el sistema.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GestionNotificaciones;
