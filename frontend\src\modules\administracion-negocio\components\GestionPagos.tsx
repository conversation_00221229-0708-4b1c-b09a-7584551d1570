import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Search, 
  Filter, 
  Eye, 
  Download,
  CreditCard,
  Building2,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { PagoSuscripcion } from '../../../types/saasNegocio';

const GestionPagos: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [methodFilter, setMethodFilter] = useState('all');

  const { data: pagosResponse, isLoading } = useQuery({
    queryKey: ['pagos-suscripcion'],
    queryFn: () => saasNegocioService.obtenerPagos()
  });

  const pagos = pagosResponse?.data || [];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEstadoPagoBadge = (estado: string) => {
    const estados = {
      pending: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        icon: Clock, 
        label: 'Pendiente' 
      },
      confirmed: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        icon: CheckCircle, 
        label: 'Confirmado' 
      },
      failed: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        icon: XCircle, 
        label: 'Fallido' 
      },
      manual_review: { 
        color: 'bg-orange-100 text-orange-800 border-orange-200', 
        icon: AlertTriangle, 
        label: 'Revisión Manual' 
      },
      cancelled: { 
        color: 'bg-gray-100 text-gray-800 border-gray-200', 
        icon: XCircle, 
        label: 'Cancelado' 
      }
    };
    
    const config = estados[estado as keyof typeof estados] || estados.pending;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} border`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getMetodoPagoBadge = (metodo: string) => {
    const metodos = {
      tarjeta_credito: { color: 'bg-blue-100 text-blue-800', label: 'Tarjeta de Crédito' },
      transferencia_bancaria: { color: 'bg-green-100 text-green-800', label: 'Transferencia' },
      debito_automatico: { color: 'bg-purple-100 text-purple-800', label: 'Débito Automático' },
      efectivo: { color: 'bg-gray-100 text-gray-800', label: 'Efectivo' }
    };
    
    const config = metodos[metodo as keyof typeof metodos] || metodos.tarjeta_credito;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const filteredPagos = pagos.filter(pago => {
    const matchesSearch = 
      pago.referencia_transaccion?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pago.tenant?.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pago.factura_id?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || pago.estado_pago === statusFilter;
    const matchesMethod = methodFilter === 'all' || pago.metodo_pago === methodFilter;
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  // Calcular estadísticas
  const totalPagos = pagos.length;
  const pagosConfirmados = pagos.filter(p => p.estado_pago === 'confirmed').length;
  const pagosFallidos = pagos.filter(p => p.estado_pago === 'failed').length;
  const montoTotal = pagos
    .filter(p => p.estado_pago === 'confirmed')
    .reduce((sum, p) => sum + p.monto, 0);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión de Pagos</h2>
          <p className="text-gray-600">Administre los pagos y transacciones de suscripciones</p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Exportar
        </Button>
      </div>

      {/* Estadísticas de pagos */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Pagos</p>
                <p className="text-2xl font-bold text-blue-600">{totalPagos}</p>
              </div>
              <CreditCard className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Confirmados</p>
                <p className="text-2xl font-bold text-green-600">{pagosConfirmados}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                  <span className="text-xs text-green-600">
                    {totalPagos > 0 ? ((pagosConfirmados / totalPagos) * 100).toFixed(1) : 0}%
                  </span>
                </div>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Fallidos</p>
                <p className="text-2xl font-bold text-red-600">{pagosFallidos}</p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-3 h-3 text-red-600 mr-1" />
                  <span className="text-xs text-red-600">
                    {totalPagos > 0 ? ((pagosFallidos / totalPagos) * 100).toFixed(1) : 0}%
                  </span>
                </div>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monto Total</p>
                <p className="text-xl font-bold text-purple-600">
                  {formatCurrency(montoTotal)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros y búsqueda */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar por referencia, tenant o factura..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los estados</option>
              <option value="pending">Pendiente</option>
              <option value="confirmed">Confirmado</option>
              <option value="failed">Fallido</option>
              <option value="manual_review">Revisión Manual</option>
              <option value="cancelled">Cancelado</option>
            </select>
            <select
              value={methodFilter}
              onChange={(e) => setMethodFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los métodos</option>
              <option value="tarjeta_credito">Tarjeta de Crédito</option>
              <option value="transferencia_bancaria">Transferencia</option>
              <option value="debito_automatico">Débito Automático</option>
              <option value="efectivo">Efectivo</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de pagos */}
      <Card>
        <CardHeader>
          <CardTitle>Historial de Pagos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Referencia</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Tenant</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Monto</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Método</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Estado</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Fecha</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {filteredPagos.map((pago) => (
                  <tr key={pago.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium text-gray-900">
                          {pago.referencia_transaccion || 'N/A'}
                        </p>
                        {pago.factura_id && (
                          <p className="text-sm text-gray-600">Factura: {pago.factura_id}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Building2 className="w-4 h-4 text-gray-400" />
                        <span className="font-medium">{pago.tenant?.nombre || `Tenant ${pago.tenant_id}`}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="font-bold text-green-600">
                        {formatCurrency(pago.monto)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      {getMetodoPagoBadge(pago.metodo_pago)}
                    </td>
                    <td className="py-3 px-4">
                      {getEstadoPagoBadge(pago.estado_pago)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">
                          {pago.fecha_pago ? formatDate(pago.fecha_pago) : formatDate(pago.created_at)}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        Ver
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredPagos.length === 0 && (
            <div className="text-center py-12">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay pagos disponibles</h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter !== 'all' || methodFilter !== 'all'
                  ? 'No se encontraron pagos que coincidan con los filtros aplicados.'
                  : 'No hay pagos registrados en el sistema.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GestionPagos;
