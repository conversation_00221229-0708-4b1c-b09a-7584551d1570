import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  CreditCard,
  Users,
  Database,
  HardDrive,
  CheckCircle,
  XCircle,
  Settings
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { PlanSuscripcion } from '../../../types/saasNegocio';

const GestionPlanes: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPlan, setSelectedPlan] = useState<PlanSuscripcion | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const { data: planes, isLoading } = useQuery({
    queryKey: ['planes-suscripcion'],
    queryFn: () => saasNegocioService.obtenerPlanes()
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const filteredPlanes = planes?.filter(plan =>
    plan.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    plan.descripcion.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleEditPlan = (plan: PlanSuscripcion) => {
    setSelectedPlan(plan);
    setShowEditModal(true);
  };

  const handleDeletePlan = async (planId: string) => {
    if (window.confirm('¿Está seguro de que desea eliminar este plan?')) {
      try {
        await saasNegocioService.eliminarPlan(planId);
        // Refetch data
      } catch (error) {
        console.error('Error al eliminar plan:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión de Planes</h2>
          <p className="text-gray-600">Administre los planes de suscripción disponibles</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Nuevo Plan
        </Button>
      </div>

      {/* Filtros y búsqueda */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar planes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Lista de planes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPlanes.map((plan) => (
          <Card key={plan.id} className="relative overflow-hidden">
            {/* Badge de estado */}
            <div className="absolute top-4 right-4">
              <Badge variant={plan.activo ? 'default' : 'secondary'}>
                {plan.activo ? (
                  <>
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Activo
                  </>
                ) : (
                  <>
                    <XCircle className="w-3 h-3 mr-1" />
                    Inactivo
                  </>
                )}
              </Badge>
            </div>

            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-xl">
                <CreditCard className="w-5 h-5 text-blue-600" />
                {plan.nombre}
              </CardTitle>
              <div className="text-3xl font-bold text-blue-600">
                {formatCurrency(plan.precio)}
                <span className="text-sm font-normal text-gray-500">/{plan.duracion_dias} días</span>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-gray-600 text-sm">{plan.descripcion}</p>

              {/* Límites del plan */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-gray-500" />
                    Usuarios
                  </span>
                  <span className="font-medium">{plan.max_usuarios}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-2">
                    <Database className="w-4 h-4 text-gray-500" />
                    Pacientes
                  </span>
                  <span className="font-medium">{plan.max_pacientes.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-2">
                    <HardDrive className="w-4 h-4 text-gray-500" />
                    Almacenamiento
                  </span>
                  <span className="font-medium">{plan.max_storage_gb} GB</span>
                </div>
              </div>

              {/* Módulos incluidos */}
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Módulos incluidos:</p>
                <div className="flex flex-wrap gap-1">
                  {plan.modulos_incluidos.slice(0, 3).map((modulo, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {modulo}
                    </Badge>
                  ))}
                  {plan.modulos_incluidos.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{plan.modulos_incluidos.length - 3} más
                    </Badge>
                  )}
                </div>
              </div>

              {/* Características especiales */}
              {plan.caracteristicas && Object.keys(plan.caracteristicas).length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Características:</p>
                  <div className="space-y-1">
                    {Object.entries(plan.caracteristicas).slice(0, 3).map(([key, value], index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 capitalize">{key.replace('_', ' ')}</span>
                        <span className="font-medium">
                          {typeof value === 'boolean' ? (value ? 'Sí' : 'No') : value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Acciones */}
              <div className="flex gap-2 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditPlan(plan)}
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeletePlan(plan.id)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mensaje cuando no hay planes */}
      {filteredPlanes.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay planes disponibles</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'No se encontraron planes que coincidan con su búsqueda.' : 'Comience creando su primer plan de suscripción.'}
            </p>
            {!searchTerm && (
              <Button onClick={() => setShowCreateModal(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Crear Primer Plan
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Estadísticas de planes */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Planes</p>
                <p className="text-2xl font-bold text-blue-600">{planes?.length || 0}</p>
              </div>
              <CreditCard className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Planes Activos</p>
                <p className="text-2xl font-bold text-green-600">
                  {planes?.filter(p => p.activo).length || 0}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Precio Promedio</p>
                <p className="text-xl font-bold text-purple-600">
                  {planes && planes.length > 0 
                    ? formatCurrency(planes.reduce((sum, p) => sum + p.precio, 0) / planes.length)
                    : formatCurrency(0)
                  }
                </p>
              </div>
              <Settings className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GestionPlanes;
