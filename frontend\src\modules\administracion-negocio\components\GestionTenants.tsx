import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Pause, 
  Play, 
  Ban,
  Building2,
  Calendar,
  Users,
  Database,
  HardDrive,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Mail,
  Phone
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import saasNegocioService from '../../../services/saasNegocioService';
import { TenantSuscripcion } from '../../../types/saasNegocio';

const GestionTenants: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedTenant, setSelectedTenant] = useState<TenantSuscripcion | null>(null);

  const { data: tenantsResponse, isLoading } = useQuery({
    queryKey: ['tenants-suscripcion'],
    queryFn: () => saasNegocioService.obtenerTenants()
  });

  const tenants = tenantsResponse?.data || [];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO');
  };

  const getEstadoBadge = (estado: string) => {
    const estados = {
      trial_active: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        icon: Clock, 
        label: 'Prueba Activa' 
      },
      active: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        icon: CheckCircle, 
        label: 'Activo' 
      },
      suspended: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        icon: XCircle, 
        label: 'Suspendido' 
      },
      trial_expired: { 
        color: 'bg-orange-100 text-orange-800 border-orange-200', 
        icon: AlertTriangle, 
        label: 'Prueba Vencida' 
      },
      cancelled: { 
        color: 'bg-gray-100 text-gray-800 border-gray-200', 
        icon: Ban, 
        label: 'Cancelado' 
      }
    };
    
    const config = estados[estado as keyof typeof estados] || estados.active;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} border`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getDiasRestantesBadge = (diasRestantes: number) => {
    if (diasRestantes < 0) {
      return <Badge variant="destructive">Vencido</Badge>;
    } else if (diasRestantes <= 7) {
      return <Badge variant="destructive">{diasRestantes} días</Badge>;
    } else if (diasRestantes <= 15) {
      return <Badge className="bg-yellow-100 text-yellow-800">{diasRestantes} días</Badge>;
    } else {
      return <Badge variant="secondary">{diasRestantes} días</Badge>;
    }
  };

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = 
      tenant.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.hospital?.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.hospital?.nit.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || tenant.estado_suscripcion === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleVerDetalle = (tenant: TenantSuscripcion) => {
    setSelectedTenant(tenant);
  };

  const handleCambiarEstado = async (tenantId: number, nuevoEstado: string) => {
    if (window.confirm(`¿Está seguro de cambiar el estado del tenant?`)) {
      try {
        // Implementar llamada a API para cambiar estado
        console.log(`Cambiar estado del tenant ${tenantId} a ${nuevoEstado}`);
      } catch (error) {
        console.error('Error al cambiar estado:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión de Tenants</h2>
          <p className="text-gray-600">Administre las suscripciones y estados de los hospitales</p>
        </div>
      </div>

      {/* Filtros y búsqueda */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar por nombre, hospital o NIT..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos los estados</option>
              <option value="trial_active">Prueba Activa</option>
              <option value="active">Activo</option>
              <option value="suspended">Suspendido</option>
              <option value="trial_expired">Prueba Vencida</option>
              <option value="cancelled">Cancelado</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de tenants */}
      <div className="space-y-4">
        {filteredTenants.map((tenant) => (
          <Card key={tenant.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <Building2 className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{tenant.nombre}</h3>
                      <p className="text-sm text-gray-600">{tenant.hospital?.nombre}</p>
                    </div>
                    {getEstadoBadge(tenant.estado_suscripcion)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    {/* Información básica */}
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Información Básica</p>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <Building2 className="w-4 h-4 text-gray-400" />
                          <span>{tenant.hospital?.nit}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-gray-400" />
                          <span className="truncate">{tenant.hospital?.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span>{tenant.hospital?.telefono}</span>
                        </div>
                      </div>
                    </div>

                    {/* Fechas importantes */}
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Fechas</p>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span>Inicio: {formatDate(tenant.fecha_inicio_prueba)}</span>
                        </div>
                        {tenant.fecha_fin_suscripcion && (
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-gray-400" />
                            <span>Vence: {formatDate(tenant.fecha_fin_suscripcion)}</span>
                          </div>
                        )}
                        {tenant.dias_restantes !== undefined && (
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-gray-400" />
                            {getDiasRestantesBadge(tenant.dias_restantes)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Uso de recursos */}
                    {tenant.porcentaje_uso && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">Uso de Recursos</p>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              Usuarios
                            </span>
                            <span className="font-medium">{tenant.porcentaje_uso.usuarios}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className="bg-blue-600 h-1.5 rounded-full" 
                              style={{ width: `${tenant.porcentaje_uso.usuarios}%` }}
                            ></div>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center gap-1">
                              <Database className="w-3 h-3" />
                              Pacientes
                            </span>
                            <span className="font-medium">{tenant.porcentaje_uso.pacientes}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className="bg-green-600 h-1.5 rounded-full" 
                              style={{ width: `${tenant.porcentaje_uso.pacientes}%` }}
                            ></div>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center gap-1">
                              <HardDrive className="w-3 h-3" />
                              Storage
                            </span>
                            <span className="font-medium">{tenant.porcentaje_uso.storage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className="bg-purple-600 h-1.5 rounded-full" 
                              style={{ width: `${tenant.porcentaje_uso.storage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Plan actual */}
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Plan Actual</p>
                      <div className="space-y-1 text-sm">
                        <Badge variant="outline" className="mb-2">
                          {tenant.plan_tipo.charAt(0).toUpperCase() + tenant.plan_tipo.slice(1)}
                        </Badge>
                        {tenant.plan && (
                          <p className="text-gray-600">{formatCurrency(tenant.plan.precio)}/mes</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Acciones */}
                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleVerDetalle(tenant)}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Ver Detalle
                  </Button>
                  
                  {tenant.estado_suscripcion === 'suspended' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCambiarEstado(tenant.id, 'active')}
                      className="text-green-600 hover:text-green-700"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Activar
                    </Button>
                  )}
                  
                  {tenant.estado_suscripcion === 'active' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCambiarEstado(tenant.id, 'suspended')}
                      className="text-orange-600 hover:text-orange-700"
                    >
                      <Pause className="w-4 h-4 mr-2" />
                      Suspender
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mensaje cuando no hay tenants */}
      {filteredTenants.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay tenants disponibles</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' 
                ? 'No se encontraron tenants que coincidan con los filtros aplicados.' 
                : 'No hay tenants registrados en el sistema.'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GestionTenants;
