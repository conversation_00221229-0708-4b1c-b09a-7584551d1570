import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Zap
} from 'lucide-react';

interface MetricaDetallada {
  titulo: string;
  valor: string | number;
  cambio: number;
  periodo: string;
  tipo: 'currency' | 'percentage' | 'number';
  tendencia: 'up' | 'down' | 'stable';
}

const MetricasAvanzadas: React.FC = () => {
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState('30d');

  const metricas: MetricaDetallada[] = [
    {
      titulo: 'Ingresos Recurrentes Mensuales (MRR)',
      valor: 1650000,
      cambio: 12.5,
      periodo: 'vs mes anterior',
      tipo: 'currency',
      tendencia: 'up'
    },
    {
      titulo: 'Ingresos Recurrentes Anuales (ARR)',
      valor: 19800000,
      cambio: 15.2,
      periodo: 'vs año anterior',
      tipo: 'currency',
      tendencia: 'up'
    },
    {
      titulo: 'Tasa de Conversión Trial → Pago',
      valor: 66.7,
      cambio: 5.3,
      periodo: 'vs mes anterior',
      tipo: 'percentage',
      tendencia: 'up'
    },
    {
      titulo: 'Churn Rate (Cancelaciones)',
      valor: 3.2,
      cambio: -1.1,
      periodo: 'vs mes anterior',
      tipo: 'percentage',
      tendencia: 'down'
    },
    {
      titulo: 'Customer Lifetime Value (CLV)',
      valor: 5400000,
      cambio: 8.7,
      periodo: 'vs trimestre anterior',
      tipo: 'currency',
      tendencia: 'up'
    },
    {
      titulo: 'Costo de Adquisición (CAC)',
      valor: 850000,
      cambio: -12.3,
      periodo: 'vs mes anterior',
      tipo: 'currency',
      tendencia: 'down'
    }
  ];

  const datosGraficos = {
    ingresosMensuales: [
      { mes: 'Jul', valor: 1200000 },
      { mes: 'Ago', valor: 1350000 },
      { mes: 'Sep', valor: 1480000 },
      { mes: 'Oct', valor: 1520000 },
      { mes: 'Nov', valor: 1650000 }
    ],
    distribucionPlanes: [
      { plan: 'Básico', cantidad: 2, porcentaje: 25, color: '#3B82F6' },
      { plan: 'Premium', cantidad: 5, porcentaje: 62.5, color: '#8B5CF6' },
      { plan: 'Enterprise', cantidad: 1, porcentaje: 12.5, color: '#F59E0B' }
    ],
    conversionFunnel: [
      { etapa: 'Visitantes', cantidad: 1200, porcentaje: 100 },
      { etapa: 'Registros Trial', cantidad: 180, porcentaje: 15 },
      { etapa: 'Trials Activos', cantidad: 120, porcentaje: 10 },
      { etapa: 'Conversiones', cantidad: 80, porcentaje: 6.7 },
      { etapa: 'Clientes Activos', cantidad: 72, porcentaje: 6 }
    ]
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('es-CO').format(num);
  };

  const getTendenciaIcon = (tendencia: string) => {
    switch (tendencia) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTendenciaColor = (tendencia: string, cambio: number) => {
    if (tendencia === 'up' && cambio > 0) return 'text-green-600';
    if (tendencia === 'down' && cambio < 0) return 'text-green-600'; // Para métricas donde bajar es bueno (como CAC, Churn)
    if (tendencia === 'down' && cambio > 0) return 'text-red-600';
    if (tendencia === 'up' && cambio < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Métricas Avanzadas de Negocio</h3>
          <p className="text-gray-600">Análisis detallado del rendimiento SaaS</p>
        </div>
        <div className="flex gap-2">
          <select 
            value={periodoSeleccionado} 
            onChange={(e) => setPeriodoSeleccionado(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Últimos 7 días</option>
            <option value="30d">Últimos 30 días</option>
            <option value="90d">Últimos 90 días</option>
            <option value="1y">Último año</option>
          </select>
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Exportar Reporte
          </Button>
        </div>
      </div>

      {/* Métricas Principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metricas.map((metrica, index) => (
          <Card key={index} className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">{metrica.titulo}</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold text-gray-900">
                      {metrica.tipo === 'currency' && formatCurrency(metrica.valor as number)}
                      {metrica.tipo === 'percentage' && `${metrica.valor}%`}
                      {metrica.tipo === 'number' && formatNumber(metrica.valor as number)}
                    </span>
                  </div>
                  <div className={`flex items-center gap-1 mt-2 text-sm ${getTendenciaColor(metrica.tendencia, metrica.cambio)}`}>
                    {getTendenciaIcon(metrica.tendencia)}
                    <span>{Math.abs(metrica.cambio)}% {metrica.periodo}</span>
                  </div>
                </div>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Gráficos y Análisis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Evolución de Ingresos */}
        <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              Evolución de Ingresos Mensuales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {datosGraficos.ingresosMensuales.map((dato, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{dato.mes}</span>
                  <div className="flex items-center gap-3 flex-1 mx-4">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${(dato.valor / 1650000) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 min-w-[100px] text-right">
                      {formatCurrency(dato.valor)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Distribución por Planes */}
        <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-purple-600" />
              Distribución por Planes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {datosGraficos.distribucionPlanes.map((plan, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: plan.color }}
                    ></div>
                    <span className="text-sm font-medium text-gray-700">{plan.plan}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-600">{plan.cantidad} tenants</span>
                    <Badge className="bg-gray-100 text-gray-800">
                      {plan.porcentaje}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Funnel de Conversión */}
        <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-blue-600" />
              Funnel de Conversión
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {datosGraficos.conversionFunnel.map((etapa, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-32 text-sm font-medium text-gray-700">
                    {etapa.etapa}
                  </div>
                  <div className="flex-1 flex items-center gap-3">
                    <div className="flex-1 bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${etapa.porcentaje}%` }}
                      ></div>
                    </div>
                    <div className="flex items-center gap-3 min-w-[150px]">
                      <span className="text-sm font-semibold text-gray-900">
                        {formatNumber(etapa.cantidad)}
                      </span>
                      <Badge className="bg-blue-100 text-blue-800">
                        {etapa.porcentaje}%
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertas y Recomendaciones */}
      <Card className="bg-white/90 backdrop-blur-sm border border-orange-200/50 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-700">
            <Zap className="w-5 h-5" />
            Insights y Recomendaciones
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-100">
              <TrendingUp className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-green-900">Excelente crecimiento en MRR</p>
                <p className="text-sm text-green-700">El crecimiento del 12.5% está por encima del promedio de la industria (8-10%)</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
              <Target className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <p className="font-medium text-blue-900">Oportunidad de optimización</p>
                <p className="text-sm text-blue-700">La tasa de conversión del 66.7% es buena, pero se puede mejorar con mejor onboarding</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-100">
              <Activity className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="font-medium text-yellow-900">Monitorear churn rate</p>
                <p className="text-sm text-yellow-700">Aunque está bajando, mantener el churn por debajo del 5% es crítico para el crecimiento</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MetricasAvanzadas;
