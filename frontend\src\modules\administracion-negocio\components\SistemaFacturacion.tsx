import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import { 
  FileText, 
  Download, 
  Send, 
  Eye, 
  DollarSign,
  Calendar,
  Building2,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Filter,
  Search,
  Plus
} from 'lucide-react';

interface Factura {
  id: string;
  numero: string;
  tenant_name: string;
  tenant_email: string;
  fecha_emision: string;
  fecha_vencimiento: string;
  monto: number;
  impuestos: number;
  total: number;
  estado: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  plan: string;
  periodo: string;
  metodo_pago?: string;
  fecha_pago?: string;
}

const SistemaFacturacion: React.FC = () => {
  const [filtroEstado, setFiltroEstado] = useState('todas');
  const [filtroFecha, setFiltroFecha] = useState('30d');

  // Datos de ejemplo
  const facturas: Factura[] = [
    {
      id: '1',
      numero: 'FAC-2024-001',
      tenant_name: 'Hospital San José',
      tenant_email: '<EMAIL>',
      fecha_emision: '2024-11-01',
      fecha_vencimiento: '2024-11-15',
      monto: 450000,
      impuestos: 85500,
      total: 535500,
      estado: 'paid',
      plan: 'Premium',
      periodo: 'Noviembre 2024',
      metodo_pago: 'Tarjeta de Crédito',
      fecha_pago: '2024-11-10'
    },
    {
      id: '2',
      numero: 'FAC-2024-002',
      tenant_name: 'Clínica del Norte',
      tenant_email: '<EMAIL>',
      fecha_emision: '2024-11-01',
      fecha_vencimiento: '2024-11-20',
      monto: 200000,
      impuestos: 38000,
      total: 238000,
      estado: 'sent',
      plan: 'Básico',
      periodo: 'Noviembre 2024'
    },
    {
      id: '3',
      numero: 'FAC-2024-003',
      tenant_name: 'Centro Médico Sur',
      tenant_email: '<EMAIL>',
      fecha_emision: '2024-10-01',
      fecha_vencimiento: '2024-10-25',
      monto: 800000,
      impuestos: 152000,
      total: 952000,
      estado: 'overdue',
      plan: 'Enterprise',
      periodo: 'Octubre 2024'
    },
    {
      id: '4',
      numero: 'FAC-2024-004',
      tenant_name: 'IPS Salud Total',
      tenant_email: '<EMAIL>',
      fecha_emision: '2024-11-15',
      fecha_vencimiento: '2024-11-30',
      monto: 450000,
      impuestos: 85500,
      total: 535500,
      estado: 'draft',
      plan: 'Premium',
      periodo: 'Noviembre 2024'
    }
  ];

  const getEstadoInfo = (estado: string) => {
    switch (estado) {
      case 'draft':
        return { 
          label: 'Borrador', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <FileText className="w-4 h-4" /> 
        };
      case 'sent':
        return { 
          label: 'Enviada', 
          color: 'bg-blue-100 text-blue-800', 
          icon: <Send className="w-4 h-4" /> 
        };
      case 'paid':
        return { 
          label: 'Pagada', 
          color: 'bg-green-100 text-green-800', 
          icon: <CheckCircle className="w-4 h-4" /> 
        };
      case 'overdue':
        return { 
          label: 'Vencida', 
          color: 'bg-red-100 text-red-800', 
          icon: <AlertTriangle className="w-4 h-4" /> 
        };
      case 'cancelled':
        return { 
          label: 'Cancelada', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <XCircle className="w-4 h-4" /> 
        };
      default:
        return { 
          label: 'Desconocido', 
          color: 'bg-gray-100 text-gray-800', 
          icon: <Clock className="w-4 h-4" /> 
        };
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const resumenFacturacion = {
    total_facturado: facturas.reduce((sum, f) => sum + f.total, 0),
    total_pagado: facturas.filter(f => f.estado === 'paid').reduce((sum, f) => sum + f.total, 0),
    total_pendiente: facturas.filter(f => f.estado === 'sent').reduce((sum, f) => sum + f.total, 0),
    total_vencido: facturas.filter(f => f.estado === 'overdue').reduce((sum, f) => sum + f.total, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Sistema de Facturación</h3>
          <p className="text-gray-600">Gestión completa de facturas y cobros</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Exportar
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Nueva Factura
          </Button>
        </div>
      </div>

      {/* Resumen Financiero */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4 text-center">
            <FileText className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Total Facturado</p>
            <p className="text-xl font-bold text-blue-700">
              {formatCurrency(resumenFacturacion.total_facturado)}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4 text-center">
            <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Total Pagado</p>
            <p className="text-xl font-bold text-green-700">
              {formatCurrency(resumenFacturacion.total_pagado)}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Pendiente</p>
            <p className="text-xl font-bold text-yellow-700">
              {formatCurrency(resumenFacturacion.total_pendiente)}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4 text-center">
            <AlertTriangle className="w-8 h-8 text-red-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Vencido</p>
            <p className="text-xl font-bold text-red-700">
              {formatCurrency(resumenFacturacion.total_vencido)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-gray-500" />
              <input 
                type="text" 
                placeholder="Buscar facturas..." 
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select 
              value={filtroEstado} 
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todas">Todas las facturas</option>
              <option value="draft">Borradores</option>
              <option value="sent">Enviadas</option>
              <option value="paid">Pagadas</option>
              <option value="overdue">Vencidas</option>
            </select>

            <select 
              value={filtroFecha} 
              onChange={(e) => setFiltroFecha(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Últimos 7 días</option>
              <option value="30d">Últimos 30 días</option>
              <option value="90d">Últimos 90 días</option>
              <option value="1y">Último año</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Facturas */}
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle>Facturas Recientes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {facturas.map((factura) => {
              const estadoInfo = getEstadoInfo(factura.estado);
              
              return (
                <div key={factura.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-6 h-6 text-blue-600" />
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-3 mb-1">
                        <h4 className="font-semibold text-gray-900">{factura.numero}</h4>
                        <Badge className={estadoInfo.color}>
                          <div className="flex items-center gap-1">
                            {estadoInfo.icon}
                            {estadoInfo.label}
                          </div>
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">{factura.tenant_name}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                        <span>Plan: {factura.plan}</span>
                        <span>Periodo: {factura.periodo}</span>
                        <span>Emitida: {formatDate(factura.fecha_emision)}</span>
                        <span>Vence: {formatDate(factura.fecha_vencimiento)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(factura.total)}
                      </p>
                      <p className="text-xs text-gray-500">
                        Base: {formatCurrency(factura.monto)} + IVA
                      </p>
                      {factura.fecha_pago && (
                        <p className="text-xs text-green-600">
                          Pagado: {formatDate(factura.fecha_pago)}
                        </p>
                      )}
                    </div>
                    
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="w-3 h-3" />
                      </Button>
                      {factura.estado === 'draft' && (
                        <Button variant="outline" size="sm">
                          <Send className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Configuración de Facturación */}
      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle>Configuración de Facturación</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Facturación Automática</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Generar facturas automáticamente</p>
                    <p className="text-sm text-gray-600">5 días antes del vencimiento</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Activo</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Envío automático por email</p>
                    <p className="text-sm text-gray-600">Al momento de generación</p>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">Activo</Badge>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Recordatorios</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Recordatorio de vencimiento</p>
                    <p className="text-sm text-gray-600">3 días antes del vencimiento</p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">Activo</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Notificación de vencimiento</p>
                    <p className="text-sm text-gray-600">Al día siguiente del vencimiento</p>
                  </div>
                  <Badge className="bg-red-100 text-red-800">Activo</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SistemaFacturacion;
