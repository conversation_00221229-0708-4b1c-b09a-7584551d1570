import React from 'react';
import { useAuth } from '../../services/authService';
import { usePermissions } from '../../hooks/usePermissions';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';

const DebugAuth: React.FC = () => {
  const auth = useAuth();
  const permissions = usePermissions();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Debug de Autenticación y Permisos</h1>
        
        {/* Información de Autenticación */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle>Información de Autenticación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Usuario autenticado:</strong> {auth.isAuthenticated ? 'Sí' : 'No'}</p>
              <p><strong>Cargando:</strong> {auth.isLoading ? 'Sí' : 'No'}</p>
              <p><strong>Usuario:</strong></p>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(auth.user, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Información de Permisos */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle>Información de Permisos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Rol del usuario:</strong> {permissions.userRole}</p>
              <p><strong>Es administrador del sistema:</strong> {permissions.isSystemAdmin() ? 'Sí' : 'No'}</p>
              <p><strong>Es administrador de tenant:</strong> {permissions.isTenantAdmin() ? 'Sí' : 'No'}</p>
              
              <div className="mt-4">
                <p><strong>Acceso a módulos específicos:</strong></p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>dashboard-negocio: {permissions.hasModuleAccess('dashboard-negocio') ? '✅' : '❌'}</li>
                  <li>gestion-ips: {permissions.hasModuleAccess('gestion-ips') ? '✅' : '❌'}</li>
                  <li>administracion-negocio: {permissions.hasModuleAccess('administracion-negocio') ? '✅' : '❌'}</li>
                  <li>administracion: {permissions.hasModuleAccess('administracion') ? '✅' : '❌'}</li>
                </ul>
              </div>

              <div className="mt-4">
                <p><strong>Módulos accesibles:</strong></p>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(permissions.getAccessibleModules(), null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Botones de prueba */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle>Acciones de Prueba</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <button
                onClick={() => {
                  const mockUser = {
                    id: '1',
                    nombre: 'Super Administrador',
                    email: '<EMAIL>',
                    rol: 'SUPER_ADMIN',
                    hospital_id: 1,
                    username: 'superadmin'
                  };
                  localStorage.setItem('user', JSON.stringify(mockUser));
                  localStorage.setItem('token', 'mock-token-' + Date.now());
                  window.location.reload();
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg mr-4"
              >
                Establecer Usuario SUPER_ADMIN
              </button>

              <button
                onClick={() => {
                  localStorage.removeItem('user');
                  localStorage.removeItem('token');
                  window.location.reload();
                }}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
              >
                Limpiar Usuario
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DebugAuth;
