import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Switch } from '../../components/ui/switch';
import { Building2 } from 'lucide-react';

const TestComponent: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState('test');
  const [switchValue, setSwitchValue] = React.useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-gray-900">Test de Componentes UI</h1>

        <Card className="mb-6 bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <Building2 className="w-5 h-5 text-blue-600" />
              Test Card
            </CardTitle>
          </CardHeader>
          <CardContent>
          <p>Este es un test de los componentes UI.</p>
          
          <div className="flex gap-2 mt-4">
            <Button variant="primary">Primary</Button>
            <Button variant="outline">Outline</Button>
          </div>
          
          <div className="flex gap-2 mt-4">
            <Badge variant="default">Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
          
          <div className="mt-4">
            <Switch 
              checked={switchValue} 
              onCheckedChange={setSwitchValue}
            />
            <span className="ml-2">Switch: {switchValue ? 'On' : 'Off'}</span>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="bg-gray-100/80 backdrop-blur-sm">
          <TabsTrigger value="test">Test Tab</TabsTrigger>
          <TabsTrigger value="another">Another Tab</TabsTrigger>
        </TabsList>

        <TabsContent value="test">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-4">
              <p className="text-gray-700">Contenido del primer tab</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="another">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-4">
              <p className="text-gray-700">Contenido del segundo tab</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </div>
  );
};

export default TestComponent;
