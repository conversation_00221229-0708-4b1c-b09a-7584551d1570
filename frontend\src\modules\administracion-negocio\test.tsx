import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Switch } from '../../components/ui/switch';
import { Building2 } from 'lucide-react';

const TestComponent: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState('test');
  const [switchValue, setSwitchValue] = React.useState(false);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Test de Componentes</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Test Card
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Este es un test de los componentes UI.</p>
          
          <div className="flex gap-2 mt-4">
            <Button variant="primary">Primary</Button>
            <Button variant="outline">Outline</Button>
          </div>
          
          <div className="flex gap-2 mt-4">
            <Badge variant="default">Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
          
          <div className="mt-4">
            <Switch 
              checked={switchValue} 
              onCheckedChange={setSwitchValue}
            />
            <span className="ml-2">Switch: {switchValue ? 'On' : 'Off'}</span>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="test">Test Tab</TabsTrigger>
          <TabsTrigger value="another">Another Tab</TabsTrigger>
        </TabsList>
        
        <TabsContent value="test">
          <Card>
            <CardContent>
              <p>Contenido del primer tab</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="another">
          <Card>
            <CardContent>
              <p>Contenido del segundo tab</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TestComponent;
