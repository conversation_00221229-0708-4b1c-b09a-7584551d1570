import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import {
  Truck,
  Save,
  ArrowLeft,
  MapPin,
  Settings,
  Wifi,
  WifiOff,
  Battery,
  Navigation,
  Signal
} from 'lucide-react';

interface DispositivoGPS {
  id: string;
  imei: string;
  estado: 'conectado' | 'desconectado' | 'sin_señal';
  bateria: number;
  ultima_actualizacion: string;
  intervalo_reporte: number;
}

export const AmbulanciaEditar = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    placa: '',
    tipo: 'Básica' as 'Básica' | 'Medicalizada' | 'Transporte_Asistencial',
    marca: '',
    modelo: '',
    año: new Date().getFullYear(),
    estado: 'Activo' as 'Activo' | 'Inactivo' | 'En Mantenimiento' | 'En Servicio',
    capacidad: 4,
    dispositivo_gps: {
      id: '',
      imei: '',
      estado: 'desconectado' as 'conectado' | 'desconectado' | 'sin_señal',
      bateria: 0,
      ultima_actualizacion: '',
      intervalo_reporte: 30
    } as DispositivoGPS,
    ubicacion_base: {
      lat: 4.6097,
      lng: -74.0817,
      direccion: ''
    },
    equipoMedico: {
      desfibrilador: false,
      respirador: false,
      monitor_signos_vitales: false,
      oxigeno: false,
      camilla: false,
      kit_primeros_auxilios: false
    }
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Simulación de carga de datos desde API
    setTimeout(() => {
      // Datos de ejemplo para desarrollo
      const mockData = {
        placa: 'ABC123',
        tipo: 'Medicalizada' as 'Básica' | 'Medicalizada' | 'Transporte_Asistencial',
        marca: 'Mercedes',
        modelo: 'Sprinter 2023',
        año: 2023,
        estado: 'Activo' as 'Activo' | 'Inactivo' | 'En Mantenimiento' | 'En Servicio',
        capacidad: 4,
        dispositivo_gps: {
          id: 'GPS001',
          imei: '123456789012345',
          estado: 'conectado' as 'conectado' | 'desconectado' | 'sin_señal',
          bateria: 85,
          ultima_actualizacion: new Date().toISOString(),
          intervalo_reporte: 30
        },
        ubicacion_base: {
          lat: 4.6097,
          lng: -74.0817,
          direccion: 'Hospital Central - Estación 1'
        },
        equipoMedico: {
          desfibrilador: true,
          respirador: true,
          monitor_signos_vitales: true,
          oxigeno: true,
          camilla: true,
          kit_primeros_auxilios: true
        }
      };

      setFormData(mockData);
      setInitialLoading(false);
    }, 1000);
  }, [id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleEquipoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      equipoMedico: {
        ...formData.equipoMedico,
        [name]: checked
      }
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.placa.trim()) {
      newErrors.placa = 'La placa es obligatoria';
    } else if (!/^[A-Z0-9]{6}$/.test(formData.placa)) {
      newErrors.placa = 'La placa debe tener 6 caracteres alfanuméricos';
    }
    
    if (!formData.modelo.trim()) {
      newErrors.modelo = 'El modelo es obligatorio';
    }
    
    if (!formData.capacidad || formData.capacidad < 1) {
      newErrors.capacidad = 'La capacidad debe ser al menos 1';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      // Simulación de envío a API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redireccionar a la vista de detalles
      navigate(`/ambulancias/${id}`);
    } catch (error) {
      console.error('Error al actualizar la ambulancia:', error);
      setErrors({
        submit: 'Error al actualizar la ambulancia. Por favor, inténtelo de nuevo.'
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          <FontAwesomeIcon icon={faAmbulance} className="mr-2" />
          Editar Ambulancia
        </h1>
        <div className="flex space-x-2">
          <Link
            to={`/ambulancias/${id}`}
            className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md flex items-center"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Volver
          </Link>
        </div>
      </div>

      <div className="glassmorphism p-6 rounded-lg">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-800 mb-4">Información General</h2>
              
              <div className="mb-4">
                <label htmlFor="placa" className="block text-gray-700 font-medium mb-2">
                  Placa *
                </label>
                <input
                  type="text"
                  id="placa"
                  name="placa"
                  value={formData.placa}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 rounded-md border ${
                    errors.placa ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Ej: ABC123"
                />
                {errors.placa && (
                  <p className="text-red-500 text-sm mt-1">{errors.placa}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label htmlFor="modelo" className="block text-gray-700 font-medium mb-2">
                  Modelo *
                </label>
                <input
                  type="text"
                  id="modelo"
                  name="modelo"
                  value={formData.modelo}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 rounded-md border ${
                    errors.modelo ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Ej: Mercedes Sprinter 2023"
                />
                {errors.modelo && (
                  <p className="text-red-500 text-sm mt-1">{errors.modelo}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label htmlFor="estado" className="block text-gray-700 font-medium mb-2">
                  Estado *
                </label>
                <select
                  id="estado"
                  name="estado"
                  value={formData.estado}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Activo">Activo</option>
                  <option value="Inactivo">Inactivo</option>
                  <option value="En Mantenimiento">En Mantenimiento</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label htmlFor="capacidad" className="block text-gray-700 font-medium mb-2">
                  Capacidad (pacientes) *
                </label>
                <input
                  type="number"
                  id="capacidad"
                  name="capacidad"
                  value={formData.capacidad}
                  onChange={handleChange}
                  min="1"
                  max="10"
                  className={`w-full px-4 py-2 rounded-md border ${
                    errors.capacidad ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                {errors.capacidad && (
                  <p className="text-red-500 text-sm mt-1">{errors.capacidad}</p>
                )}
              </div>
            </div>
            
            <div>
              <h2 className="text-xl font-bold text-gray-800 mb-4">Equipo Médico</h2>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="desfibrilador"
                    name="desfibrilador"
                    checked={formData.equipoMedico.desfibrilador}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="desfibrilador" className="ml-2 text-gray-700">
                    Desfibrilador
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="respirador"
                    name="respirador"
                    checked={formData.equipoMedico.respirador}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="respirador" className="ml-2 text-gray-700">
                    Respirador
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="monitor_signos_vitales"
                    name="monitor_signos_vitales"
                    checked={formData.equipoMedico.monitor_signos_vitales}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="monitor_signos_vitales" className="ml-2 text-gray-700">
                    Monitor de signos vitales
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="oxigeno"
                    name="oxigeno"
                    checked={formData.equipoMedico.oxigeno}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="oxigeno" className="ml-2 text-gray-700">
                    Oxígeno
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="camilla"
                    name="camilla"
                    checked={formData.equipoMedico.camilla}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="camilla" className="ml-2 text-gray-700">
                    Camilla
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="kit_primeros_auxilios"
                    name="kit_primeros_auxilios"
                    checked={formData.equipoMedico.kit_primeros_auxilios}
                    onChange={handleEquipoChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="kit_primeros_auxilios" className="ml-2 text-gray-700">
                    Kit de primeros auxilios
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          {errors.submit && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {errors.submit}
            </div>
          )}
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className={`bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md flex items-center ${
                loading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {loading ? (
                <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
              ) : (
                <FontAwesomeIcon icon={faSave} className="mr-2" />
              )}
              Guardar Cambios
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
