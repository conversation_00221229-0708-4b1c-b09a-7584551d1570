import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faAmbulance,
  faArrowLeft,
  faSave,
  faMapMarkerAlt,
  faLocationDot,
  faMobile,
  faCog,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación basado en la estructura de la BD
const ambulanciaSchema = z.object({
  // Información básica del vehículo
  placa: z.string()
    .min(6, 'La placa debe tener al menos 6 caracteres')
    .max(10, 'La placa no puede exceder 10 caracteres')
    .regex(/^[A-Z0-9]+$/, 'La placa solo puede contener letras y números'),
  tipo: z.enum(['Básica', 'Medicalizada', 'Transporte_Asistencial'], {
    required_error: 'Debe seleccionar un tipo de ambulancia'
  }),
  marca: z.string().min(2, 'La marca es requerida'),
  modelo: z.string().min(2, 'El modelo es requerido'),
  año: z.number()
    .min(1990, 'El año debe ser mayor a 1990')
    .max(new Date().getFullYear() + 1, 'El año no puede ser futuro'),
  numero_motor: z.string().optional(),
  numero_chasis: z.string().optional(),

  // Ubicación base (requerida)
  ubicacion_base_lat: z.number()
    .min(-90, 'Latitud inválida')
    .max(90, 'Latitud inválida'),
  ubicacion_base_lng: z.number()
    .min(-180, 'Longitud inválida')
    .max(180, 'Longitud inválida'),
  direccion_base: z.string().min(10, 'La dirección base es requerida'),

  // Configuración GPS/Celular
  dispositivo_gps_id: z.string().optional(),
  dispositivo_celular_imei: z.string()
    .regex(/^\d{15}$/, 'IMEI debe tener exactamente 15 dígitos')
    .optional(),
  intervalo_reporte_gps: z.number()
    .min(10, 'Intervalo mínimo: 10 segundos')
    .max(3600, 'Intervalo máximo: 1 hora')
    .default(30),

  // Información adicional
  capacidad_pacientes: z.number().min(1).max(10).default(1),
  observaciones: z.string().optional(),
});

type AmbulanciaFormData = z.infer<typeof ambulanciaSchema>;

export const AmbulanciaNueva = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ubicacionSeleccionada, setUbicacionSeleccionada] = useState<{lat: number, lng: number} | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<AmbulanciaFormData>({
    resolver: zodResolver(ambulanciaSchema),
    defaultValues: {
      intervalo_reporte_gps: 30,
      capacidad_pacientes: 1,
      año: new Date().getFullYear()
    }
  });

  const tipoAmbulancia = watch('tipo');

  const onSubmit = async (data: AmbulanciaFormData) => {
    setIsSubmitting(true);
    try {
      // TODO: Implementar llamada a la API
      console.log('Datos de ambulancia:', data);

      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Ambulancia registrada exitosamente');
      navigate('/ambulancias');
    } catch (error) {
      console.error('Error al registrar ambulancia:', error);
      toast.error('Error al registrar la ambulancia');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Función para obtener ubicación actual
  const obtenerUbicacionActual = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          setValue('ubicacion_base_lat', lat);
          setValue('ubicacion_base_lng', lng);
          setUbicacionSeleccionada({ lat, lng });
          toast.success('Ubicación obtenida exitosamente');
        },
        (error) => {
          console.error('Error al obtener ubicación:', error);
          toast.error('No se pudo obtener la ubicación actual');
        }
      );
    } else {
      toast.error('Geolocalización no soportada por el navegador');
    }
  };

  // Función para generar IMEI aleatorio (para pruebas)
  const generarIMEI = () => {
    const imei = Array.from({length: 15}, () => Math.floor(Math.random() * 10)).join('');
    setValue('dispositivo_celular_imei', imei);
    toast.success('IMEI generado para pruebas');
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-lg max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-primary flex items-center">
          <FontAwesomeIcon icon={faAmbulance} className="mr-2" />
          Nueva Ambulancia
        </h1>
        <Button variant="secondary" onClick={() => navigate('/ambulancias')}>
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
          Volver
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Información Básica del Vehículo */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faAmbulance} className="mr-2" />
            📋 Información del Vehículo
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Placa *
              </label>
              <Input
                {...register('placa')}
                placeholder="ABC123"
                error={errors.placa?.message}
                className="uppercase"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Ambulancia *
              </label>
              <select
                {...register('tipo')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione tipo</option>
                <option value="Básica">🚑 Básica</option>
                <option value="Medicalizada">🏥 Medicalizada</option>
                <option value="Transporte_Asistencial">🚐 Transporte Asistencial</option>
              </select>
              {errors.tipo && (
                <p className="mt-1 text-sm text-error">{errors.tipo.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Marca *
              </label>
              <Input
                {...register('marca')}
                placeholder="Mercedes-Benz"
                error={errors.marca?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Modelo *
              </label>
              <Input
                {...register('modelo')}
                placeholder="Sprinter"
                error={errors.modelo?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Año *
              </label>
              <Input
                {...register('año', { valueAsNumber: true })}
                type="number"
                min="1990"
                max={new Date().getFullYear() + 1}
                error={errors.año?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Capacidad de Pacientes *
              </label>
              <Input
                {...register('capacidad_pacientes', { valueAsNumber: true })}
                type="number"
                min="1"
                max="10"
                error={errors.capacidad_pacientes?.message}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Número de Motor
              </label>
              <Input
                {...register('numero_motor')}
                placeholder="Opcional"
                error={errors.numero_motor?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Número de Chasis
              </label>
              <Input
                {...register('numero_chasis')}
                placeholder="Opcional"
                error={errors.numero_chasis?.message}
              />
            </div>
          </div>
        </div>

        {/* Configuración de Ubicación Base */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2" />
            📍 Ubicación Base
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-3">
              <label className="block text-sm font-medium text-secondary mb-1">
                Dirección Base *
              </label>
              <Input
                {...register('direccion_base')}
                placeholder="Dirección completa de la base de la ambulancia"
                error={errors.direccion_base?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Latitud *
              </label>
              <Input
                {...register('ubicacion_base_lat', { valueAsNumber: true })}
                type="number"
                step="any"
                placeholder="4.6097"
                error={errors.ubicacion_base_lat?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Longitud *
              </label>
              <Input
                {...register('ubicacion_base_lng', { valueAsNumber: true })}
                type="number"
                step="any"
                placeholder="-74.0817"
                error={errors.ubicacion_base_lng?.message}
              />
            </div>

            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                onClick={obtenerUbicacionActual}
                className="w-full"
              >
                <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2" />
                Obtener Ubicación Actual
              </Button>
            </div>
          </div>

          {ubicacionSeleccionada && (
            <div className="mt-4 p-3 bg-success/20 border border-success/30 rounded-lg">
              <p className="text-sm text-success">
                ✅ Ubicación seleccionada: {ubicacionSeleccionada.lat.toFixed(6)}, {ubicacionSeleccionada.lng.toFixed(6)}
              </p>
            </div>
          )}
        </div>

        {/* Configuración de Instrumentos de Geolocalización */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faLocationDot} className="mr-2" />
            🛰️ Instrumentos de Geolocalización
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Configuración GPS */}
            <div className="space-y-4">
              <h4 className="font-medium text-secondary flex items-center">
                <FontAwesomeIcon icon={faLocationDot} className="mr-2" />
                Dispositivo GPS
              </h4>

              <div>
                <label className="block text-sm font-medium text-secondary mb-1">
                  ID del Dispositivo GPS
                </label>
                <Input
                  {...register('dispositivo_gps_id')}
                  placeholder="GPS-001 (Opcional)"
                  error={errors.dispositivo_gps_id?.message}
                />
                <p className="text-xs text-muted mt-1">
                  Identificador único del dispositivo GPS instalado
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary mb-1">
                  Intervalo de Reporte GPS (segundos) *
                </label>
                <Input
                  {...register('intervalo_reporte_gps', { valueAsNumber: true })}
                  type="number"
                  min="10"
                  max="3600"
                  error={errors.intervalo_reporte_gps?.message}
                />
                <p className="text-xs text-muted mt-1">
                  Frecuencia de envío de ubicación (10-3600 segundos)
                </p>
              </div>
            </div>

            {/* Configuración Celular */}
            <div className="space-y-4">
              <h4 className="font-medium text-secondary flex items-center">
                <FontAwesomeIcon icon={faMobile} className="mr-2" />
                Dispositivo Celular
              </h4>

              <div>
                <label className="block text-sm font-medium text-secondary mb-1">
                  IMEI del Dispositivo Celular
                </label>
                <div className="flex gap-2">
                  <Input
                    {...register('dispositivo_celular_imei')}
                    placeholder="123456789012345"
                    error={errors.dispositivo_celular_imei?.message}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={generarIMEI}
                    className="px-3"
                  >
                    🎲
                  </Button>
                </div>
                <p className="text-xs text-muted mt-1">
                  IMEI de 15 dígitos para rastreo celular (opcional)
                </p>
              </div>

              <div className="p-3 bg-info/20 border border-info/30 rounded-lg">
                <h5 className="font-medium text-info mb-2 flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                  Información Técnica
                </h5>
                <ul className="text-xs text-muted space-y-1">
                  <li>• GPS: Ubicación precisa por satélite</li>
                  <li>• Celular: Respaldo cuando GPS no está disponible</li>
                  <li>• Intervalo recomendado: 30-60 segundos</li>
                  <li>• Menor intervalo = mayor precisión, mayor consumo</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Observaciones Adicionales */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faCog} className="mr-2" />
            📝 Información Adicional
          </h3>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Observaciones
            </label>
            <textarea
              {...register('observaciones')}
              rows={4}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
              placeholder="Información adicional sobre la ambulancia, equipos especiales, restricciones, etc."
            />
            {errors.observaciones && (
              <p className="mt-1 text-sm text-error">{errors.observaciones.message}</p>
            )}
          </div>

          {/* Información del tipo de ambulancia seleccionado */}
          {tipoAmbulancia && (
            <div className="mt-4 p-3 bg-info/20 border border-info/30 rounded-lg">
              <h5 className="font-medium text-info mb-2">
                Características del tipo: {tipoAmbulancia}
              </h5>
              <div className="text-xs text-muted">
                {tipoAmbulancia === 'Básica' && (
                  <ul className="space-y-1">
                    <li>• Transporte de pacientes estables</li>
                    <li>• Equipos básicos de primeros auxilios</li>
                    <li>• Personal técnico en emergencias médicas</li>
                  </ul>
                )}
                {tipoAmbulancia === 'Medicalizada' && (
                  <ul className="space-y-1">
                    <li>• Transporte de pacientes críticos</li>
                    <li>• Equipos médicos avanzados</li>
                    <li>• Personal médico especializado</li>
                    <li>• Capacidad de soporte vital avanzado</li>
                  </ul>
                )}
                {tipoAmbulancia === 'Transporte_Asistencial' && (
                  <ul className="space-y-1">
                    <li>• Transporte de pacientes no críticos</li>
                    <li>• Traslados programados</li>
                    <li>• Equipos básicos de monitoreo</li>
                  </ul>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Botones de Acción */}
        <div className="flex justify-end gap-4 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate('/ambulancias')}
          >
            Cancelar
          </Button>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                Guardando...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="mr-2" />
                Guardar Ambulancia
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
