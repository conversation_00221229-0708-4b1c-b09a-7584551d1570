import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import {
  Truck,
  Plus,
  Edit,
  Trash2,
  Search,
  MapPin,
  Settings,
  Eye,
  Navigation,
  Wifi,
  WifiOff,
  Battery,
  Signal
} from 'lucide-react';

// Definición de tipos
interface DispositivoGPS {
  id: string;
  imei: string;
  estado: 'conectado' | 'desconectado' | 'sin_señal';
  bateria: number;
  ultima_actualizacion: string;
  intervalo_reporte: number;
}

interface Ambulancia {
  id: number;
  placa: string;
  tipo: 'Básica' | 'Medicalizada' | 'Transporte_Asistencial';
  marca: string;
  modelo: string;
  año: number;
  estado: 'Activo' | 'Inactivo' | 'En Mantenimiento' | 'En Servicio';
  capacidad: number;
  ubicacion_actual?: {
    lat: number;
    lng: number;
    direccion?: string;
    timestamp?: string;
  };
  dispositivo_gps?: DispositivoGPS;
  ubicacion_base: {
    lat: number;
    lng: number;
    direccion: string;
  };
}

export const Ambulancias = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [ambulancias, setAmbulancias] = useState<Ambulancia[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');

  // Datos de ejemplo para desarrollo
  useEffect(() => {
    // Simulación de carga de datos desde API
    setTimeout(() => {
      const mockData: Ambulancia[] = [
        {
          id: 1,
          placa: 'ABC123',
          tipo: 'Medicalizada',
          marca: 'Mercedes',
          modelo: 'Sprinter 2023',
          año: 2023,
          estado: 'En Servicio',
          capacidad: 4,
          ubicacion_actual: {
            lat: 4.6097,
            lng: -74.0817,
            direccion: 'Calle 26 #68-35, Bogotá',
            timestamp: new Date().toISOString()
          },
          dispositivo_gps: {
            id: 'GPS001',
            imei: '123456789012345',
            estado: 'conectado',
            bateria: 85,
            ultima_actualizacion: new Date().toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6097,
            lng: -74.0817,
            direccion: 'Hospital Central - Estación 1'
          }
        },
        {
          id: 2,
          placa: 'DEF456',
          tipo: 'Básica',
          marca: 'Ford',
          modelo: 'Transit 2022',
          año: 2022,
          estado: 'Activo',
          capacidad: 3,
          ubicacion_actual: {
            lat: 4.6261,
            lng: -74.0644,
            direccion: 'Carrera 15 #93-47, Bogotá',
            timestamp: new Date(Date.now() - 300000).toISOString()
          },
          dispositivo_gps: {
            id: 'GPS002',
            imei: '234567890123456',
            estado: 'conectado',
            bateria: 92,
            ultima_actualizacion: new Date(Date.now() - 300000).toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6261,
            lng: -74.0644,
            direccion: 'Clínica Norte - Estación 2'
          }
        },
        {
          id: 3,
          placa: 'GHI789',
          tipo: 'Medicalizada',
          marca: 'Renault',
          modelo: 'Master 2021',
          año: 2021,
          estado: 'En Mantenimiento',
          capacidad: 4,
          dispositivo_gps: {
            id: 'GPS003',
            imei: '345678901234567',
            estado: 'desconectado',
            bateria: 0,
            ultima_actualizacion: new Date(Date.now() - 86400000).toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Taller de Mantenimiento'
          }
        },
        {
          id: 4,
          placa: 'JKL012',
          tipo: 'Transporte_Asistencial',
          marca: 'Chevrolet',
          modelo: 'Express 2023',
          año: 2023,
          estado: 'Activo',
          capacidad: 5,
          ubicacion_actual: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Avenida 68 #45-23, Bogotá',
            timestamp: new Date(Date.now() - 120000).toISOString()
          },
          dispositivo_gps: {
            id: 'GPS004',
            imei: '456789012345678',
            estado: 'sin_señal',
            bateria: 45,
            ultima_actualizacion: new Date(Date.now() - 1800000).toISOString(),
            intervalo_reporte: 60
          },
          ubicacion_base: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Centro Médico Sur - Estación 3'
          }
        }
      ];
      setAmbulancias(mockData);
      setLoading(false);
    }, 1000);
  }, []);

  // Filtrar ambulancias
  const filteredAmbulancias = ambulancias.filter(ambulancia => {
    const matchesSearch =
      ambulancia.placa.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.estado.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = filtroEstado === 'todos' || ambulancia.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || ambulancia.tipo === filtroTipo;

    return matchesSearch && matchesEstado && matchesTipo;
  });

  // Eliminar ambulancia (simulado)
  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de que desea eliminar esta ambulancia?')) {
      setAmbulancias(ambulancias.filter(ambulancia => ambulancia.id !== id));
    }
  };

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return { color: 'bg-green-100 text-green-800', label: 'Activo' };
      case 'En Servicio':
        return { color: 'bg-blue-100 text-blue-800', label: 'En Servicio' };
      case 'Inactivo':
        return { color: 'bg-gray-100 text-gray-800', label: 'Inactivo' };
      case 'En Mantenimiento':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'En Mantenimiento' };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado };
    }
  };

  // Obtener información del dispositivo GPS
  const getGPSStatusInfo = (dispositivo?: DispositivoGPS) => {
    if (!dispositivo) {
      return { icon: <WifiOff className="w-4 h-4" />, color: 'text-gray-500', label: 'Sin GPS' };
    }

    switch (dispositivo.estado) {
      case 'conectado':
        return { icon: <Wifi className="w-4 h-4" />, color: 'text-green-600', label: 'Conectado' };
      case 'desconectado':
        return { icon: <WifiOff className="w-4 h-4" />, color: 'text-red-600', label: 'Desconectado' };
      case 'sin_señal':
        return { icon: <Signal className="w-4 h-4" />, color: 'text-yellow-600', label: 'Sin Señal' };
      default:
        return { icon: <WifiOff className="w-4 h-4" />, color: 'text-gray-500', label: 'Desconocido' };
    }
  };

  // Formatear tiempo transcurrido
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Ahora';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h`;
    return `${Math.floor(diffMins / 1440)}d`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">
          <FontAwesomeIcon icon={faAmbulance} className="mr-2" />
          Gestión de Ambulancias
        </h1>
        <Link
          to="/ambulancias/nueva"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Nueva Ambulancia
        </Link>
      </div>

      <div className="glassmorphism p-6 rounded-lg mb-6">
        <div className="flex flex-col md:flex-row justify-between items-center mb-4">
          <div className="w-full md:w-1/3 mb-4 md:mb-0">
            <div className="relative">
              <input
                type="text"
                placeholder="Buscar ambulancia..."
                className="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-800 bg-opacity-50 text-white placeholder-gray-400"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute right-3 top-3 text-gray-400"
              />
            </div>
          </div>
          <div className="w-full md:w-auto">
            <Link
              to="/ambulancias/mapa"
              className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md flex items-center"
            >
              <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2" />
              Ver en Mapa
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-gray-800 bg-opacity-50 rounded-lg overflow-hidden">
                <thead className="bg-gray-700 bg-opacity-70">
                  <tr>
                    <th className="py-3 px-4 text-left text-white">ID</th>
                    <th className="py-3 px-4 text-left text-white">Placa</th>
                    <th className="py-3 px-4 text-left text-white">Modelo</th>
                    <th className="py-3 px-4 text-left text-white">Estado</th>
                    <th className="py-3 px-4 text-left text-white">Capacidad</th>
                    <th className="py-3 px-4 text-left text-white">Ubicación</th>
                    <th className="py-3 px-4 text-left text-white">Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {currentItems.length > 0 ? (
                    currentItems.map((ambulancia) => (
                      <tr key={ambulancia.id} className="border-b border-gray-700 hover:bg-gray-700 hover:bg-opacity-70">
                        <td className="py-3 px-4 text-white">{ambulancia.id}</td>
                        <td className="py-3 px-4 font-medium text-white">{ambulancia.placa}</td>
                        <td className="py-3 px-4 text-white">{ambulancia.modelo}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(ambulancia.estado)}`}>
                            {ambulancia.estado}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-white">{ambulancia.capacidad} pacientes</td>
                        <td className="py-3 px-4">
                          {ambulancia.ubicacion_actual ? (
                            <span className="text-green-300 flex items-center">
                              <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-1" />
                              Disponible
                            </span>
                          ) : (
                            <span className="text-gray-400">No disponible</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Link
                              to={`/ambulancias/${ambulancia.id}`}
                              className="text-blue-300 hover:text-blue-100"
                              title="Ver detalles"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                            </Link>
                            <Link
                              to={`/ambulancias/editar/${ambulancia.id}`}
                              className="text-yellow-300 hover:text-yellow-100"
                              title="Editar"
                            >
                              <FontAwesomeIcon icon={faEdit} />
                            </Link>
                            <button
                              onClick={() => handleDelete(ambulancia.id)}
                              className="text-red-300 hover:text-red-100"
                              title="Eliminar"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="py-4 px-4 text-center text-gray-300">
                        No se encontraron ambulancias
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Paginación */}
            {filteredAmbulancias.length > itemsPerPage && (
              <div className="flex justify-center mt-6">
                <nav>
                  <ul className="flex space-x-2">
                    <li>
                      <button
                        onClick={() => paginate(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded-md ${
                          currentPage === 1
                            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        Anterior
                      </button>
                    </li>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                      <li key={number}>
                        <button
                          onClick={() => paginate(number)}
                          className={`px-3 py-1 rounded-md ${
                            currentPage === number
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                        >
                          {number}
                        </button>
                      </li>
                    ))}
                    <li>
                      <button
                        onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded-md ${
                          currentPage === totalPages
                            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        Siguiente
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
