import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import {
  Truck,
  Plus,
  Edit,
  Trash2,
  Search,
  MapPin,
  Settings,
  Eye,
  Navigation,
  Wifi,
  WifiOff,
  Battery,
  Signal
} from 'lucide-react';

// Definición de tipos
interface DispositivoGPS {
  id: string;
  imei: string;
  estado: 'conectado' | 'desconectado' | 'sin_señal';
  bateria: number;
  ultima_actualizacion: string;
  intervalo_reporte: number;
}

interface Ambulancia {
  id: number;
  placa: string;
  tipo: 'Básica' | 'Medicalizada' | 'Transporte_Asistencial';
  marca: string;
  modelo: string;
  año: number;
  estado: 'Activo' | 'Inactivo' | 'En Mantenimiento' | 'En Servicio';
  capacidad: number;
  ubicacion_actual?: {
    lat: number;
    lng: number;
    direccion?: string;
    timestamp?: string;
  };
  dispositivo_gps?: DispositivoGPS;
  ubicacion_base: {
    lat: number;
    lng: number;
    direccion: string;
  };
}

export const Ambulancias = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [ambulancias, setAmbulancias] = useState<Ambulancia[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');

  // Datos de ejemplo para desarrollo
  useEffect(() => {
    // Simulación de carga de datos desde API
    setTimeout(() => {
      const mockData: Ambulancia[] = [
        {
          id: 1,
          placa: 'ABC123',
          tipo: 'Medicalizada',
          marca: 'Mercedes',
          modelo: 'Sprinter 2023',
          año: 2023,
          estado: 'En Servicio',
          capacidad: 4,
          ubicacion_actual: {
            lat: 4.6097,
            lng: -74.0817,
            direccion: 'Calle 26 #68-35, Bogotá',
            timestamp: new Date().toISOString()
          },
          dispositivo_gps: {
            id: 'GPS001',
            imei: '123456789012345',
            estado: 'conectado',
            bateria: 85,
            ultima_actualizacion: new Date().toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6097,
            lng: -74.0817,
            direccion: 'Hospital Central - Estación 1'
          }
        },
        {
          id: 2,
          placa: 'DEF456',
          tipo: 'Básica',
          marca: 'Ford',
          modelo: 'Transit 2022',
          año: 2022,
          estado: 'Activo',
          capacidad: 3,
          ubicacion_actual: {
            lat: 4.6261,
            lng: -74.0644,
            direccion: 'Carrera 15 #93-47, Bogotá',
            timestamp: new Date(Date.now() - 300000).toISOString()
          },
          dispositivo_gps: {
            id: 'GPS002',
            imei: '234567890123456',
            estado: 'conectado',
            bateria: 92,
            ultima_actualizacion: new Date(Date.now() - 300000).toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6261,
            lng: -74.0644,
            direccion: 'Clínica Norte - Estación 2'
          }
        },
        {
          id: 3,
          placa: 'GHI789',
          tipo: 'Medicalizada',
          marca: 'Renault',
          modelo: 'Master 2021',
          año: 2021,
          estado: 'En Mantenimiento',
          capacidad: 4,
          dispositivo_gps: {
            id: 'GPS003',
            imei: '345678901234567',
            estado: 'desconectado',
            bateria: 0,
            ultima_actualizacion: new Date(Date.now() - 86400000).toISOString(),
            intervalo_reporte: 30
          },
          ubicacion_base: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Taller de Mantenimiento'
          }
        },
        {
          id: 4,
          placa: 'JKL012',
          tipo: 'Transporte_Asistencial',
          marca: 'Chevrolet',
          modelo: 'Express 2023',
          año: 2023,
          estado: 'Activo',
          capacidad: 5,
          ubicacion_actual: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Avenida 68 #45-23, Bogotá',
            timestamp: new Date(Date.now() - 120000).toISOString()
          },
          dispositivo_gps: {
            id: 'GPS004',
            imei: '456789012345678',
            estado: 'sin_señal',
            bateria: 45,
            ultima_actualizacion: new Date(Date.now() - 1800000).toISOString(),
            intervalo_reporte: 60
          },
          ubicacion_base: {
            lat: 4.6481,
            lng: -74.1019,
            direccion: 'Centro Médico Sur - Estación 3'
          }
        }
      ];
      setAmbulancias(mockData);
      setLoading(false);
    }, 1000);
  }, []);

  // Filtrar ambulancias
  const filteredAmbulancias = ambulancias.filter(ambulancia => {
    const matchesSearch =
      ambulancia.placa.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulancia.estado.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = filtroEstado === 'todos' || ambulancia.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || ambulancia.tipo === filtroTipo;

    return matchesSearch && matchesEstado && matchesTipo;
  });

  // Eliminar ambulancia (simulado)
  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de que desea eliminar esta ambulancia?')) {
      setAmbulancias(ambulancias.filter(ambulancia => ambulancia.id !== id));
    }
  };

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return { color: 'bg-green-100 text-green-800', label: 'Activo' };
      case 'En Servicio':
        return { color: 'bg-blue-100 text-blue-800', label: 'En Servicio' };
      case 'Inactivo':
        return { color: 'bg-gray-100 text-gray-800', label: 'Inactivo' };
      case 'En Mantenimiento':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'En Mantenimiento' };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado };
    }
  };

  // Obtener información del dispositivo GPS
  const getGPSStatusInfo = (dispositivo?: DispositivoGPS) => {
    if (!dispositivo) {
      return { icon: <WifiOff className="w-4 h-4" />, color: 'text-gray-500', label: 'Sin GPS' };
    }

    switch (dispositivo.estado) {
      case 'conectado':
        return { icon: <Wifi className="w-4 h-4" />, color: 'text-green-600', label: 'Conectado' };
      case 'desconectado':
        return { icon: <WifiOff className="w-4 h-4" />, color: 'text-red-600', label: 'Desconectado' };
      case 'sin_señal':
        return { icon: <Signal className="w-4 h-4" />, color: 'text-yellow-600', label: 'Sin Señal' };
      default:
        return { icon: <WifiOff className="w-4 h-4" />, color: 'text-gray-500', label: 'Desconocido' };
    }
  };

  // Formatear tiempo transcurrido
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Ahora';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h`;
    return `${Math.floor(diffMins / 1440)}d`;
  };




  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Truck className="w-8 h-8 text-blue-600" />
              Gestión de Ambulancias
            </h1>
            <p className="text-gray-600 mt-2">
              Control y monitoreo de la flota de ambulancias con GPS en tiempo real
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/ambulancias/mapa')}
            >
              <MapPin className="w-4 h-4 mr-2" />
              Ver Mapa
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => navigate('/ambulancias/nueva')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Nueva Ambulancia
            </Button>
          </div>
        </div>

        {/* Filtros y controles */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar por placa, modelo, marca..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filtroEstado}
                  onChange={(e) => setFiltroEstado(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="todos">Todos los estados</option>
                  <option value="Activo">Activo</option>
                  <option value="En Servicio">En Servicio</option>
                  <option value="Inactivo">Inactivo</option>
                  <option value="En Mantenimiento">En Mantenimiento</option>
                </select>

                <select
                  value={filtroTipo}
                  onChange={(e) => setFiltroTipo(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="todos">Todos los tipos</option>
                  <option value="Básica">Básica</option>
                  <option value="Medicalizada">Medicalizada</option>
                  <option value="Transporte_Asistencial">Transporte Asistencial</option>
                </select>
              </div>
            </div>

            {/* Resumen */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Mostrando {filteredAmbulancias.length} de {ambulancias.length} ambulancias</span>
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Activas: {ambulancias.filter(a => a.estado === 'Activo' || a.estado === 'En Servicio').length}
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    Mantenimiento: {ambulancias.filter(a => a.estado === 'En Mantenimiento').length}
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                    Inactivas: {ambulancias.filter(a => a.estado === 'Inactivo').length}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lista de ambulancias */}
        {loading ? (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">Cargando ambulancias...</p>
            </CardContent>
          </Card>
        ) : filteredAmbulancias.length === 0 ? (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-12 text-center">
              <Truck className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No se encontraron ambulancias</h3>
              <p className="text-gray-600 mb-6">No hay ambulancias que coincidan con los filtros seleccionados.</p>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => navigate('/ambulancias/nueva')}
              >
                <Plus className="w-4 h-4 mr-2" />
                Crear Primera Ambulancia
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredAmbulancias.map((ambulancia) => {
              const statusInfo = getStatusInfo(ambulancia.estado);
              const gpsInfo = getGPSStatusInfo(ambulancia.dispositivo_gps);

              return (
                <Card key={ambulancia.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4">
                        <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Truck className="w-8 h-8 text-blue-600" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-semibold text-gray-900">{ambulancia.placa}</h3>
                            <Badge className={statusInfo.color}>
                              {statusInfo.label}
                            </Badge>
                            <Badge className="bg-gray-100 text-gray-800">
                              {ambulancia.tipo}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">
                                <strong>Vehículo:</strong> {ambulancia.marca} {ambulancia.modelo} ({ambulancia.año})
                              </p>
                              <p className="text-gray-600">
                                <strong>Capacidad:</strong> {ambulancia.capacidad} personas
                              </p>
                              <p className="text-gray-600">
                                <strong>Base:</strong> {ambulancia.ubicacion_base.direccion}
                              </p>
                            </div>

                            <div>
                              {ambulancia.ubicacion_actual ? (
                                <>
                                  <p className="text-gray-600 flex items-center gap-1">
                                    <MapPin className="w-4 h-4 text-green-600" />
                                    <strong>Ubicación:</strong> {ambulancia.ubicacion_actual.direccion}
                                  </p>
                                  <p className="text-gray-600">
                                    <strong>Última actualización:</strong> {formatTimeAgo(ambulancia.ubicacion_actual.timestamp || '')}
                                  </p>
                                </>
                              ) : (
                                <p className="text-gray-500 flex items-center gap-1">
                                  <MapPin className="w-4 h-4" />
                                  Sin ubicación disponible
                                </p>
                              )}
                            </div>

                            <div>
                              {ambulancia.dispositivo_gps && (
                                <>
                                  <p className={`flex items-center gap-1 ${gpsInfo.color}`}>
                                    {gpsInfo.icon}
                                    <strong>GPS:</strong> {gpsInfo.label}
                                  </p>
                                  <p className="text-gray-600 flex items-center gap-1">
                                    <Battery className="w-4 h-4" />
                                    <strong>Batería:</strong> {ambulancia.dispositivo_gps.bateria}%
                                  </p>
                                  <p className="text-gray-600">
                                    <strong>IMEI:</strong> {ambulancia.dispositivo_gps.imei}
                                  </p>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/ambulancias/${ambulancia.id}`)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/ambulancias/${ambulancia.id}/editar`)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(ambulancia.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>

                        <div className="flex gap-2">
                          {ambulancia.ubicacion_actual && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/ambulancias/${ambulancia.id}/ubicacion`)}
                            >
                              <Navigation className="w-4 h-4" />
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/ambulancias/${ambulancia.id}/configuracion`)}
                          >
                            <Settings className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
