import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { camasService } from "../../services/camasService";
import { Cama } from "../../types";

import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faTrash, faSave, faTimes } from "@fortawesome/free-solid-svg-icons";

export const Camas = () => {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<Cama>>({
    numero: "",
    estado: "Libre",
    ubicacion: "",
  });

  // Obtener lista de camas
  const { data: camas, isLoading, isError, refetch } = useQuery({
    queryKey: ["camas"],
    queryFn: () => camasService.getAll(),
  });

  // Mutación para crear cama
  const createMutation = useMutation({
    mutationFn: (data: Omit<Cama, "id" | "created_at">) => {
      return camasService.create(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["camas"] });
      resetForm();
    },
  });

  // Mutación para actualizar cama
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Cama> }) => {
      return camasService.update(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["camas"] });
      resetForm();
    },
  });

  // Mutación para eliminar cama
  const deleteMutation = useMutation({
    mutationFn: (id: string) => {
      return camasService.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["camas"] });
    },
  });

  // Manejar cambios en los campos del formulario
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isEditing && formData.id) {
      updateMutation.mutate({ id: formData.id, data: formData });
    } else {
      createMutation.mutate(formData as Omit<Cama, "id" | "created_at">);
    }
  };

  // Iniciar edición de cama
  const handleEdit = (cama: Cama) => {
    setFormData(cama);
    setIsEditing(true);
  };

  // Eliminar cama
  const handleDelete = (id: string) => {
    if (window.confirm("¿Está seguro de eliminar esta cama?")) {
      deleteMutation.mutate(id);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      numero: "",
      estado: "Libre",
      ubicacion: "",
    });
    setIsEditing(false);
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case "Libre":
        return "text-green-500";
      case "Ocupada":
        return "text-red-500";
      case "Reservada":
        return "text-yellow-500";
      case "En Limpieza":
        return "text-blue-500";
      default:
        return "";
    }
  };

  return (
    <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Camas</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              {isLoading ? (
                <p className="text-white text-center py-4">Cargando camas...</p>
              ) : isError ? (
                <p className="text-red-500 text-center py-4">
                  Error al cargar las camas
                </p>
              ) : camas && camas.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-700">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Número
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Estado
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Ubicación
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Acciones
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {camas.map((cama: Cama) => (
                      <tr key={cama.id} className="hover:bg-gray-800">
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {cama.numero}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <span className={getEstadoColor(cama.estado)}>
                            {cama.estado}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {cama.ubicacion || "-"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(cama)}
                            className="mr-2"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(cama.id)}
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-white text-center py-4">
                  No se encontraron camas.
                </p>
              )}
            </div>
          </div>

          <div>
            <div className="bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">
                {isEditing ? "Editar Cama" : "Nueva Cama"}
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Número *
                  </label>
                  <Input
                    type="text"
                    name="numero"
                    value={formData.numero}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Estado *
                  </label>
                  <Select
                    name="estado"
                    value={formData.estado}
                    onChange={handleChange}
                    required
                  >
                    <option value="Libre">Libre</option>
                    <option value="Ocupada">Ocupada</option>
                    <option value="Reservada">Reservada</option>
                    <option value="En Limpieza">En Limpieza</option>
                  </Select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Ubicación
                  </label>
                  <Input
                    type="text"
                    name="ubicacion"
                    value={formData.ubicacion}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex justify-end">
                  {isEditing && (
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={resetForm}
                      className="mr-2"
                    >
                      <FontAwesomeIcon icon={faTimes} className="mr-2" />
                      Cancelar
                    </Button>
                  )}
                  <Button
                    type="submit"
                    disabled={createMutation.isPending || updateMutation.isPending}
                  >
                    <FontAwesomeIcon icon={faSave} className="mr-2" />
                    {createMutation.isPending || updateMutation.isPending
                      ? "Guardando..."
                      : "Guardar"}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
  );
};

