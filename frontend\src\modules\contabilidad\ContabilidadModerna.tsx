import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  Calculator, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  BarChart3,
  PieChart,
  Target,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  CreditCard,
  Banknote,
  Receipt,
  BookOpen,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  ArrowRightLeft
} from 'lucide-react';

// Interfaces de datos
interface MovimientoContable {
  id: number;
  fecha: string;
  tipo: 'Ingreso' | 'Egreso' | 'Ajuste' | 'Traslado';
  categoria: string;
  subcategoria: string;
  descripcion: string;
  monto: number;
  cuenta_debito: string;
  cuenta_credito: string;
  numero_comprobante: string;
  referencia: string;
  estado: 'Pendiente' | 'Aprobado' | 'Rechazado' | 'Anulado';
  usuario_registro: string;
  fecha_registro: string;
  observaciones: string;
  documentos_adjuntos: string[];
}

interface CuentaContable {
  id: number;
  codigo: string;
  nombre: string;
  tipo: 'Activo' | 'Pasivo' | 'Patrimonio' | 'Ingreso' | 'Gasto';
  nivel: number;
  cuenta_padre: string | null;
  saldo_actual: number;
  saldo_anterior: number;
  estado: 'Activa' | 'Inactiva';
  descripcion: string;
}

interface BalanceGeneral {
  activos: {
    corrientes: number;
    no_corrientes: number;
    total: number;
  };
  pasivos: {
    corrientes: number;
    no_corrientes: number;
    total: number;
  };
  patrimonio: {
    capital: number;
    reservas: number;
    utilidades_retenidas: number;
    total: number;
  };
}

const ContabilidadModerna: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('movimientos');
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroTipo, setFiltroTipo] = useState('todos');
  const [filtroCategoria, setFiltroCategoria] = useState('todos');
  const [filtroEstado, setFiltroEstado] = useState('todos');
  const [filtroFecha, setFiltroFecha] = useState('mes_actual');

  // Datos de ejemplo para desarrollo
  const movimientos: MovimientoContable[] = [
    {
      id: 1,
      fecha: '2024-11-25',
      tipo: 'Ingreso',
      categoria: 'Servicios Médicos',
      subcategoria: 'Consultas',
      descripcion: 'Ingresos por consultas médicas - Noviembre 2024',
      monto: 15000000,
      cuenta_debito: '1105 - Caja',
      cuenta_credito: '4105 - Ingresos por Servicios',
      numero_comprobante: 'ING-2024-001',
      referencia: 'FACT-2024-1125',
      estado: 'Aprobado',
      usuario_registro: 'Contador Principal',
      fecha_registro: '2024-11-25T10:30:00',
      observaciones: 'Ingresos del día 25 de noviembre',
      documentos_adjuntos: ['factura_001.pdf', 'soporte_001.pdf']
    },
    {
      id: 2,
      fecha: '2024-11-25',
      tipo: 'Egreso',
      categoria: 'Gastos Operacionales',
      subcategoria: 'Nómina',
      descripcion: 'Pago de nómina personal médico - Noviembre 2024',
      monto: 8500000,
      cuenta_debito: '5105 - Gastos de Personal',
      cuenta_credito: '1110 - Bancos',
      numero_comprobante: 'EGR-2024-001',
      referencia: 'NOM-2024-11',
      estado: 'Aprobado',
      usuario_registro: 'Contador Principal',
      fecha_registro: '2024-11-25T14:15:00',
      observaciones: 'Pago quincenal de nómina',
      documentos_adjuntos: ['nomina_nov_2024.pdf']
    },
    {
      id: 3,
      fecha: '2024-11-24',
      tipo: 'Egreso',
      categoria: 'Compras',
      subcategoria: 'Medicamentos',
      descripcion: 'Compra de medicamentos e insumos médicos',
      monto: 3200000,
      cuenta_debito: '1435 - Inventario Medicamentos',
      cuenta_credito: '2205 - Proveedores',
      numero_comprobante: 'EGR-2024-002',
      referencia: 'OC-2024-045',
      estado: 'Pendiente',
      usuario_registro: 'Auxiliar Contable',
      fecha_registro: '2024-11-24T16:45:00',
      observaciones: 'Pendiente de aprobación',
      documentos_adjuntos: ['orden_compra_045.pdf']
    },
    {
      id: 4,
      fecha: '2024-11-23',
      tipo: 'Ajuste',
      categoria: 'Ajustes Contables',
      subcategoria: 'Depreciación',
      descripcion: 'Ajuste por depreciación de equipos médicos',
      monto: 1200000,
      cuenta_debito: '5305 - Depreciación',
      cuenta_credito: '1592 - Depreciación Acumulada',
      numero_comprobante: 'AJU-2024-001',
      referencia: 'DEP-NOV-2024',
      estado: 'Aprobado',
      usuario_registro: 'Contador Principal',
      fecha_registro: '2024-11-23T17:30:00',
      observaciones: 'Ajuste mensual de depreciación',
      documentos_adjuntos: ['calculo_depreciacion.xlsx']
    }
  ];

  const cuentas: CuentaContable[] = [
    {
      id: 1,
      codigo: '1105',
      nombre: 'Caja',
      tipo: 'Activo',
      nivel: 1,
      cuenta_padre: null,
      saldo_actual: 5000000,
      saldo_anterior: 3500000,
      estado: 'Activa',
      descripcion: 'Efectivo en caja'
    },
    {
      id: 2,
      codigo: '1110',
      nombre: 'Bancos',
      tipo: 'Activo',
      nivel: 1,
      cuenta_padre: null,
      saldo_actual: 45000000,
      saldo_anterior: 38000000,
      estado: 'Activa',
      descripcion: 'Cuentas bancarias'
    },
    {
      id: 3,
      codigo: '1435',
      nombre: 'Inventario Medicamentos',
      tipo: 'Activo',
      nivel: 1,
      cuenta_padre: null,
      saldo_actual: 12000000,
      saldo_anterior: 10500000,
      estado: 'Activa',
      descripcion: 'Inventario de medicamentos e insumos'
    },
    {
      id: 4,
      codigo: '4105',
      nombre: 'Ingresos por Servicios',
      tipo: 'Ingreso',
      nivel: 1,
      cuenta_padre: null,
      saldo_actual: 85000000,
      saldo_anterior: 78000000,
      estado: 'Activa',
      descripcion: 'Ingresos por servicios médicos'
    }
  ];

  const categorias = [
    'Servicios Médicos',
    'Gastos Operacionales',
    'Compras',
    'Ajustes Contables',
    'Inversiones',
    'Financieros',
    'Administrativos'
  ];

  // Filtrar movimientos
  const filteredMovimientos = movimientos.filter(movimiento => {
    const matchesSearch = 
      movimiento.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movimiento.numero_comprobante.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movimiento.referencia.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movimiento.cuenta_debito.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movimiento.cuenta_credito.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTipo = filtroTipo === 'todos' || movimiento.tipo === filtroTipo;
    const matchesCategoria = filtroCategoria === 'todos' || movimiento.categoria === filtroCategoria;
    const matchesEstado = filtroEstado === 'todos' || movimiento.estado === filtroEstado;
    
    return matchesSearch && matchesTipo && matchesCategoria && matchesEstado;
  });

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Pendiente':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'Pendiente', icon: <Clock className="w-3 h-3" /> };
      case 'Aprobado':
        return { color: 'bg-green-100 text-green-800', label: 'Aprobado', icon: <CheckCircle className="w-3 h-3" /> };
      case 'Rechazado':
        return { color: 'bg-red-100 text-red-800', label: 'Rechazado', icon: <AlertCircle className="w-3 h-3" /> };
      case 'Anulado':
        return { color: 'bg-gray-100 text-gray-800', label: 'Anulado', icon: <AlertCircle className="w-3 h-3" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado, icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  // Obtener icono de tipo de movimiento
  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'Ingreso':
        return <ArrowUpRight className="w-4 h-4 text-green-600" />;
      case 'Egreso':
        return <ArrowDownRight className="w-4 h-4 text-red-600" />;
      case 'Ajuste':
        return <RefreshCw className="w-4 h-4 text-blue-600" />;
      case 'Traslado':
        return <ArrowRightLeft className="w-4 h-4 text-purple-600" />;
      default:
        return <Calculator className="w-4 h-4 text-gray-600" />;
    }
  };

  // Formatear moneda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Calcular estadísticas
  const stats = {
    total_movimientos: filteredMovimientos.length,
    total_ingresos: filteredMovimientos.filter(m => m.tipo === 'Ingreso').reduce((sum, m) => sum + m.monto, 0),
    total_egresos: filteredMovimientos.filter(m => m.tipo === 'Egreso').reduce((sum, m) => sum + m.monto, 0),
    total_ajustes: filteredMovimientos.filter(m => m.tipo === 'Ajuste').reduce((sum, m) => sum + m.monto, 0),
    pendientes: filteredMovimientos.filter(m => m.estado === 'Pendiente').length,
    aprobados: filteredMovimientos.filter(m => m.estado === 'Aprobado').length
  };

  stats.balance = stats.total_ingresos - stats.total_egresos;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Calculator className="w-8 h-8 text-green-600" />
              Sistema Contable
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de la contabilidad hospitalaria
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={() => setActiveTab('reportes')}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Reportes
            </Button>
            <Button 
              className="bg-green-600 hover:bg-green-700"
              onClick={() => navigate('/contabilidad/nuevo-movimiento')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Movimiento
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Ingresos</p>
                  <p className="text-2xl font-bold text-green-700">
                    {formatCurrency(stats.total_ingresos)}
                  </p>
                  <div className="flex items-center mt-2">
                    <ArrowUpRight className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">Ingresos del período</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Egresos</p>
                  <p className="text-2xl font-bold text-red-700">
                    {formatCurrency(stats.total_egresos)}
                  </p>
                  <div className="flex items-center mt-2">
                    <ArrowDownRight className="w-4 h-4 text-red-600 mr-1" />
                    <span className="text-sm text-red-600">Gastos del período</span>
                  </div>
                </div>
                <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                  <TrendingDown className="w-8 h-8 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Balance</p>
                  <p className={`text-2xl font-bold ${stats.balance >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                    {formatCurrency(stats.balance)}
                  </p>
                  <div className="flex items-center mt-2">
                    <DollarSign className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm text-blue-600">
                      {stats.balance >= 0 ? 'Superávit' : 'Déficit'}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <Calculator className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pendientes</p>
                  <p className="text-3xl font-bold text-orange-700">{stats.pendientes}</p>
                  <div className="flex items-center mt-2">
                    <Clock className="w-4 h-4 text-orange-600 mr-1" />
                    <span className="text-sm text-orange-600">Por aprobar</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg border border-orange-100">
                  <Clock className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeTab === 'movimientos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('movimientos')}
                className="flex items-center gap-2"
              >
                <Receipt className="w-4 h-4" />
                Movimientos
              </Button>
              <Button
                variant={activeTab === 'cuentas' ? 'default' : 'outline'}
                onClick={() => setActiveTab('cuentas')}
                className="flex items-center gap-2"
              >
                <BookOpen className="w-4 h-4" />
                Plan de Cuentas
              </Button>
              <Button
                variant={activeTab === 'balance' ? 'default' : 'outline'}
                onClick={() => setActiveTab('balance')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="w-4 h-4" />
                Balance General
              </Button>
              <Button
                variant={activeTab === 'estados' ? 'default' : 'outline'}
                onClick={() => setActiveTab('estados')}
                className="flex items-center gap-2"
              >
                <FileText className="w-4 h-4" />
                Estados Financieros
              </Button>
              <Button
                variant={activeTab === 'reportes' ? 'default' : 'outline'}
                onClick={() => setActiveTab('reportes')}
                className="flex items-center gap-2"
              >
                <PieChart className="w-4 h-4" />
                Reportes
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'movimientos' && (
          <>
            {/* Filtros y controles */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por descripción, comprobante, referencia o cuenta..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <select
                      value={filtroTipo}
                      onChange={(e) => setFiltroTipo(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="todos">Todos los tipos</option>
                      <option value="Ingreso">Ingresos</option>
                      <option value="Egreso">Egresos</option>
                      <option value="Ajuste">Ajustes</option>
                      <option value="Traslado">Traslados</option>
                    </select>

                    <select
                      value={filtroCategoria}
                      onChange={(e) => setFiltroCategoria(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="todos">Todas las categorías</option>
                      {categorias.map(categoria => (
                        <option key={categoria} value={categoria}>{categoria}</option>
                      ))}
                    </select>

                    <select
                      value={filtroEstado}
                      onChange={(e) => setFiltroEstado(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="todos">Todos los estados</option>
                      <option value="Pendiente">Pendiente</option>
                      <option value="Aprobado">Aprobado</option>
                      <option value="Rechazado">Rechazado</option>
                      <option value="Anulado">Anulado</option>
                    </select>
                  </div>
                </div>

                {/* Resumen de filtros */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Mostrando {filteredMovimientos.length} de {movimientos.length} movimientos</span>
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Aprobados: {stats.aprobados}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        Pendientes: {stats.pendientes}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lista de movimientos */}
            <div className="space-y-4">
              {filteredMovimientos.map((movimiento) => {
                const statusInfo = getStatusInfo(movimiento.estado);
                const tipoIcon = getTipoIcon(movimiento.tipo);

                return (
                  <Card key={movimiento.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
                            {tipoIcon}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{movimiento.numero_comprobante}</h3>
                              <Badge className={statusInfo.color}>
                                <div className="flex items-center gap-1">
                                  {statusInfo.icon}
                                  {statusInfo.label}
                                </div>
                              </Badge>
                              <Badge className="bg-gray-100 text-gray-800">
                                {movimiento.tipo}
                              </Badge>
                            </div>

                            <p className="text-gray-600 mb-3">{movimiento.descripcion}</p>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">
                                  <strong>Fecha:</strong> {new Date(movimiento.fecha).toLocaleDateString('es-CO')}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Monto:</strong> {formatCurrency(movimiento.monto)}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Referencia:</strong> {movimiento.referencia}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Categoría:</strong> {movimiento.categoria}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Subcategoría:</strong> {movimiento.subcategoria}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Usuario:</strong> {movimiento.usuario_registro}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Cuenta Débito:</strong> {movimiento.cuenta_debito}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Cuenta Crédito:</strong> {movimiento.cuenta_credito}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Documentos:</strong> {movimiento.documentos_adjuntos.length} archivo(s)
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/contabilidad/movimientos/${movimiento.id}`)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/contabilidad/movimientos/${movimiento.id}/editar`)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </>
        )}

        {/* Pestaña de Plan de Cuentas */}
        {activeTab === 'cuentas' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Plan de Cuentas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Plan de Cuentas Contables</h3>
                <p className="text-gray-600 mb-6">Gestione el catálogo de cuentas contables del hospital.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Nueva Cuenta
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Balance General */}
        {activeTab === 'balance' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Balance General</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Balance General</h3>
                <p className="text-gray-600 mb-6">Visualice el estado financiero actual del hospital.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Generar Balance
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Estados Financieros */}
        {activeTab === 'estados' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Estados Financieros</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Estados Financieros</h3>
                <p className="text-gray-600 mb-6">Genere estados de resultados, flujo de efectivo y otros reportes.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <FileText className="w-4 h-4 mr-2" />
                  Generar Estados
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Reportes */}
        {activeTab === 'reportes' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Reportes Contables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Reportes y Análisis</h3>
                <p className="text-gray-600 mb-6">Genere reportes personalizados y análisis financieros.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <PieChart className="w-4 h-4 mr-2" />
                  Generar Reporte
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ContabilidadModerna;
