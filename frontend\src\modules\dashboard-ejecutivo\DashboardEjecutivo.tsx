import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  Activity,
  Calendar,
  Building,
  Heart,
  Stethoscope,
  UserCheck,
  Clock,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  LineChart,
  Target,
  Award,
  Zap,
  Shield,
  Globe,
  Briefcase,
  FileText,
  Download,
  RefreshCw,
  Filter,
  Eye,
  Settings
} from 'lucide-react';

// Interfaces para el Dashboard Ejecutivo
interface MetricaEjecutiva {
  id: string;
  titulo: string;
  valor: number;
  valorAnterior: number;
  unidad: string;
  formato: 'numero' | 'moneda' | 'porcentaje';
  tendencia: 'subida' | 'bajada' | 'estable';
  variacion: number;
  descripcion: string;
  meta?: number;
  categoria: 'financiero' | 'operacional' | 'calidad' | 'recursos';
}

interface IndicadorKPI {
  id: string;
  nombre: string;
  valor_actual: number;
  meta: number;
  unidad: string;
  estado: 'excelente' | 'bueno' | 'regular' | 'critico';
  tendencia_mes: number;
  descripcion: string;
  area_responsable: string;
}

interface ResumenEjecutivo {
  ingresos_mes: number;
  ingresos_mes_anterior: number;
  pacientes_atendidos: number;
  pacientes_mes_anterior: number;
  ocupacion_promedio: number;
  satisfaccion_pacientes: number;
  tiempo_promedio_atencion: number;
  margen_operacional: number;
  rotacion_personal: number;
  indicadores_calidad: number;
}

export const DashboardEjecutivo: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('resumen');
  
  // Estados de datos
  const [resumenEjecutivo, setResumenEjecutivo] = useState<ResumenEjecutivo | null>(null);
  const [metricas, setMetricas] = useState<MetricaEjecutiva[]>([]);
  const [kpis, setKpis] = useState<IndicadorKPI[]>([]);
  const [cargando, setCargando] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Estados de UI
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState('mes_actual');
  const [mostrarFiltros, setMostrarFiltros] = useState(false);

  // Datos mock para demostración
  const resumenMock: ResumenEjecutivo = {
    ingresos_mes: 2850000000, // $2.850 millones
    ingresos_mes_anterior: 2650000000,
    pacientes_atendidos: 8450,
    pacientes_mes_anterior: 7890,
    ocupacion_promedio: 87.5,
    satisfaccion_pacientes: 94.2,
    tiempo_promedio_atencion: 28.5,
    margen_operacional: 18.7,
    rotacion_personal: 8.3,
    indicadores_calidad: 96.8
  };

  const metricasMock: MetricaEjecutiva[] = [
    {
      id: 'ingresos',
      titulo: 'Ingresos Totales',
      valor: 2850000000,
      valorAnterior: 2650000000,
      unidad: 'COP',
      formato: 'moneda',
      tendencia: 'subida',
      variacion: 7.5,
      descripcion: 'Ingresos totales del mes actual vs mes anterior',
      meta: 3000000000,
      categoria: 'financiero'
    },
    {
      id: 'pacientes',
      titulo: 'Pacientes Atendidos',
      valor: 8450,
      valorAnterior: 7890,
      unidad: 'pacientes',
      formato: 'numero',
      tendencia: 'subida',
      variacion: 7.1,
      descripcion: 'Total de pacientes atendidos en el mes',
      meta: 9000,
      categoria: 'operacional'
    },
    {
      id: 'ocupacion',
      titulo: 'Ocupación Hospitalaria',
      valor: 87.5,
      valorAnterior: 82.3,
      unidad: '%',
      formato: 'porcentaje',
      tendencia: 'subida',
      variacion: 6.3,
      descripcion: 'Porcentaje promedio de ocupación de camas',
      meta: 85,
      categoria: 'operacional'
    },
    {
      id: 'satisfaccion',
      titulo: 'Satisfacción del Paciente',
      valor: 94.2,
      valorAnterior: 93.8,
      unidad: '%',
      formato: 'porcentaje',
      tendencia: 'subida',
      variacion: 0.4,
      descripcion: 'Índice de satisfacción basado en encuestas',
      meta: 95,
      categoria: 'calidad'
    }
  ];

  const kpisMock: IndicadorKPI[] = [
    {
      id: 'tiempo_atencion',
      nombre: 'Tiempo Promedio de Atención',
      valor_actual: 28.5,
      meta: 30,
      unidad: 'minutos',
      estado: 'bueno',
      tendencia_mes: -5.2,
      descripcion: 'Tiempo promedio desde llegada hasta atención médica',
      area_responsable: 'Operaciones'
    },
    {
      id: 'margen_operacional',
      nombre: 'Margen Operacional',
      valor_actual: 18.7,
      meta: 20,
      unidad: '%',
      estado: 'regular',
      tendencia_mes: 2.1,
      descripcion: 'Margen operacional del hospital',
      area_responsable: 'Finanzas'
    },
    {
      id: 'rotacion_personal',
      nombre: 'Rotación de Personal',
      valor_actual: 8.3,
      meta: 10,
      unidad: '%',
      estado: 'bueno',
      tendencia_mes: -1.2,
      descripcion: 'Tasa de rotación mensual del personal',
      area_responsable: 'RRHH'
    },
    {
      id: 'indicadores_calidad',
      nombre: 'Indicadores de Calidad',
      valor_actual: 96.8,
      meta: 95,
      unidad: '%',
      estado: 'excelente',
      tendencia_mes: 1.5,
      descripcion: 'Promedio de indicadores de calidad asistencial',
      area_responsable: 'Calidad'
    }
  ];

  // Cargar datos
  useEffect(() => {
    const cargarDatos = async () => {
      setCargando(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setResumenEjecutivo(resumenMock);
        setMetricas(metricasMock);
        setKpis(kpisMock);
      } catch (error) {
        setError('Error al cargar datos del dashboard ejecutivo');
      } finally {
        setCargando(false);
      }
    };

    cargarDatos();
  }, [periodoSeleccionado]);

  const formatearMoneda = (valor: number) => {
    if (valor >= 1000000000) {
      return `$${(valor / 1000000000).toFixed(1)}B`;
    } else if (valor >= 1000000) {
      return `$${(valor / 1000000).toFixed(1)}M`;
    } else {
      return new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0
      }).format(valor);
    }
  };

  const formatearNumero = (valor: number) => {
    return new Intl.NumberFormat('es-CO').format(valor);
  };

  const formatearValor = (metrica: MetricaEjecutiva) => {
    switch (metrica.formato) {
      case 'moneda':
        return formatearMoneda(metrica.valor);
      case 'porcentaje':
        return `${metrica.valor.toFixed(1)}%`;
      case 'numero':
        return formatearNumero(metrica.valor);
      default:
        return metrica.valor.toString();
    }
  };

  const obtenerColorTendencia = (tendencia: string) => {
    switch (tendencia) {
      case 'subida':
        return 'text-green-600';
      case 'bajada':
        return 'text-red-600';
      case 'estable':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const obtenerIconoTendencia = (tendencia: string) => {
    switch (tendencia) {
      case 'subida':
        return <TrendingUp className="w-4 h-4" />;
      case 'bajada':
        return <TrendingDown className="w-4 h-4" />;
      case 'estable':
        return <Activity className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const obtenerColorKPI = (estado: string) => {
    switch (estado) {
      case 'excelente':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'bueno':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'regular':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critico':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calcularProgreso = (valor: number, meta: number) => {
    return Math.min((valor / meta) * 100, 100);
  };

  if (cargando) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando dashboard ejecutivo...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-red-50 border border-red-200">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-900 mb-2">Error al cargar datos</h3>
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Reintentar
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <BarChart3 className="w-8 h-8 text-blue-600" />
              Dashboard Ejecutivo
            </h1>
            <p className="text-gray-600 mt-2">
              Métricas clave y KPIs para la toma de decisiones estratégicas
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select 
              value={periodoSeleccionado}
              onChange={(e) => setPeriodoSeleccionado(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="mes_actual">Mes Actual</option>
              <option value="trimestre">Trimestre</option>
              <option value="semestre">Semestre</option>
              <option value="año">Año</option>
            </select>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeTab === 'resumen' ? 'default' : 'outline'}
                onClick={() => setActiveTab('resumen')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="w-4 h-4" />
                Resumen Ejecutivo
              </Button>
              <Button
                variant={activeTab === 'financiero' ? 'default' : 'outline'}
                onClick={() => setActiveTab('financiero')}
                className="flex items-center gap-2"
              >
                <DollarSign className="w-4 h-4" />
                Financiero
              </Button>
              <Button
                variant={activeTab === 'operacional' ? 'default' : 'outline'}
                onClick={() => setActiveTab('operacional')}
                className="flex items-center gap-2"
              >
                <Activity className="w-4 h-4" />
                Operacional
              </Button>
              <Button
                variant={activeTab === 'calidad' ? 'default' : 'outline'}
                onClick={() => setActiveTab('calidad')}
                className="flex items-center gap-2"
              >
                <Award className="w-4 h-4" />
                Calidad
              </Button>
              <Button
                variant={activeTab === 'recursos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('recursos')}
                className="flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Recursos Humanos
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'resumen' && (
          <>
            {/* Métricas principales */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {metricas.map((metrica) => (
                <Card key={metrica.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <div className={`p-2 rounded-lg ${
                          metrica.categoria === 'financiero' ? 'bg-green-100' :
                          metrica.categoria === 'operacional' ? 'bg-blue-100' :
                          metrica.categoria === 'calidad' ? 'bg-purple-100' :
                          'bg-orange-100'
                        }`}>
                          {metrica.categoria === 'financiero' && <DollarSign className="w-5 h-5 text-green-600" />}
                          {metrica.categoria === 'operacional' && <Activity className="w-5 h-5 text-blue-600" />}
                          {metrica.categoria === 'calidad' && <Award className="w-5 h-5 text-purple-600" />}
                          {metrica.categoria === 'recursos' && <Users className="w-5 h-5 text-orange-600" />}
                        </div>
                      </div>
                      <div className={`flex items-center gap-1 ${obtenerColorTendencia(metrica.tendencia)}`}>
                        {obtenerIconoTendencia(metrica.tendencia)}
                        <span className="text-sm font-medium">
                          {metrica.variacion > 0 ? '+' : ''}{metrica.variacion.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-600 mb-1">{metrica.titulo}</h3>
                      <p className="text-2xl font-bold text-gray-900 mb-2">
                        {formatearValor(metrica)}
                      </p>
                      <p className="text-xs text-gray-500">{metrica.descripcion}</p>

                      {metrica.meta && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>Progreso hacia meta</span>
                            <span>{((metrica.valor / metrica.meta) * 100).toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min((metrica.valor / metrica.meta) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* KPIs principales */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  Indicadores Clave de Rendimiento (KPIs)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {kpis.map((kpi) => (
                    <div key={kpi.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{kpi.nombre}</h4>
                        <Badge className={obtenerColorKPI(kpi.estado)}>
                          {kpi.estado}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between mb-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {kpi.valor_actual.toFixed(1)} {kpi.unidad}
                        </span>
                        <span className="text-sm text-gray-600">
                          Meta: {kpi.meta} {kpi.unidad}
                        </span>
                      </div>

                      <div className="mb-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              kpi.estado === 'excelente' ? 'bg-green-500' :
                              kpi.estado === 'bueno' ? 'bg-blue-500' :
                              kpi.estado === 'regular' ? 'bg-yellow-500' :
                              'bg-red-500'
                            }`}
                            style={{ width: `${calcularProgreso(kpi.valor_actual, kpi.meta)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">{kpi.descripcion}</span>
                        <div className={`flex items-center gap-1 ${
                          kpi.tendencia_mes > 0 ? 'text-green-600' :
                          kpi.tendencia_mes < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {kpi.tendencia_mes > 0 ? <TrendingUp className="w-3 h-3" /> :
                           kpi.tendencia_mes < 0 ? <TrendingDown className="w-3 h-3" /> :
                           <Activity className="w-3 h-3" />}
                          <span>{kpi.tendencia_mes > 0 ? '+' : ''}{kpi.tendencia_mes.toFixed(1)}%</span>
                        </div>
                      </div>

                      <div className="mt-2 text-xs text-gray-500">
                        Responsable: {kpi.area_responsable}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Otras pestañas */}
        {activeTab === 'financiero' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <DollarSign className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard Financiero</h3>
              <p className="text-gray-600 mb-4">
                Análisis detallado de ingresos, gastos, rentabilidad y proyecciones financieras
              </p>
              <Button>
                <BarChart3 className="w-4 h-4 mr-2" />
                Ver Análisis Completo
              </Button>
            </CardContent>
          </Card>
        )}

        {activeTab === 'operacional' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <Activity className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard Operacional</h3>
              <p className="text-gray-600 mb-4">
                Métricas de eficiencia operativa, ocupación, tiempos de atención y productividad
              </p>
              <Button>
                <PieChart className="w-4 h-4 mr-2" />
                Ver Métricas Operacionales
              </Button>
            </CardContent>
          </Card>
        )}

        {activeTab === 'calidad' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <Award className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard de Calidad</h3>
              <p className="text-gray-600 mb-4">
                Indicadores de calidad asistencial, satisfacción del paciente y acreditaciones
              </p>
              <Button>
                <CheckCircle className="w-4 h-4 mr-2" />
                Ver Indicadores de Calidad
              </Button>
            </CardContent>
          </Card>
        )}

        {activeTab === 'recursos' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <Users className="w-12 h-12 text-orange-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard de RRHH</h3>
              <p className="text-gray-600 mb-4">
                Gestión del talento humano, rotación, capacitación y bienestar del personal
              </p>
              <Button>
                <UserCheck className="w-4 h-4 mr-2" />
                Ver Métricas de Personal
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DashboardEjecutivo;
