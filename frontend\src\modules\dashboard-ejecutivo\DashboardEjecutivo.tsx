import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  Activity,
  Calendar,
  Building,
  Heart,
  Stethoscope,
  UserCheck,
  Clock,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  LineChart,
  Target,
  Award,
  Zap,
  Shield,
  Globe,
  Briefcase,
  FileText,
  Download,
  RefreshCw,
  Filter,
  Eye,
  Settings,
  XCircle
} from 'lucide-react';

// Interfaces para el Dashboard Ejecutivo
interface MetricaEjecutiva {
  id: string;
  titulo: string;
  valor: number;
  valorAnterior: number;
  unidad: string;
  formato: 'numero' | 'moneda' | 'porcentaje';
  tendencia: 'subida' | 'bajada' | 'estable';
  variacion: number;
  descripcion: string;
  meta?: number;
  categoria: 'financiero' | 'operacional' | 'calidad' | 'recursos';
}

interface IndicadorKPI {
  id: string;
  nombre: string;
  valor_actual: number;
  meta: number;
  unidad: string;
  estado: 'excelente' | 'bueno' | 'regular' | 'critico';
  tendencia_mes: number;
  descripcion: string;
  area_responsable: string;
}

interface ResumenEjecutivo {
  ingresos_mes: number;
  ingresos_mes_anterior: number;
  pacientes_atendidos: number;
  pacientes_mes_anterior: number;
  ocupacion_promedio: number;
  satisfaccion_pacientes: number;
  tiempo_promedio_atencion: number;
  margen_operacional: number;
  rotacion_personal: number;
  indicadores_calidad: number;
}

export const DashboardEjecutivo: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('resumen');
  
  // Estados de datos
  const [resumenEjecutivo, setResumenEjecutivo] = useState<ResumenEjecutivo | null>(null);
  const [metricas, setMetricas] = useState<MetricaEjecutiva[]>([]);
  const [kpis, setKpis] = useState<IndicadorKPI[]>([]);
  const [cargando, setCargando] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Estados de UI
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState('mes_actual');
  const [mostrarFiltros, setMostrarFiltros] = useState(false);
  const [modalAbierto, setModalAbierto] = useState<string | null>(null);
  const [datosModal, setDatosModal] = useState<any>(null);

  // Datos mock para demostración
  const resumenMock: ResumenEjecutivo = {
    ingresos_mes: 2850000000, // $2.850 millones
    ingresos_mes_anterior: 2650000000,
    pacientes_atendidos: 8450,
    pacientes_mes_anterior: 7890,
    ocupacion_promedio: 87.5,
    satisfaccion_pacientes: 94.2,
    tiempo_promedio_atencion: 28.5,
    margen_operacional: 18.7,
    rotacion_personal: 8.3,
    indicadores_calidad: 96.8
  };

  const metricasMock: MetricaEjecutiva[] = [
    {
      id: 'ingresos',
      titulo: 'Ingresos Totales',
      valor: 2850000000,
      valorAnterior: 2650000000,
      unidad: 'COP',
      formato: 'moneda',
      tendencia: 'subida',
      variacion: 7.5,
      descripcion: 'Ingresos totales del mes actual vs mes anterior',
      meta: 3000000000,
      categoria: 'financiero'
    },
    {
      id: 'pacientes',
      titulo: 'Pacientes Atendidos',
      valor: 8450,
      valorAnterior: 7890,
      unidad: 'pacientes',
      formato: 'numero',
      tendencia: 'subida',
      variacion: 7.1,
      descripcion: 'Total de pacientes atendidos en el mes',
      meta: 9000,
      categoria: 'operacional'
    },
    {
      id: 'ocupacion',
      titulo: 'Ocupación Hospitalaria',
      valor: 87.5,
      valorAnterior: 82.3,
      unidad: '%',
      formato: 'porcentaje',
      tendencia: 'subida',
      variacion: 6.3,
      descripcion: 'Porcentaje promedio de ocupación de camas',
      meta: 85,
      categoria: 'operacional'
    },
    {
      id: 'satisfaccion',
      titulo: 'Satisfacción del Paciente',
      valor: 94.2,
      valorAnterior: 93.8,
      unidad: '%',
      formato: 'porcentaje',
      tendencia: 'subida',
      variacion: 0.4,
      descripcion: 'Índice de satisfacción basado en encuestas',
      meta: 95,
      categoria: 'calidad'
    }
  ];

  const kpisMock: IndicadorKPI[] = [
    {
      id: 'tiempo_atencion',
      nombre: 'Tiempo Promedio de Atención',
      valor_actual: 28.5,
      meta: 30,
      unidad: 'minutos',
      estado: 'bueno',
      tendencia_mes: -5.2,
      descripcion: 'Tiempo promedio desde llegada hasta atención médica',
      area_responsable: 'Operaciones'
    },
    {
      id: 'margen_operacional',
      nombre: 'Margen Operacional',
      valor_actual: 18.7,
      meta: 20,
      unidad: '%',
      estado: 'regular',
      tendencia_mes: 2.1,
      descripcion: 'Margen operacional del hospital',
      area_responsable: 'Finanzas'
    },
    {
      id: 'rotacion_personal',
      nombre: 'Rotación de Personal',
      valor_actual: 8.3,
      meta: 10,
      unidad: '%',
      estado: 'bueno',
      tendencia_mes: -1.2,
      descripcion: 'Tasa de rotación mensual del personal',
      area_responsable: 'RRHH'
    },
    {
      id: 'indicadores_calidad',
      nombre: 'Indicadores de Calidad',
      valor_actual: 96.8,
      meta: 95,
      unidad: '%',
      estado: 'excelente',
      tendencia_mes: 1.5,
      descripcion: 'Promedio de indicadores de calidad asistencial',
      area_responsable: 'Calidad'
    }
  ];

  // Cargar datos
  useEffect(() => {
    const cargarDatos = async () => {
      setCargando(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setResumenEjecutivo(resumenMock);
        setMetricas(metricasMock);
        setKpis(kpisMock);
      } catch (error) {
        setError('Error al cargar datos del dashboard ejecutivo');
      } finally {
        setCargando(false);
      }
    };

    cargarDatos();
  }, [periodoSeleccionado]);

  const formatearMoneda = (valor: number) => {
    if (valor >= 1000000000) {
      return `$${(valor / 1000000000).toFixed(1)}B`;
    } else if (valor >= 1000000) {
      return `$${(valor / 1000000).toFixed(1)}M`;
    } else {
      return new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0
      }).format(valor);
    }
  };

  const formatearNumero = (valor: number) => {
    return new Intl.NumberFormat('es-CO').format(valor);
  };

  const formatearValor = (metrica: MetricaEjecutiva) => {
    switch (metrica.formato) {
      case 'moneda':
        return formatearMoneda(metrica.valor);
      case 'porcentaje':
        return `${metrica.valor.toFixed(1)}%`;
      case 'numero':
        return formatearNumero(metrica.valor);
      default:
        return metrica.valor.toString();
    }
  };

  const obtenerColorTendencia = (tendencia: string) => {
    switch (tendencia) {
      case 'subida':
        return 'text-green-600';
      case 'bajada':
        return 'text-red-600';
      case 'estable':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const obtenerIconoTendencia = (tendencia: string) => {
    switch (tendencia) {
      case 'subida':
        return <TrendingUp className="w-4 h-4" />;
      case 'bajada':
        return <TrendingDown className="w-4 h-4" />;
      case 'estable':
        return <Activity className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const obtenerColorKPI = (estado: string) => {
    switch (estado) {
      case 'excelente':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'bueno':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'regular':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critico':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calcularProgreso = (valor: number, meta: number) => {
    return Math.min((valor / meta) * 100, 100);
  };

  // Funciones para manejar modales y acciones
  const handleExportar = () => {
    setModalAbierto('exportar');
    setDatosModal({
      periodo: periodoSeleccionado,
      fecha_generacion: new Date().toISOString(),
      metricas_incluidas: metricas.length,
      kpis_incluidos: kpis.length
    });
  };

  const handleActualizar = () => {
    setCargando(true);
    setTimeout(() => {
      setCargando(false);
      alert('✅ Dashboard actualizado exitosamente\n\nDatos actualizados con la información más reciente del sistema.');
    }, 2000);
  };

  const handleVerAnalisisCompleto = (tipo: string) => {
    setModalAbierto('analisis');
    setDatosModal({
      tipo,
      datos: tipo === 'financiero' ? {
        ingresos_detalle: [
          { concepto: 'Consultas Externas', valor: 850000000, porcentaje: 29.8 },
          { concepto: 'Hospitalizaciones', valor: 1200000000, porcentaje: 42.1 },
          { concepto: 'Cirugías', valor: 450000000, porcentaje: 15.8 },
          { concepto: 'Laboratorio', valor: 200000000, porcentaje: 7.0 },
          { concepto: 'Imágenes Diagnósticas', valor: 150000000, porcentaje: 5.3 }
        ],
        gastos_detalle: [
          { concepto: 'Personal Médico', valor: 1100000000, porcentaje: 45.2 },
          { concepto: 'Medicamentos', valor: 650000000, porcentaje: 26.7 },
          { concepto: 'Equipos y Mantenimiento', valor: 300000000, porcentaje: 12.3 },
          { concepto: 'Servicios Generales', valor: 250000000, porcentaje: 10.3 },
          { concepto: 'Otros Gastos', valor: 135000000, porcentaje: 5.5 }
        ]
      } : tipo === 'operacional' ? {
        servicios_productividad: [
          { servicio: 'Urgencias', pacientes: 2450, tiempo_promedio: 25, satisfaccion: 92.1 },
          { servicio: 'Consulta Externa', pacientes: 3200, tiempo_promedio: 18, satisfaccion: 95.8 },
          { servicio: 'Hospitalización', pacientes: 1800, estancia_promedio: 4.2, satisfaccion: 94.5 },
          { servicio: 'Cirugías', procedimientos: 450, tiempo_promedio: 85, satisfaccion: 96.2 },
          { servicio: 'Laboratorio', examenes: 8900, tiempo_entrega: 2.5, satisfaccion: 93.7 }
        ]
      } : {}
    });
  };

  const handleVerMetricas = (categoria: string) => {
    setModalAbierto('metricas');
    setDatosModal({
      categoria,
      metricas: metricas.filter(m => m.categoria === categoria),
      periodo: periodoSeleccionado
    });
  };

  const handleCerrarModal = () => {
    setModalAbierto(null);
    setDatosModal(null);
  };

  const handleDescargarReporte = (formato: string) => {
    alert(`📥 Descargando reporte en formato ${formato.toUpperCase()}\n\nEl archivo se descargará automáticamente en unos segundos.\n\nContenido:\n• Métricas ejecutivas\n• KPIs principales\n• Análisis de tendencias\n• Período: ${periodoSeleccionado}`);
    handleCerrarModal();
  };

  if (cargando) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando dashboard ejecutivo...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-red-50 border border-red-200">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-900 mb-2">Error al cargar datos</h3>
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Reintentar
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <BarChart3 className="w-8 h-8 text-blue-600" />
              Dashboard Ejecutivo
            </h1>
            <p className="text-gray-600 mt-2">
              Métricas clave y KPIs para la toma de decisiones estratégicas
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select 
              value={periodoSeleccionado}
              onChange={(e) => setPeriodoSeleccionado(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="mes_actual">Mes Actual</option>
              <option value="trimestre">Trimestre</option>
              <option value="semestre">Semestre</option>
              <option value="año">Año</option>
            </select>
            <Button variant="outline" onClick={handleExportar}>
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline" onClick={handleActualizar}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeTab === 'resumen' ? 'default' : 'outline'}
                onClick={() => setActiveTab('resumen')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="w-4 h-4" />
                Resumen Ejecutivo
              </Button>
              <Button
                variant={activeTab === 'financiero' ? 'default' : 'outline'}
                onClick={() => setActiveTab('financiero')}
                className="flex items-center gap-2"
              >
                <DollarSign className="w-4 h-4" />
                Financiero
              </Button>
              <Button
                variant={activeTab === 'operacional' ? 'default' : 'outline'}
                onClick={() => setActiveTab('operacional')}
                className="flex items-center gap-2"
              >
                <Activity className="w-4 h-4" />
                Operacional
              </Button>
              <Button
                variant={activeTab === 'calidad' ? 'default' : 'outline'}
                onClick={() => setActiveTab('calidad')}
                className="flex items-center gap-2"
              >
                <Award className="w-4 h-4" />
                Calidad
              </Button>
              <Button
                variant={activeTab === 'recursos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('recursos')}
                className="flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Recursos Humanos
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'resumen' && (
          <>
            {/* Métricas principales */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {metricas.map((metrica) => (
                <Card key={metrica.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <div className={`p-2 rounded-lg ${
                          metrica.categoria === 'financiero' ? 'bg-green-100' :
                          metrica.categoria === 'operacional' ? 'bg-blue-100' :
                          metrica.categoria === 'calidad' ? 'bg-purple-100' :
                          'bg-orange-100'
                        }`}>
                          {metrica.categoria === 'financiero' && <DollarSign className="w-5 h-5 text-green-600" />}
                          {metrica.categoria === 'operacional' && <Activity className="w-5 h-5 text-blue-600" />}
                          {metrica.categoria === 'calidad' && <Award className="w-5 h-5 text-purple-600" />}
                          {metrica.categoria === 'recursos' && <Users className="w-5 h-5 text-orange-600" />}
                        </div>
                      </div>
                      <div className={`flex items-center gap-1 ${obtenerColorTendencia(metrica.tendencia)}`}>
                        {obtenerIconoTendencia(metrica.tendencia)}
                        <span className="text-sm font-medium">
                          {metrica.variacion > 0 ? '+' : ''}{metrica.variacion.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-600 mb-1">{metrica.titulo}</h3>
                      <p className="text-2xl font-bold text-gray-900 mb-2">
                        {formatearValor(metrica)}
                      </p>
                      <p className="text-xs text-gray-500">{metrica.descripcion}</p>

                      {metrica.meta && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>Progreso hacia meta</span>
                            <span>{((metrica.valor / metrica.meta) * 100).toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min((metrica.valor / metrica.meta) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* KPIs principales */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  Indicadores Clave de Rendimiento (KPIs)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {kpis.map((kpi) => (
                    <div key={kpi.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{kpi.nombre}</h4>
                        <Badge className={obtenerColorKPI(kpi.estado)}>
                          {kpi.estado}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between mb-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {kpi.valor_actual.toFixed(1)} {kpi.unidad}
                        </span>
                        <span className="text-sm text-gray-600">
                          Meta: {kpi.meta} {kpi.unidad}
                        </span>
                      </div>

                      <div className="mb-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              kpi.estado === 'excelente' ? 'bg-green-500' :
                              kpi.estado === 'bueno' ? 'bg-blue-500' :
                              kpi.estado === 'regular' ? 'bg-yellow-500' :
                              'bg-red-500'
                            }`}
                            style={{ width: `${calcularProgreso(kpi.valor_actual, kpi.meta)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">{kpi.descripcion}</span>
                        <div className={`flex items-center gap-1 ${
                          kpi.tendencia_mes > 0 ? 'text-green-600' :
                          kpi.tendencia_mes < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {kpi.tendencia_mes > 0 ? <TrendingUp className="w-3 h-3" /> :
                           kpi.tendencia_mes < 0 ? <TrendingDown className="w-3 h-3" /> :
                           <Activity className="w-3 h-3" />}
                          <span>{kpi.tendencia_mes > 0 ? '+' : ''}{kpi.tendencia_mes.toFixed(1)}%</span>
                        </div>
                      </div>

                      <div className="mt-2 text-xs text-gray-500">
                        Responsable: {kpi.area_responsable}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Pestaña Financiero */}
        {activeTab === 'financiero' && (
          <div className="space-y-6">
            {/* Métricas financieras principales */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <DollarSign className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Ingresos Totales</p>
                      <p className="text-2xl font-bold text-green-700">{formatearMoneda(2850000000)}</p>
                      <div className="flex items-center justify-end gap-1 text-green-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+7.5%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Meta mensual</span>
                      <span className="font-medium">{formatearMoneda(3000000000)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-red-100 rounded-lg">
                      <TrendingDown className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Gastos Operacionales</p>
                      <p className="text-2xl font-bold text-red-700">{formatearMoneda(2320000000)}</p>
                      <div className="flex items-center justify-end gap-1 text-red-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+3.2%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Presupuesto</span>
                      <span className="font-medium">{formatearMoneda(2400000000)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{ width: '97%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Target className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Margen Neto</p>
                      <p className="text-2xl font-bold text-blue-700">18.7%</p>
                      <div className="flex items-center justify-end gap-1 text-blue-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+2.1%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Meta anual</span>
                      <span className="font-medium">20%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '93.5%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Análisis de ingresos por servicio */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5 text-green-600" />
                  Distribución de Ingresos por Servicio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {[
                      { servicio: 'Hospitalizaciones', valor: 1200000000, porcentaje: 42.1, color: 'bg-blue-500' },
                      { servicio: 'Consultas Externas', valor: 850000000, porcentaje: 29.8, color: 'bg-green-500' },
                      { servicio: 'Cirugías', valor: 450000000, porcentaje: 15.8, color: 'bg-purple-500' },
                      { servicio: 'Laboratorio', valor: 200000000, porcentaje: 7.0, color: 'bg-yellow-500' },
                      { servicio: 'Imágenes Diagnósticas', valor: 150000000, porcentaje: 5.3, color: 'bg-red-500' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded ${item.color}`}></div>
                          <span className="font-medium text-gray-900">{item.servicio}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900">{formatearMoneda(item.valor)}</p>
                          <p className="text-sm text-gray-600">{item.porcentaje}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-48 h-48 mx-auto mb-4 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Total Ingresos</p>
                          <p className="text-2xl font-bold text-gray-900">{formatearMoneda(2850000000)}</p>
                        </div>
                      </div>
                      <Button onClick={() => handleVerAnalisisCompleto('financiero')}>
                        <BarChart3 className="w-4 h-4 mr-2" />
                        Ver Análisis Detallado
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Proyecciones financieras */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="w-5 h-5 text-blue-600" />
                  Proyecciones Financieras
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Próximo Mes</h4>
                    <p className="text-2xl font-bold text-blue-700">{formatearMoneda(2950000000)}</p>
                    <p className="text-sm text-green-600">+3.5% proyectado</p>
                  </div>
                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Próximo Trimestre</h4>
                    <p className="text-2xl font-bold text-blue-700">{formatearMoneda(8900000000)}</p>
                    <p className="text-sm text-green-600">+4.2% proyectado</p>
                  </div>
                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Año Completo</h4>
                    <p className="text-2xl font-bold text-blue-700">{formatearMoneda(35200000000)}</p>
                    <p className="text-sm text-green-600">+5.8% proyectado</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'operacional' && (
          <div className="space-y-6">
            {/* Métricas operacionales principales */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Pacientes/Día</p>
                      <p className="text-2xl font-bold text-blue-700">273</p>
                      <div className="flex items-center justify-end gap-1 text-blue-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+5.2%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <Clock className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Tiempo Atención</p>
                      <p className="text-2xl font-bold text-green-700">28.5min</p>
                      <div className="flex items-center justify-end gap-1 text-green-600">
                        <TrendingDown className="w-4 h-4" />
                        <span className="text-sm">-8.3%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Building className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Ocupación</p>
                      <p className="text-2xl font-bold text-purple-700">87.5%</p>
                      <div className="flex items-center justify-end gap-1 text-purple-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+6.3%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-orange-100 rounded-lg">
                      <Zap className="w-6 h-6 text-orange-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Eficiencia</p>
                      <p className="text-2xl font-bold text-orange-700">94.2%</p>
                      <div className="flex items-center justify-end gap-1 text-orange-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+2.1%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Productividad por servicio */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-blue-600" />
                  Productividad por Servicio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { servicio: 'Urgencias', pacientes: 2450, tiempo: 25, satisfaccion: 92.1, estado: 'bueno' },
                    { servicio: 'Consulta Externa', pacientes: 3200, tiempo: 18, satisfaccion: 95.8, estado: 'excelente' },
                    { servicio: 'Hospitalización', pacientes: 1800, tiempo: 4.2, satisfaccion: 94.5, estado: 'excelente' },
                    { servicio: 'Cirugías', pacientes: 450, tiempo: 85, satisfaccion: 96.2, estado: 'excelente' },
                    { servicio: 'Laboratorio', pacientes: 8900, tiempo: 2.5, satisfaccion: 93.7, estado: 'bueno' }
                  ].map((item, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">{item.servicio}</h4>
                        <Badge className={obtenerColorKPI(item.estado)}>
                          {item.estado}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Pacientes</p>
                          <p className="text-xl font-bold text-blue-700">{formatearNumero(item.pacientes)}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Tiempo Prom.</p>
                          <p className="text-xl font-bold text-green-700">{item.tiempo}{item.servicio === 'Hospitalización' ? ' días' : ' min'}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Satisfacción</p>
                          <p className="text-xl font-bold text-purple-700">{item.satisfaccion}%</p>
                        </div>
                        <div className="text-center">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                item.estado === 'excelente' ? 'bg-green-500' : 'bg-blue-500'
                              }`}
                              style={{ width: `${item.satisfaccion}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 text-center">
                  <Button onClick={() => handleVerAnalisisCompleto('operacional')}>
                    <PieChart className="w-4 h-4 mr-2" />
                    Ver Análisis Completo
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Ocupación de recursos */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="w-5 h-5 text-purple-600" />
                    Ocupación de Camas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { area: 'UCI', ocupadas: 18, total: 20, porcentaje: 90 },
                      { area: 'Medicina Interna', ocupadas: 45, total: 50, porcentaje: 90 },
                      { area: 'Cirugía', ocupadas: 28, total: 35, porcentaje: 80 },
                      { area: 'Pediatría', ocupadas: 15, total: 20, porcentaje: 75 },
                      { area: 'Maternidad', ocupadas: 12, total: 15, porcentaje: 80 }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{item.area}</p>
                          <p className="text-sm text-gray-600">{item.ocupadas}/{item.total} camas</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-purple-700">{item.porcentaje}%</p>
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-purple-500 h-2 rounded-full"
                              style={{ width: `${item.porcentaje}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Stethoscope className="w-5 h-5 text-blue-600" />
                    Utilización de Quirófanos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { quirofano: 'Quirófano 1', horas_uso: 8.5, horas_disponibles: 10, porcentaje: 85 },
                      { quirofano: 'Quirófano 2', horas_uso: 9.2, horas_disponibles: 10, porcentaje: 92 },
                      { quirofano: 'Quirófano 3', horas_uso: 7.8, horas_disponibles: 10, porcentaje: 78 },
                      { quirofano: 'Quirófano 4', horas_uso: 6.5, horas_disponibles: 8, porcentaje: 81 }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{item.quirofano}</p>
                          <p className="text-sm text-gray-600">{item.horas_uso}h / {item.horas_disponibles}h</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-blue-700">{item.porcentaje}%</p>
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${item.porcentaje}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'calidad' && (
          <div className="space-y-6">
            {/* Indicadores de calidad principales */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <Heart className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Satisfacción</p>
                      <p className="text-2xl font-bold text-green-700">94.2%</p>
                      <div className="flex items-center justify-end gap-1 text-green-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+0.4%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Shield className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Seguridad</p>
                      <p className="text-2xl font-bold text-blue-700">98.5%</p>
                      <div className="flex items-center justify-end gap-1 text-blue-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+1.2%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Award className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Acreditación</p>
                      <p className="text-2xl font-bold text-purple-700">96.8%</p>
                      <div className="flex items-center justify-end gap-1 text-purple-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+1.5%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-orange-100 rounded-lg">
                      <AlertTriangle className="w-6 h-6 text-orange-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Incidentes</p>
                      <p className="text-2xl font-bold text-orange-700">0.8%</p>
                      <div className="flex items-center justify-end gap-1 text-orange-600">
                        <TrendingDown className="w-4 h-4" />
                        <span className="text-sm">-0.3%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Indicadores detallados */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  Indicadores de Calidad Asistencial
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    { indicador: 'Mortalidad Hospitalaria', valor: 1.2, meta: 1.5, unidad: '%', estado: 'excelente' },
                    { indicador: 'Infecciones Nosocomiales', valor: 2.1, meta: 3.0, unidad: '%', estado: 'bueno' },
                    { indicador: 'Reingresos 30 días', valor: 4.8, meta: 6.0, unidad: '%', estado: 'bueno' },
                    { indicador: 'Tiempo Espera Cirugía', valor: 12.5, meta: 15.0, unidad: 'días', estado: 'bueno' },
                    { indicador: 'Adherencia Protocolos', valor: 96.2, meta: 95.0, unidad: '%', estado: 'excelente' },
                    { indicador: 'Eventos Adversos', valor: 0.8, meta: 1.2, unidad: '%', estado: 'excelente' }
                  ].map((item, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{item.indicador}</h4>
                        <Badge className={obtenerColorKPI(item.estado)}>
                          {item.estado}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {item.valor} {item.unidad}
                        </span>
                        <span className="text-sm text-gray-600">
                          Meta: {item.meta} {item.unidad}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            item.estado === 'excelente' ? 'bg-green-500' : 'bg-blue-500'
                          }`}
                          style={{ width: `${Math.min((item.valor / item.meta) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'recursos' && (
          <div className="space-y-6">
            {/* Métricas de RRHH principales */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Total Personal</p>
                      <p className="text-2xl font-bold text-blue-700">1,247</p>
                      <div className="flex items-center justify-end gap-1 text-blue-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+3.2%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <UserCheck className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Satisfacción</p>
                      <p className="text-2xl font-bold text-green-700">87.3%</p>
                      <div className="flex items-center justify-end gap-1 text-green-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+2.1%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-orange-100 rounded-lg">
                      <TrendingDown className="w-6 h-6 text-orange-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Rotación</p>
                      <p className="text-2xl font-bold text-orange-700">8.3%</p>
                      <div className="flex items-center justify-end gap-1 text-green-600">
                        <TrendingDown className="w-4 h-4" />
                        <span className="text-sm">-1.2%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Award className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Capacitación</p>
                      <p className="text-2xl font-bold text-purple-700">92.1%</p>
                      <div className="flex items-center justify-end gap-1 text-purple-600">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">+5.8%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Distribución del personal */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  Distribución del Personal por Área
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    { area: 'Médicos Especialistas', cantidad: 185, porcentaje: 14.8, color: 'bg-blue-500' },
                    { area: 'Enfermeras', cantidad: 320, porcentaje: 25.7, color: 'bg-green-500' },
                    { area: 'Auxiliares de Enfermería', cantidad: 280, porcentaje: 22.5, color: 'bg-purple-500' },
                    { area: 'Personal Administrativo', cantidad: 195, porcentaje: 15.6, color: 'bg-yellow-500' },
                    { area: 'Personal de Apoyo', cantidad: 145, porcentaje: 11.6, color: 'bg-red-500' },
                    { area: 'Técnicos y Terapeutas', cantidad: 122, porcentaje: 9.8, color: 'bg-indigo-500' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded ${item.color}`}></div>
                        <span className="font-medium text-gray-900">{item.area}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-gray-900">{formatearNumero(item.cantidad)}</p>
                        <p className="text-sm text-gray-600">{item.porcentaje}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Métricas de bienestar */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="w-5 h-5 text-red-600" />
                    Bienestar del Personal
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { metrica: 'Ausentismo', valor: 3.2, meta: 5.0, unidad: '%', estado: 'bueno' },
                      { metrica: 'Accidentes Laborales', valor: 0.8, meta: 1.5, unidad: '%', estado: 'excelente' },
                      { metrica: 'Clima Organizacional', valor: 87.3, meta: 85.0, unidad: '%', estado: 'excelente' },
                      { metrica: 'Burnout', valor: 12.5, meta: 15.0, unidad: '%', estado: 'bueno' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{item.metrica}</p>
                          <p className="text-sm text-gray-600">Meta: {item.meta}{item.unidad}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900">{item.valor}{item.unidad}</p>
                          <Badge className={obtenerColorKPI(item.estado)}>
                            {item.estado}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5 text-purple-600" />
                    Desarrollo Profesional
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { programa: 'Capacitación Continua', participacion: 92.1, meta: 90.0 },
                      { programa: 'Certificaciones', participacion: 78.5, meta: 75.0 },
                      { programa: 'Programas de Postgrado', participacion: 45.2, meta: 40.0 },
                      { programa: 'Investigación', participacion: 23.8, meta: 25.0 }
                    ].map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-900">{item.programa}</span>
                          <span className="text-sm text-gray-600">{item.participacion}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-500 h-2 rounded-full"
                            style={{ width: `${Math.min((item.participacion / 100) * 100, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Modales */}
        {modalAbierto === 'exportar' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <Download className="w-5 h-5 text-blue-600" />
                    Exportar Dashboard
                  </h2>
                  <Button variant="outline" onClick={handleCerrarModal}>
                    <XCircle className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Período seleccionado:</p>
                    <p className="font-medium text-gray-900">{periodoSeleccionado.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Contenido a exportar:</p>
                    <ul className="text-sm text-gray-900 space-y-1">
                      <li>• {datosModal?.metricas_incluidas} métricas ejecutivas</li>
                      <li>• {datosModal?.kpis_incluidos} indicadores KPI</li>
                      <li>• Análisis de tendencias</li>
                      <li>• Gráficos y visualizaciones</li>
                    </ul>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Formato de exportación:</p>
                    <div className="space-y-2">
                      <Button
                        className="w-full justify-start"
                        variant="outline"
                        onClick={() => handleDescargarReporte('pdf')}
                      >
                        <FileText className="w-4 h-4 mr-2" />
                        PDF - Reporte ejecutivo
                      </Button>
                      <Button
                        className="w-full justify-start"
                        variant="outline"
                        onClick={() => handleDescargarReporte('excel')}
                      >
                        <BarChart3 className="w-4 h-4 mr-2" />
                        Excel - Datos y gráficos
                      </Button>
                      <Button
                        className="w-full justify-start"
                        variant="outline"
                        onClick={() => handleDescargarReporte('powerpoint')}
                      >
                        <PieChart className="w-4 h-4 mr-2" />
                        PowerPoint - Presentación
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {modalAbierto === 'analisis' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                    Análisis {datosModal?.tipo === 'financiero' ? 'Financiero' : 'Operacional'} Detallado
                  </h2>
                  <Button variant="outline" onClick={handleCerrarModal}>
                    <XCircle className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                {datosModal?.tipo === 'financiero' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalle de Ingresos</h3>
                      <div className="space-y-3">
                        {datosModal.datos.ingresos_detalle.map((item: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <span className="font-medium text-gray-900">{item.concepto}</span>
                            <div className="text-right">
                              <p className="font-bold text-green-700">{formatearMoneda(item.valor)}</p>
                              <p className="text-sm text-gray-600">{item.porcentaje}%</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalle de Gastos</h3>
                      <div className="space-y-3">
                        {datosModal.datos.gastos_detalle.map((item: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <span className="font-medium text-gray-900">{item.concepto}</span>
                            <div className="text-right">
                              <p className="font-bold text-red-700">{formatearMoneda(item.valor)}</p>
                              <p className="text-sm text-gray-600">{item.porcentaje}%</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                {datosModal?.tipo === 'operacional' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Productividad por Servicio</h3>
                      <div className="space-y-4">
                        {datosModal.datos.servicios_productividad.map((item: any, index: number) => (
                          <div key={index} className="p-4 border border-gray-200 rounded-lg">
                            <h4 className="font-semibold text-gray-900 mb-3">{item.servicio}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="text-center">
                                <p className="text-sm text-gray-600">Pacientes/Procedimientos</p>
                                <p className="text-xl font-bold text-blue-700">{formatearNumero(item.pacientes || item.procedimientos || item.examenes)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-sm text-gray-600">Tiempo Promedio</p>
                                <p className="text-xl font-bold text-green-700">
                                  {item.tiempo_promedio || item.estancia_promedio || item.tiempo_entrega}
                                  {item.servicio === 'Hospitalización' ? ' días' : item.servicio === 'Laboratorio' ? ' hrs' : ' min'}
                                </p>
                              </div>
                              <div className="text-center">
                                <p className="text-sm text-gray-600">Satisfacción</p>
                                <p className="text-xl font-bold text-purple-700">{item.satisfaccion}%</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {modalAbierto === 'metricas' && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <Target className="w-5 h-5 text-blue-600" />
                    Métricas {datosModal?.categoria}
                  </h2>
                  <Button variant="outline" onClick={handleCerrarModal}>
                    <XCircle className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {datosModal?.metricas?.map((metrica: MetricaEjecutiva, index: number) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">{metrica.titulo}</h4>
                        <div className={`flex items-center gap-1 ${obtenerColorTendencia(metrica.tendencia)}`}>
                          {obtenerIconoTendencia(metrica.tendencia)}
                          <span className="text-sm font-medium">
                            {metrica.variacion > 0 ? '+' : ''}{metrica.variacion.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Valor Actual</p>
                          <p className="text-xl font-bold text-gray-900">{formatearValor(metrica)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Valor Anterior</p>
                          <p className="text-lg font-medium text-gray-700">
                            {metrica.formato === 'moneda' ? formatearMoneda(metrica.valorAnterior) :
                             metrica.formato === 'porcentaje' ? `${metrica.valorAnterior.toFixed(1)}%` :
                             formatearNumero(metrica.valorAnterior)}
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-3">{metrica.descripcion}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardEjecutivo;
