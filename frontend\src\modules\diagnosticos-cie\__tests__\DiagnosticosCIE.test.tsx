import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import DiagnosticosCIE from '../DiagnosticosCIE';
import { cie11Service } from '../../../services/cie11Service';

// Mock del servicio CIE-11
jest.mock('../../../services/cie11Service', () => ({
  cie11Service: {
    buscarDiagnosticos: jest.fn(),
    obtenerConfiguracion: jest.fn(),
    obtenerEstadisticas: jest.fn(),
  }
}));

// Mock del hook useAuth
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user',
      hospital_id: 1,
      nombre: 'Test User',
      rol: 'DOCTOR'
    }
  })
}));

// Wrapper para providers
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('DiagnosticosCIE', () => {
  const mockCie11Service = cie11Service as jest.Mocked<typeof cie11Service>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock de configuración
    mockCie11Service.obtenerConfiguracion.mockResolvedValue({
      api_habilitada: true,
      idioma_predeterminado: 'es',
      sincronizacion_automatica: true,
      ultima_sincronizacion: '2024-01-15T10:00:00Z'
    });

    // Mock de estadísticas
    mockCie11Service.obtenerEstadisticas.mockResolvedValue({
      total_busquedas: 1250,
      diagnosticos_mas_usados: [
        { codigo: 'BA00', titulo: 'Fiebre', frecuencia: 45 },
        { codigo: 'BD10', titulo: 'Dolor abdominal', frecuencia: 38 }
      ],
      capitulos_mas_usados: [
        { capitulo: 'I', frecuencia: 120 },
        { capitulo: 'II', frecuencia: 95 }
      ],
      tendencia_mensual: [
        { mes: 'Enero', cantidad: 150 },
        { mes: 'Febrero', cantidad: 180 }
      ]
    });

    // Mock de búsqueda
    mockCie11Service.buscarDiagnosticos.mockResolvedValue([
      {
        id: 'cie11-001',
        codigo: 'BA00',
        titulo: 'Fiebre',
        definicion: 'Elevación de la temperatura corporal',
        capitulo: 'I',
        es_hoja: true,
        inclusiones: ['Fiebre alta', 'Hipertermia'],
        exclusiones: ['Fiebre reumática'],
        url_oms: 'https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/BA00'
      },
      {
        id: 'cie11-002',
        codigo: 'BD10',
        titulo: 'Dolor abdominal',
        definicion: 'Dolor localizado en el abdomen',
        capitulo: 'I',
        es_hoja: true,
        inclusiones: ['Dolor de estómago', 'Cólico abdominal'],
        exclusiones: ['Dolor pélvico'],
        url_oms: 'https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/BD10'
      }
    ]);
  });

  describe('Renderizado inicial', () => {
    it('debe renderizar el componente correctamente', async () => {
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Verificar que el título principal esté presente
      expect(screen.getByText('Sistema de Diagnósticos CIE-10/CIE-11')).toBeInTheDocument();
      
      // Verificar que la descripción esté presente
      expect(screen.getByText('Búsqueda unificada, herramienta de transición y gestión de diagnósticos')).toBeInTheDocument();
      
      // Verificar que las pestañas estén presentes
      expect(screen.getByText('Búsqueda Unificada')).toBeInTheDocument();
      expect(screen.getByText('Herramienta de Transición')).toBeInTheDocument();
      expect(screen.getByText('Diagnósticos Favoritos')).toBeInTheDocument();
      expect(screen.getByText('Estadísticas de Uso')).toBeInTheDocument();
      expect(screen.getByText('Configuración')).toBeInTheDocument();
    });

    it('debe mostrar las estadísticas correctamente', async () => {
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('1,250')).toBeInTheDocument(); // Total búsquedas
        expect(screen.getByText('Conectado')).toBeInTheDocument(); // Estado API
        expect(screen.getByText('Hace 2h')).toBeInTheDocument(); // Última sincronización
      });
    });
  });

  describe('Funcionalidad de búsqueda', () => {
    it('debe permitir buscar diagnósticos', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Encontrar el campo de búsqueda
      const searchInput = screen.getByPlaceholderText('Buscar diagnósticos por código, título o síntomas...');
      expect(searchInput).toBeInTheDocument();

      // Escribir en el campo de búsqueda
      await user.type(searchInput, 'fiebre');

      // Esperar a que se ejecute la búsqueda (debounce)
      await waitFor(() => {
        expect(mockCie11Service.buscarDiagnosticos).toHaveBeenCalledWith({
          termino: 'fiebre',
          capitulo: '',
          solo_hojas: false,
          limite: 50
        });
      }, { timeout: 1000 });

      // Verificar que se muestren los resultados
      await waitFor(() => {
        expect(screen.getByText('BA00')).toBeInTheDocument();
        expect(screen.getByText('Fiebre')).toBeInTheDocument();
        expect(screen.getByText('Elevación de la temperatura corporal')).toBeInTheDocument();
      });
    });

    it('debe filtrar por tipo de CIE', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Cambiar el filtro de tipo
      const tipoSelect = screen.getByDisplayValue('CIE-10 y CIE-11');
      await user.selectOptions(tipoSelect, 'CIE11');

      expect(tipoSelect).toHaveValue('CIE11');
    });

    it('debe mostrar filtros avanzados al hacer clic', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Hacer clic en filtros avanzados
      const filtrosButton = screen.getByText('Filtros Avanzados');
      await user.click(filtrosButton);

      // Verificar que se muestren los filtros avanzados
      expect(screen.getByText('Solo códigos finales (hojas)')).toBeInTheDocument();
      expect(screen.getByText('Solo favoritos')).toBeInTheDocument();
    });
  });

  describe('Navegación por pestañas', () => {
    it('debe cambiar a la pestaña de transición', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Hacer clic en la pestaña de transición
      const transicionTab = screen.getByText('Herramienta de Transición');
      await user.click(transicionTab);

      // Verificar que se muestre el contenido de la pestaña
      expect(screen.getByText('Herramienta de Transición CIE-10 → CIE-11')).toBeInTheDocument();
      expect(screen.getByText('Compare y migre códigos de CIE-10 a CIE-11 con mapeo automático y sugerencias inteligentes.')).toBeInTheDocument();
    });

    it('debe cambiar a la pestaña de favoritos', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Hacer clic en la pestaña de favoritos
      const favoritosTab = screen.getByText('Diagnósticos Favoritos');
      await user.click(favoritosTab);

      // Verificar que se muestre el contenido de la pestaña
      expect(screen.getByText('Diagnósticos Más Utilizados')).toBeInTheDocument();
      expect(screen.getByText('Acceso rápido a los diagnósticos que más utiliza en su práctica clínica.')).toBeInTheDocument();
    });

    it('debe cambiar a la pestaña de estadísticas', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Hacer clic en la pestaña de estadísticas
      const estadisticasTab = screen.getByText('Estadísticas de Uso');
      await user.click(estadisticasTab);

      // Verificar que se muestre el contenido de la pestaña
      expect(screen.getByText('Análisis de Uso')).toBeInTheDocument();
      expect(screen.getByText('Visualice patrones de uso, tendencias y estadísticas de diagnósticos.')).toBeInTheDocument();
    });

    it('debe cambiar a la pestaña de configuración', async () => {
      const user = userEvent.setup();
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Hacer clic en la pestaña de configuración
      const configuracionTab = screen.getByText('Configuración');
      await user.click(configuracionTab);

      // Verificar que se muestre el contenido de la pestaña
      expect(screen.getByText('Configuración del Sistema')).toBeInTheDocument();
      expect(screen.getByText('Configure la integración con la API de la OMS, idiomas y sincronización.')).toBeInTheDocument();
    });
  });

  describe('Manejo de errores', () => {
    it('debe mostrar mensaje de error cuando falla la búsqueda', async () => {
      const user = userEvent.setup();
      
      // Mock de error en la búsqueda
      mockCie11Service.buscarDiagnosticos.mockRejectedValue(new Error('Error de conexión'));
      
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Escribir en el campo de búsqueda
      const searchInput = screen.getByPlaceholderText('Buscar diagnósticos por código, título o síntomas...');
      await user.type(searchInput, 'test');

      // Esperar a que se muestre el error
      await waitFor(() => {
        expect(screen.getByText('Error al realizar la búsqueda')).toBeInTheDocument();
      });
    });

    it('debe mostrar mensaje cuando no hay resultados', async () => {
      const user = userEvent.setup();
      
      // Mock de búsqueda sin resultados
      mockCie11Service.buscarDiagnosticos.mockResolvedValue([]);
      
      render(<DiagnosticosCIE />, { wrapper: createWrapper() });

      // Escribir en el campo de búsqueda
      const searchInput = screen.getByPlaceholderText('Buscar diagnósticos por código, título o síntomas...');
      await user.type(searchInput, 'xyz123');

      // Esperar a que se muestre el mensaje de no resultados
      await waitFor(() => {
        expect(screen.getByText('No se encontraron resultados')).toBeInTheDocument();
        expect(screen.getByText('No se encontraron diagnósticos que coincidan con "xyz123"')).toBeInTheDocument();
      });
    });
  });
});
