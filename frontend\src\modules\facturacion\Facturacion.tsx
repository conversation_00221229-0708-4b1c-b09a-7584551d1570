import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  FileText,
  DollarSign,
  Calendar,
  User,
  Building,
  CreditCard,
  Download,
  Send,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  TrendingUp,
  Receipt,
  Calculator,
  Printer,
  Mail,
  Phone,
  MapPin,
  BarChart3
} from 'lucide-react';

// Interfaces para Facturación Electrónica FEV/DIAN
interface Factura {
  id: string;
  numero_factura: string;
  cufe?: string; // Código Único de Facturación Electrónica
  firma_digital?: string;
  codigo_qr?: string;
  fecha_emision: string;
  fecha_vencimiento: string;
  periodo_facturado: string;
  fecha_fin_periodo?: string;
  nit_ips: string;
  codigo_prestador: string;
  tipo_operacion: '10' | '20' | '30'; // 10=Estándar, 20=AIU, 30=Mandatos
  tipo_servicio: string;
  paciente: {
    id: string;
    nombres: string;
    apellidos: string;
    tipo_documento: string;
    numero_documento: string;
    telefono: string;
    email: string;
    eps?: string;
    numero_autorizacion?: string;
  };
  servicios: Array<{
    id: string;
    descripcion: string;
    cantidad: number;
    valor_unitario: number;
    valor_total: number;
    codigo_cups: string;
    numero_autorizacion?: string;
    tipo_servicio: string;
  }>;
  subtotal: number;
  descuentos: number;
  impuestos: {
    iva: number;
    retefuente: number;
    reteica: number;
    total: number;
  };
  total: number;
  estado: 'generada' | 'validada' | 'enviada_dian' | 'pagada' | 'rechazada' | 'anulada';
  estado_dian?: 'pendiente' | 'aceptada' | 'rechazada';
  metodo_pago?: string;
  fecha_pago?: string;
  observaciones?: string;
  xml_dian?: string;
  pdf_url?: string;
  detalles_qr?: any;
  detalles_qr_completo?: any;
  created_at: string;
  updated_at: string;
}

interface ResumenFacturacion {
  total_facturas: number;
  facturas_pendientes: number;
  facturas_pagadas: number;
  facturas_vencidas: number;
  valor_total_pendiente: number;
  valor_total_recaudado: number;
  valor_promedio_factura: number;
}

interface FiltrosFacturacion {
  busqueda: string;
  estado: string;
  fecha_desde: string;
  fecha_hasta: string;
  metodo_pago: string;
}

export const Facturacion: React.FC = () => {
  const { user } = useAuth();

  // Test simple para verificar que el componente se carga
  console.log('Facturacion component loaded', { user });
  const [activeTab, setActiveTab] = useState('facturas');

  // Estados de datos
  const [facturas, setFacturas] = useState<Factura[]>([]);
  const [resumen, setResumen] = useState<ResumenFacturacion | null>(null);
  const [cargando, setCargando] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Estados de UI
  const [filtros, setFiltros] = useState<FiltrosFacturacion>({
    busqueda: '',
    estado: '',
    fecha_desde: '',
    fecha_hasta: '',
    metodo_pago: ''
  });
  const [mostrarFiltros, setMostrarFiltros] = useState(false);
  const [facturaSeleccionada, setFacturaSeleccionada] = useState<Factura | null>(null);
  const [mostrarModal, setMostrarModal] = useState(false);
  const [mostrarFormularioNuevaFactura, setMostrarFormularioNuevaFactura] = useState(false);
  const [tipoReporte, setTipoReporte] = useState<string | null>(null);

  // Datos mock para demostración con cumplimiento FEV/DIAN
  const facturasMock: Factura[] = [
    {
      id: 'fact-001',
      numero_factura: 'FHIP-2024-001',
      cufe: 'a1b2c3d4e5f6789012345678901234567890abcd',
      codigo_qr: 'https://catalogo-vpfe.dian.gov.co/document/searchqr?documentkey=a1b2c3d4e5f6789012345678901234567890abcd',
      fecha_emision: '2024-01-15',
      fecha_vencimiento: '2024-02-15',
      periodo_facturado: '2024-01-15',
      nit_ips: '900123456-7',
      codigo_prestador: 'HIP001',
      tipo_operacion: '10',
      tipo_servicio: 'Consulta Externa',
      paciente: {
        id: 'pac-001',
        nombres: 'María Elena',
        apellidos: 'González López',
        tipo_documento: 'CC',
        numero_documento: '12345678',
        telefono: '3001234567',
        email: '<EMAIL>',
        eps: 'EPS001',
        numero_autorizacion: 'AUT-2024-001'
      },
      servicios: [
        {
          id: 'serv-001',
          descripcion: 'Consulta Medicina General',
          cantidad: 1,
          valor_unitario: 80000,
          valor_total: 80000,
          codigo_cups: '890201',
          numero_autorizacion: 'AUT-2024-001',
          tipo_servicio: 'Consulta'
        },
        {
          id: 'serv-002',
          descripcion: 'Hemograma Completo',
          cantidad: 1,
          valor_unitario: 45000,
          valor_total: 45000,
          codigo_cups: '902210',
          numero_autorizacion: 'AUT-2024-002',
          tipo_servicio: 'Laboratorio'
        }
      ],
      subtotal: 125000,
      descuentos: 0,
      impuestos: {
        iva: 23750, // 19%
        retefuente: 3125, // 2.5%
        reteica: 518, // 0.414%
        total: 27393
      },
      total: 152393,
      estado: 'validada',
      estado_dian: 'aceptada',
      xml_dian: '<xml>...</xml>',
      pdf_url: '/facturas/FHIP-2024-001.pdf',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: 'fact-002',
      numero_factura: 'FHIP-2024-002',
      cufe: 'b2c3d4e5f6789012345678901234567890abcde1',
      codigo_qr: 'https://catalogo-vpfe.dian.gov.co/document/searchqr?documentkey=b2c3d4e5f6789012345678901234567890abcde1',
      fecha_emision: '2024-01-16',
      fecha_vencimiento: '2024-02-16',
      periodo_facturado: '2024-01-16',
      nit_ips: '900123456-7',
      codigo_prestador: 'HIP001',
      tipo_operacion: '10',
      tipo_servicio: 'Cirugía Ambulatoria',
      paciente: {
        id: 'pac-002',
        nombres: 'Carlos Alberto',
        apellidos: 'Rodríguez Pérez',
        tipo_documento: 'CC',
        numero_documento: '87654321',
        telefono: '3009876543',
        email: '<EMAIL>',
        eps: 'EPS002',
        numero_autorizacion: 'AUT-2024-003'
      },
      servicios: [
        {
          id: 'serv-003',
          descripcion: 'Escisión de Lesión Cutánea',
          cantidad: 1,
          valor_unitario: 350000,
          valor_total: 350000,
          codigo_cups: '890301',
          numero_autorizacion: 'AUT-2024-003',
          tipo_servicio: 'Cirugía'
        }
      ],
      subtotal: 350000,
      descuentos: 35000,
      impuestos: {
        iva: 59850, // 19% sobre (350000-35000)
        retefuente: 8750, // 2.5%
        reteica: 1449, // 0.414%
        total: 70049
      },
      total: 385049,
      estado: 'pagada',
      estado_dian: 'aceptada',
      metodo_pago: 'Tarjeta de Crédito',
      fecha_pago: '2024-01-18',
      xml_dian: '<xml>...</xml>',
      pdf_url: '/facturas/FHIP-2024-002.pdf',
      created_at: '2024-01-16T14:20:00Z',
      updated_at: '2024-01-18T09:15:00Z'
    },
    {
      id: 'fact-003',
      numero_factura: 'FHIP-2024-003',
      cufe: 'c3d4e5f6789012345678901234567890abcdef12',
      codigo_qr: 'https://catalogo-vpfe.dian.gov.co/document/searchqr?documentkey=c3d4e5f6789012345678901234567890abcdef12',
      fecha_emision: '2024-01-10',
      fecha_vencimiento: '2024-02-10',
      periodo_facturado: '2024-01-10',
      fecha_fin_periodo: '2024-01-13',
      nit_ips: '900123456-7',
      codigo_prestador: 'HIP001',
      tipo_operacion: '10',
      tipo_servicio: 'Hospitalización',
      paciente: {
        id: 'pac-003',
        nombres: 'Ana Sofía',
        apellidos: 'Martínez Silva',
        tipo_documento: 'CC',
        numero_documento: '11223344',
        telefono: '3005556677',
        email: '<EMAIL>',
        eps: 'EPS003',
        numero_autorizacion: 'AUT-2024-004'
      },
      servicios: [
        {
          id: 'serv-004',
          descripcion: 'Estancia Hospitalaria - Medicina Interna',
          cantidad: 3,
          valor_unitario: 120000,
          valor_total: 360000,
          codigo_cups: '890401',
          numero_autorizacion: 'AUT-2024-004',
          tipo_servicio: 'Hospitalización'
        }
      ],
      subtotal: 360000,
      descuentos: 0,
      impuestos: {
        iva: 68400, // 19%
        retefuente: 9000, // 2.5%
        reteica: 1490, // 0.414%
        total: 78890
      },
      total: 438890,
      estado: 'enviada_dian',
      estado_dian: 'pendiente',
      xml_dian: '<xml>...</xml>',
      pdf_url: '/facturas/FHIP-2024-003.pdf',
      created_at: '2024-01-10T08:45:00Z',
      updated_at: '2024-01-10T08:45:00Z'
    }
  ];

  const resumenMock: ResumenFacturacion = {
    total_facturas: 156,
    facturas_pendientes: 45,
    facturas_pagadas: 98,
    facturas_vencidas: 13,
    valor_total_pendiente: 12500000,
    valor_total_recaudado: 45800000,
    valor_promedio_factura: 285000
  };

  // Cargar datos
  useEffect(() => {
    const cargarDatos = async () => {
      setCargando(true);
      try {
        // Simular carga de datos
        await new Promise(resolve => setTimeout(resolve, 1000));
        setFacturas(facturasMock);
        setResumen(resumenMock);
      } catch (error) {
        setError('Error al cargar datos de facturación');
      } finally {
        setCargando(false);
      }
    };

    cargarDatos();
  }, []);

  // Filtrar facturas
  const facturasFiltradas = facturas.filter(factura => {
    const cumpleBusqueda = !filtros.busqueda ||
      factura.numero.toLowerCase().includes(filtros.busqueda.toLowerCase()) ||
      `${factura.paciente.nombres} ${factura.paciente.apellidos}`.toLowerCase().includes(filtros.busqueda.toLowerCase()) ||
      factura.paciente.documento.includes(filtros.busqueda);

    const cumpleEstado = !filtros.estado || factura.estado === filtros.estado;

    const cumpleFecha = (!filtros.fecha_desde || factura.fecha_emision >= filtros.fecha_desde) &&
                       (!filtros.fecha_hasta || factura.fecha_emision <= filtros.fecha_hasta);

    const cumpleMetodoPago = !filtros.metodo_pago || factura.metodo_pago === filtros.metodo_pago;

    return cumpleBusqueda && cumpleEstado && cumpleFecha && cumpleMetodoPago;
  });

  const obtenerColorEstado = (estado: string) => {
    switch (estado) {
      case 'pagada':
        return 'bg-green-100 text-green-800';
      case 'generada':
        return 'bg-blue-100 text-blue-800';
      case 'validada':
        return 'bg-indigo-100 text-indigo-800';
      case 'enviada_dian':
        return 'bg-purple-100 text-purple-800';
      case 'rechazada':
        return 'bg-red-100 text-red-800';
      case 'anulada':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const obtenerIconoEstado = (estado: string) => {
    switch (estado) {
      case 'pagada':
        return <CheckCircle className="w-4 h-4" />;
      case 'generada':
        return <FileText className="w-4 h-4" />;
      case 'validada':
        return <CheckCircle className="w-4 h-4" />;
      case 'enviada_dian':
        return <Send className="w-4 h-4" />;
      case 'rechazada':
        return <XCircle className="w-4 h-4" />;
      case 'anulada':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const obtenerColorEstadoDian = (estado?: string) => {
    switch (estado) {
      case 'aceptada':
        return 'bg-green-100 text-green-800';
      case 'rechazada':
        return 'bg-red-100 text-red-800';
      case 'pendiente':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatearMoneda = (valor: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(valor);
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Funciones para manejar acciones
  const handleNuevaFactura = () => {
    const confirmar = confirm(
      '🧾 NUEVA FACTURA ELECTRÓNICA FEV\n\n' +
      '¿Desea crear una nueva factura electrónica?\n\n' +
      '✅ Cumple normativa DIAN\n' +
      '✅ Generación automática de CUFE\n' +
      '✅ Código QR integrado\n' +
      '✅ Firma digital\n' +
      '✅ Envío automático a DIAN\n\n' +
      'Se abrirá el formulario de creación.'
    );

    if (confirmar) {
      setMostrarFormularioNuevaFactura(true);

      // Simular proceso de creación de factura FEV
      setTimeout(() => {
        const numeroFactura = `FHIP-2024-${String(facturas.length + 1).padStart(3, '0')}`;
        const cufe = generateCUFE();

        alert(
          `✅ FACTURA ELECTRÓNICA CREADA\n\n` +
          `📄 Número: ${numeroFactura}\n` +
          `🔐 CUFE: ${cufe.substring(0, 20)}...\n` +
          `📅 Fecha: ${new Date().toLocaleDateString()}\n` +
          `🏥 NIT IPS: 900123456-7\n` +
          `📋 Código Prestador: HIP001\n\n` +
          `La factura ha sido generada y está lista para:\n` +
          `• Agregar servicios médicos\n` +
          `• Validar información\n` +
          `• Enviar a DIAN\n` +
          `• Generar PDF con QR`
        );

        setMostrarFormularioNuevaFactura(false);
      }, 2000);

      console.log('Creando nueva factura FEV con cumplimiento DIAN...');
    }
  };

  // Función para generar CUFE (simulado)
  const generateCUFE = () => {
    const chars = '0123456789abcdef';
    let result = '';
    for (let i = 0; i < 40; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleExportarFacturas = () => {
    console.log('Exportando facturas...', { filtros, total: facturasFiltradas.length });
    // Aquí se implementaría la lógica de exportación
    alert(`Exportando ${facturasFiltradas.length} facturas con los filtros aplicados`);
  };

  const handleGenerarReporte = (tipo: string) => {
    setTipoReporte(tipo);
    console.log('Generando reporte:', tipo);

    switch (tipo) {
      case 'ventas':
        alert('Generando reporte de ventas...\n\nEste reporte incluirá:\n- Ingresos por período\n- Servicios más facturados\n- Tendencias de ventas');
        break;
      case 'cartera':
        alert('Generando reporte de cartera vencida...\n\nEste reporte incluirá:\n- Facturas vencidas\n- Análisis de morosidad\n- Gestión de cobro');
        break;
      case 'fiscal':
        alert('Generando reporte fiscal...\n\nEste reporte incluirá:\n- Información fiscal\n- Impuestos recaudados\n- Datos para declaraciones');
        break;
      case 'paciente':
        const paciente = prompt('Ingrese el nombre o documento del paciente:');
        if (paciente) {
          alert(`Generando reporte para el paciente: ${paciente}\n\nEste reporte incluirá:\n- Historial de facturación\n- Análisis de frecuencia\n- Valores totales`);
        }
        break;
      case 'servicio':
        alert('Generando análisis por servicio...\n\nEste reporte incluirá:\n- Rentabilidad por servicio\n- Códigos CUPS más utilizados\n- Análisis de demanda');
        break;
      case 'metodos-pago':
        alert('Generando análisis de métodos de pago...\n\nEste reporte incluirá:\n- Distribución por método\n- Preferencias de pago\n- Tendencias de uso');
        break;
      default:
        alert('Generando reporte...');
    }
  };

  const handleRegistrarPago = (factura: Factura) => {
    const metodoPago = prompt(`Registrar pago para la factura ${factura.numero}\nTotal: ${formatearMoneda(factura.total)}\n\nSeleccione método de pago:\n1. Efectivo\n2. Tarjeta de Crédito\n3. Tarjeta de Débito\n4. Transferencia\n5. Cheque\n\nIngrese el número (1-5):`);

    if (metodoPago) {
      const metodos = ['', 'Efectivo', 'Tarjeta de Crédito', 'Tarjeta de Débito', 'Transferencia', 'Cheque'];
      const metodoSeleccionado = metodos[parseInt(metodoPago)] || 'Efectivo';

      alert(`Pago registrado exitosamente!\n\nFactura: ${factura.numero}\nMonto: ${formatearMoneda(factura.total)}\nMétodo: ${metodoSeleccionado}\nFecha: ${new Date().toLocaleDateString()}`);

      // Aquí se actualizaría el estado de la factura
      console.log('Pago registrado:', { factura: factura.id, metodo: metodoSeleccionado, monto: factura.total });
    }
  };

  const handleEnviarRecordatorio = (factura: Factura) => {
    const confirmar = confirm(`¿Enviar recordatorio de pago para la factura ${factura.numero}?\n\nPaciente: ${factura.paciente.nombres} ${factura.paciente.apellidos}\nMonto: ${formatearMoneda(factura.total)}\nVencimiento: ${formatearFecha(factura.fecha_vencimiento)}`);

    if (confirmar) {
      alert(`Recordatorio enviado exitosamente!\n\nSe ha enviado un recordatorio por email a: ${factura.paciente.email}\nY por SMS al: ${factura.paciente.telefono}`);
      console.log('Recordatorio enviado:', { factura: factura.id, email: factura.paciente.email });
    }
  };

  const handleVerFactura = (factura: Factura) => {
    setFacturaSeleccionada(factura);
    setMostrarModal(true);
    console.log('Mostrando detalles de factura:', factura);
  };

  const handleEditarFactura = (factura: Factura) => {
    alert(`Editando factura ${factura.numero}\n\nEsta funcionalidad abrirá el formulario de edición con los datos actuales de la factura.`);
    console.log('Editando factura:', factura.id);
  };

  const handleImprimirFactura = (factura: Factura) => {
    alert(`Imprimiendo factura ${factura.numero}\n\nSe abrirá una ventana con la vista previa de impresión de la factura.`);
    console.log('Imprimiendo factura:', factura.id);
    // Aquí se abriría una ventana de impresión o se generaría un PDF
  };

  const handleGuardarConfiguracion = (tipo: string) => {
    alert(`Configuración de ${tipo} guardada exitosamente!\n\nLos cambios se han aplicado al sistema.`);
    console.log('Configuración guardada:', tipo);
  };

  // Funciones específicas para FEV/DIAN
  const handleEnviarDian = (factura: Factura) => {
    const confirmar = confirm(
      `📤 ENVIAR A DIAN\n\n` +
      `Factura: ${factura.numero_factura}\n` +
      `CUFE: ${factura.cufe?.substring(0, 20)}...\n` +
      `Total: ${formatearMoneda(factura.total)}\n\n` +
      `¿Confirma el envío a la DIAN?`
    );

    if (confirmar) {
      alert(
        `✅ ENVIADO A DIAN EXITOSAMENTE\n\n` +
        `📄 Factura: ${factura.numero_factura}\n` +
        `🕐 Fecha envío: ${new Date().toLocaleString()}\n` +
        `📋 Estado: Pendiente validación DIAN\n` +
        `⏱️ Tiempo estimado respuesta: 5-10 minutos\n\n` +
        `Se notificará cuando la DIAN responda.`
      );
    }
  };

  const handleDescargarXML = (factura: Factura) => {
    alert(
      `📥 DESCARGANDO XML DIAN\n\n` +
      `Factura: ${factura.numero_factura}\n` +
      `Archivo: ${factura.numero_factura}.xml\n` +
      `Tamaño: ~15KB\n\n` +
      `El archivo XML se descargará automáticamente.`
    );
    console.log('Descargando XML DIAN:', factura.numero_factura);
  };

  const handleVerificarCUFE = (factura: Factura) => {
    alert(
      `🔍 VERIFICACIÓN CUFE\n\n` +
      `Factura: ${factura.numero_factura}\n` +
      `CUFE: ${factura.cufe}\n` +
      `Estado DIAN: ${factura.estado_dian || 'No enviada'}\n` +
      `QR: ${factura.codigo_qr ? 'Generado' : 'Pendiente'}\n\n` +
      `✅ CUFE válido y verificado`
    );
  };

  const handleGenerarQR = (factura: Factura) => {
    if (factura.codigo_qr) {
      alert(
        `📱 CÓDIGO QR GENERADO\n\n` +
        `Factura: ${factura.numero_factura}\n` +
        `URL: ${factura.codigo_qr}\n\n` +
        `El código QR permite:\n` +
        `• Verificación en línea\n` +
        `• Consulta estado DIAN\n` +
        `• Descarga PDF oficial\n` +
        `• Validación autenticidad`
      );
    } else {
      alert('⚠️ Código QR no disponible. La factura debe estar validada por la DIAN.');
    }
  };

  if (cargando) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando datos de facturación...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-red-50 border border-red-200">
            <CardContent className="p-6 text-center">
              <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-900 mb-2">Error al cargar datos</h3>
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Reintentar
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Receipt className="w-8 h-8 text-blue-600" />
              Facturación
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de facturación y cobros
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={handleExportarFacturas}>
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleNuevaFactura}>
              <Plus className="w-4 h-4 mr-2" />
              Nueva Factura
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        {resumen && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Facturas</p>
                    <p className="text-2xl font-bold text-blue-700">{resumen.total_facturas}</p>
                    <div className="flex items-center mt-2">
                      <FileText className="w-4 h-4 text-blue-600 mr-1" />
                      <span className="text-sm text-blue-600">Este mes</span>
                    </div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                    <FileText className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pendientes</p>
                    <p className="text-2xl font-bold text-yellow-700">{resumen.facturas_pendientes}</p>
                    <div className="flex items-center mt-2">
                      <Clock className="w-4 h-4 text-yellow-600 mr-1" />
                      <span className="text-sm text-yellow-600">{formatearMoneda(resumen.valor_total_pendiente)}</span>
                    </div>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                    <Clock className="w-8 h-8 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pagadas</p>
                    <p className="text-2xl font-bold text-green-700">{resumen.facturas_pagadas}</p>
                    <div className="flex items-center mt-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">{formatearMoneda(resumen.valor_total_recaudado)}</span>
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Vencidas</p>
                    <p className="text-2xl font-bold text-red-700">{resumen.facturas_vencidas}</p>
                    <div className="flex items-center mt-2">
                      <AlertCircle className="w-4 h-4 text-red-600 mr-1" />
                      <span className="text-sm text-red-600">Requieren atención</span>
                    </div>
                  </div>
                  <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                    <AlertCircle className="w-8 h-8 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeTab === 'facturas' ? 'default' : 'outline'}
                onClick={() => setActiveTab('facturas')}
                className="flex items-center gap-2"
              >
                <FileText className="w-4 h-4" />
                Facturas
              </Button>
              <Button
                variant={activeTab === 'reportes' ? 'default' : 'outline'}
                onClick={() => setActiveTab('reportes')}
                className="flex items-center gap-2"
              >
                <TrendingUp className="w-4 h-4" />
                Reportes
              </Button>
              <Button
                variant={activeTab === 'configuracion' ? 'default' : 'outline'}
                onClick={() => setActiveTab('configuracion')}
                className="flex items-center gap-2"
              >
                <Calculator className="w-4 h-4" />
                Configuración
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'facturas' && (
          <>
            {/* Filtros y búsqueda */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Barra de búsqueda */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por número, paciente o documento..."
                        className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={filtros.busqueda}
                        onChange={(e) => setFiltros({ ...filtros, busqueda: e.target.value })}
                      />
                    </div>
                    <Button
                      variant={mostrarFiltros ? 'default' : 'outline'}
                      onClick={() => setMostrarFiltros(!mostrarFiltros)}
                    >
                      <Filter className="w-4 h-4 mr-2" />
                      Filtros
                    </Button>
                  </div>

                  {/* Filtros avanzados */}
                  {mostrarFiltros && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Estado</label>
                        <select
                          value={filtros.estado}
                          onChange={(e) => setFiltros({ ...filtros, estado: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Todos los estados</option>
                          <option value="pendiente">Pendiente</option>
                          <option value="pagada">Pagada</option>
                          <option value="vencida">Vencida</option>
                          <option value="anulada">Anulada</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Fecha Desde</label>
                        <input
                          type="date"
                          value={filtros.fecha_desde}
                          onChange={(e) => setFiltros({ ...filtros, fecha_desde: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Fecha Hasta</label>
                        <input
                          type="date"
                          value={filtros.fecha_hasta}
                          onChange={(e) => setFiltros({ ...filtros, fecha_hasta: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Método de Pago</label>
                        <select
                          value={filtros.metodo_pago}
                          onChange={(e) => setFiltros({ ...filtros, metodo_pago: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Todos los métodos</option>
                          <option value="Efectivo">Efectivo</option>
                          <option value="Tarjeta de Crédito">Tarjeta de Crédito</option>
                          <option value="Tarjeta de Débito">Tarjeta de Débito</option>
                          <option value="Transferencia">Transferencia</option>
                          <option value="Cheque">Cheque</option>
                        </select>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Lista de facturas */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Facturas ({facturasFiltradas.length})
                </h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Printer className="w-4 h-4 mr-2" />
                    Imprimir
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Exportar
                  </Button>
                </div>
              </div>

              {facturasFiltradas.length === 0 ? (
                <Card className="bg-gray-50 border border-gray-200">
                  <CardContent className="p-8 text-center">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No se encontraron facturas</h3>
                    <p className="text-gray-600 mb-4">
                      No hay facturas que coincidan con los filtros aplicados.
                    </p>
                    <Button onClick={() => setFiltros({
                      busqueda: '',
                      estado: '',
                      fecha_desde: '',
                      fecha_hasta: '',
                      metodo_pago: ''
                    })}>
                      Limpiar filtros
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                facturasFiltradas.map((factura) => (
                  <Card key={factura.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Receipt className="w-6 h-6 text-blue-600" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="text-lg font-semibold text-gray-900">{factura.numero_factura}</h4>
                              <Badge className={obtenerColorEstado(factura.estado)}>
                                {obtenerIconoEstado(factura.estado)}
                                <span className="ml-1 capitalize">{factura.estado.replace('_', ' ')}</span>
                              </Badge>
                              {factura.estado_dian && (
                                <Badge className={obtenerColorEstadoDian(factura.estado_dian)}>
                                  <span className="text-xs">DIAN: {factura.estado_dian}</span>
                                </Badge>
                              )}
                              {factura.cufe && (
                                <Badge className="bg-blue-50 text-blue-700 border border-blue-200">
                                  <span className="text-xs">FEV</span>
                                </Badge>
                              )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-600">Paciente</p>
                                <p className="font-medium text-gray-900">
                                  {factura.paciente.nombres} {factura.paciente.apellidos}
                                </p>
                                <p className="text-sm text-gray-600">
                                  {factura.paciente.tipo_documento}: {factura.paciente.numero_documento}
                                </p>
                                {factura.paciente.eps && (
                                  <p className="text-xs text-blue-600">EPS: {factura.paciente.eps}</p>
                                )}
                              </div>

                              <div>
                                <p className="text-sm text-gray-600">Fechas</p>
                                <p className="font-medium text-gray-900">
                                  Emisión: {formatearFecha(factura.fecha_emision)}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Vencimiento: {formatearFecha(factura.fecha_vencimiento)}
                                </p>
                              </div>

                              <div>
                                <p className="text-sm text-gray-600">FEV/DIAN</p>
                                {factura.cufe && (
                                  <p className="font-mono text-xs text-gray-700">
                                    CUFE: {factura.cufe.substring(0, 12)}...
                                  </p>
                                )}
                                <p className="text-sm text-gray-600">
                                  Tipo: {factura.tipo_servicio}
                                </p>
                                {factura.codigo_qr && (
                                  <p className="text-xs text-green-600">✅ QR Generado</p>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm text-gray-600">Total</p>
                                <p className="text-xl font-bold text-blue-700">{formatearMoneda(factura.total)}</p>
                              </div>

                              {factura.estado === 'pagada' && factura.metodo_pago && (
                                <div className="text-right">
                                  <p className="text-sm text-gray-600">Pagado con</p>
                                  <p className="font-medium text-green-700">{factura.metodo_pago}</p>
                                  <p className="text-sm text-gray-600">{formatearFecha(factura.fecha_pago!)}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleVerFactura(factura)}
                              title="Ver detalles"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditarFactura(factura)}
                              title="Editar"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleImprimirFactura(factura)}
                              title="Imprimir PDF"
                            >
                              <Printer className="w-4 h-4" />
                            </Button>
                          </div>

                          {/* Botones específicos FEV/DIAN */}
                          <div className="flex gap-2">
                            {factura.cufe && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleVerificarCUFE(factura)}
                                title="Verificar CUFE"
                                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                            )}
                            {factura.codigo_qr && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleGenerarQR(factura)}
                                title="Ver código QR"
                                className="text-green-600 border-green-200 hover:bg-green-50"
                              >
                                <span className="text-xs">QR</span>
                              </Button>
                            )}
                            {factura.xml_dian && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDescargarXML(factura)}
                                title="Descargar XML DIAN"
                                className="text-purple-600 border-purple-200 hover:bg-purple-50"
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                            )}
                          </div>

                          {/* Acciones según estado FEV */}
                          {factura.estado === 'validada' && !factura.estado_dian && (
                            <Button
                              size="sm"
                              className="bg-blue-600 hover:bg-blue-700"
                              onClick={() => handleEnviarDian(factura)}
                            >
                              <Send className="w-4 h-4 mr-2" />
                              Enviar a DIAN
                            </Button>
                          )}

                          {(factura.estado === 'pagada' || factura.estado === 'enviada_dian') && (
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleRegistrarPago(factura)}
                            >
                              <CreditCard className="w-4 h-4 mr-2" />
                              {factura.estado === 'pagada' ? 'Ver Pago' : 'Registrar Pago'}
                            </Button>
                          )}

                          {factura.estado_dian === 'rechazada' && (
                            <Button
                              size="sm"
                              className="bg-orange-600 hover:bg-orange-700"
                              onClick={() => handleEnviarRecordatorio(factura)}
                            >
                              <AlertCircle className="w-4 h-4 mr-2" />
                              Revisar Rechazo
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </>
        )}

        {/* Pestaña de Reportes */}
        {activeTab === 'reportes' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  Reporte de Ventas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Análisis detallado de ingresos por período, servicios más facturados y tendencias.
                </p>
                <Button className="w-full" onClick={() => handleGenerarReporte('ventas')}>
                  <Download className="w-4 h-4 mr-2" />
                  Generar Reporte
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-yellow-600" />
                  Cartera Vencida
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Listado de facturas vencidas, análisis de morosidad y gestión de cobro.
                </p>
                <Button className="w-full" variant="outline" onClick={() => handleGenerarReporte('cartera')}>
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Ver Cartera
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="w-5 h-5 text-green-600" />
                  Reporte Fiscal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Información fiscal, impuestos recaudados y reportes para declaraciones.
                </p>
                <Button className="w-full" variant="outline" onClick={() => handleGenerarReporte('fiscal')}>
                  <FileText className="w-4 h-4 mr-2" />
                  Generar Fiscal
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5 text-purple-600" />
                  Por Paciente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Historial de facturación por paciente, análisis de frecuencia y valores.
                </p>
                <Button className="w-full" variant="outline" onClick={() => handleGenerarReporte('paciente')}>
                  <Search className="w-4 h-4 mr-2" />
                  Buscar Paciente
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="w-5 h-5 text-indigo-600" />
                  Por Servicio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Análisis de rentabilidad por servicio, códigos CUPS más utilizados.
                </p>
                <Button className="w-full" variant="outline" onClick={() => handleGenerarReporte('servicio')}>
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Ver Análisis
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5 text-orange-600" />
                  Métodos de Pago
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Distribución de pagos por método, análisis de preferencias de pago.
                </p>
                <Button className="w-full" variant="outline" onClick={() => handleGenerarReporte('metodos-pago')}>
                  <DollarSign className="w-4 h-4 mr-2" />
                  Ver Distribución
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Pestaña de Configuración */}
        {activeTab === 'configuracion' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="w-5 h-5" />
                  Configuración de Impuestos
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">IVA (%)</label>
                  <input
                    type="number"
                    defaultValue="19"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Retención en la Fuente (%)</label>
                  <input
                    type="number"
                    defaultValue="2.5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ReteICA (%)</label>
                  <input
                    type="number"
                    defaultValue="0.414"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <Button className="w-full" onClick={() => handleGuardarConfiguracion('impuestos')}>
                  Guardar Configuración
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Numeración de Facturas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Prefijo</label>
                  <input
                    type="text"
                    defaultValue="FAC"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Siguiente Número</label>
                  <input
                    type="number"
                    defaultValue="1001"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Formato</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>FAC-YYYY-NNNN</option>
                    <option>FAC-NNNN</option>
                    <option>YYYY-FAC-NNNN</option>
                  </select>
                </div>
                <Button className="w-full" onClick={() => handleGuardarConfiguracion('numeración')}>
                  Actualizar Numeración
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Notificaciones
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Envío automático por email</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Recordatorios de vencimiento</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Notificaciones de pago</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Días antes del vencimiento</label>
                  <input
                    type="number"
                    defaultValue="7"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <Button className="w-full" onClick={() => handleGuardarConfiguracion('notificaciones')}>
                  Guardar Preferencias
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  Datos de la Empresa
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Razón Social</label>
                  <input
                    type="text"
                    defaultValue="Hospital Hipócrates S.A.S."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">NIT</label>
                  <input
                    type="text"
                    defaultValue="900.123.456-7"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Dirección</label>
                  <input
                    type="text"
                    defaultValue="Calle 123 #45-67, Bogotá"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <Button className="w-full" onClick={() => handleGuardarConfiguracion('datos empresa')}>
                  Actualizar Datos
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default Facturacion;
