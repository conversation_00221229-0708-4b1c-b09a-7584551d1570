import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

import { useAuth } from '../../context/AuthContext';
import gestionIpsService from '../../services/gestionIpsService';
import { SuscripcionIps, FiltrosIps } from '../../types/gestionIps';
import { 
  Building2, 
  CreditCard, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Power,
  PowerOff,
  Calendar,
  DollarSign
} from 'lucide-react';

const GestionIps: React.FC = () => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'SUPER_ADMIN';

  // Estados
  const [activeTab, setActiveTab] = useState<'dashboard' | 'ips' | 'planes' | 'estadisticas'>(
    isAdmin ? 'ips' : 'dashboard'
  );
  const [filtros, setFiltros] = useState<FiltrosIps>({
    page: 1,
    limit: 20
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Queries
  const { data: suscripciones, isLoading: loadingSuscripciones } = useQuery({
    queryKey: ['gestion-ips-suscripciones', filtros],
    queryFn: () => {
      console.log('🔍 Cargando suscripciones con filtros:', filtros);
      return gestionIpsService.getSuscripciones(filtros);
    },
    enabled: activeTab === 'ips' && isAdmin
  });

  // Debug logs
  console.log('🎯 GestionIps Debug:', {
    isAdmin,
    activeTab,
    suscripciones: suscripciones?.data?.length || 0,
    loadingSuscripciones
  });

  // Efecto para actualizar la tab cuando cambie el rol
  useEffect(() => {
    setActiveTab(isAdmin ? 'ips' : 'dashboard');
  }, [isAdmin]);

  const { data: estadisticas, isLoading: loadingEstadisticas } = useQuery({
    queryKey: ['gestion-ips-estadisticas'],
    queryFn: () => gestionIpsService.getEstadisticasGenerales(),
    enabled: activeTab === 'estadisticas' && isAdmin
  });

  const { data: dashboardData, isLoading: loadingDashboard } = useQuery({
    queryKey: ['gestion-ips-dashboard', user?.hospital_id],
    queryFn: () => gestionIpsService.getDashboardIps(user?.hospital_id || 1),
    enabled: activeTab === 'dashboard' && !isAdmin
  });

  const { data: planes, isLoading: loadingPlanes } = useQuery({
    queryKey: ['gestion-ips-planes'],
    queryFn: () => gestionIpsService.getPlanes(),
    enabled: activeTab === 'planes'
  });

  // Handlers
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFiltros(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key: keyof FiltrosIps, value: any) => {
    setFiltros(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const clearFilters = () => {
    setFiltros({ page: 1, limit: 20 });
    setSearchTerm('');
  };

  const handleToggleEstadoIps = async (id: string, estadoActual: string) => {
    const nuevoEstado = estadoActual === 'ACTIVA' ? 'SUSPENDIDA' : 'ACTIVA';
    try {
      await gestionIpsService.toggleEstadoIps(id, nuevoEstado);
      // Refetch data
    } catch (error) {
      console.error('Error al cambiar estado de IPS:', error);
    }
  };

  const getEstadoBadge = (estado: SuscripcionIps['estado']) => {
    const badges = {
      'ACTIVA': 'bg-green-100 text-green-800 border-green-200',
      'SUSPENDIDA': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'VENCIDA': 'bg-red-100 text-red-800 border-red-200',
      'CANCELADA': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    
    return badges[estado] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getDiasRestantesColor = (dias: number) => {
    if (dias <= 7) return 'text-red-600';
    if (dias <= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {isAdmin ? 'Gestión de IPS' : 'Mi Suscripción'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isAdmin 
                ? 'Administración completa de suscripciones y pagos'
                : 'Gestión de su suscripción y métodos de pago'
              }
            </p>
          </div>
          {isAdmin && (
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <Plus className="h-4 w-4" />
              Nueva IPS
            </button>
          )}
        </div>

        {/* Estadísticas rápidas */}
        {isAdmin ? (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total IPS</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {estadisticas?.total_ips || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">IPS Activas</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {estadisticas?.ips_activas || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Por Vencer</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {estadisticas?.ips_por_vencer || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Ingresos Mes</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(estadisticas?.ingresos_mes_actual || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Dashboard para IPS individual
          dashboardData && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Estado Suscripción</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getEstadoBadge(dashboardData.suscripcion_actual.estado)}`}>
                      {dashboardData.suscripcion_actual.estado}
                    </span>
                  </div>
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Días Restantes</p>
                    <p className={`text-2xl font-bold ${getDiasRestantesColor(dashboardData.suscripcion_actual.dias_restantes || 0)}`}>
                      {dashboardData.suscripcion_actual.dias_restantes || 0}
                    </p>
                  </div>
                  <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <Calendar className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Plan Actual</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {dashboardData.suscripcion_actual.plan?.nombre}
                    </p>
                  </div>
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </div>
            </div>
          )
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {(isAdmin ? [
              { id: 'ips', label: 'Gestión de IPS', icon: Building2 },
              { id: 'planes', label: 'Planes', icon: CreditCard },
              { id: 'estadisticas', label: 'Estadísticas', icon: TrendingUp }
            ] : [
              { id: 'dashboard', label: 'Mi Dashboard', icon: Building2 },
              { id: 'planes', label: 'Planes Disponibles', icon: CreditCard }
            ]).map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenido de las tabs */}
        {activeTab === 'ips' && isAdmin && (
          <div className="space-y-6">
            {/* Filtros y búsqueda */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Búsqueda */}
                <form onSubmit={handleSearch} className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Buscar por nombre de hospital, NIT o email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </form>

                {/* Botones de acción */}
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <Filter className="h-4 w-4" />
                    Filtros
                  </button>
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                  >
                    Limpiar
                  </button>
                </div>
              </div>

              {/* Filtros expandidos */}
              {showFilters && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Estado
                      </label>
                      <select
                        value={filtros.estado || ''}
                        onChange={(e) => handleFilterChange('estado', e.target.value || undefined)}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Todos los estados</option>
                        <option value="ACTIVA">Activa</option>
                        <option value="SUSPENDIDA">Suspendida</option>
                        <option value="VENCIDA">Vencida</option>
                        <option value="CANCELADA">Cancelada</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Plan
                      </label>
                      <select
                        value={filtros.plan_id || ''}
                        onChange={(e) => handleFilterChange('plan_id', e.target.value ? parseInt(e.target.value) : undefined)}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Todos los planes</option>
                        {planes?.map(plan => (
                          <option key={plan.id} value={plan.id}>{plan.nombre}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Vencimiento
                      </label>
                      <select
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">Todos</option>
                        <option value="7">Próximos 7 días</option>
                        <option value="30">Próximos 30 días</option>
                        <option value="90">Próximos 90 días</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Tabla de IPS */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              {loadingSuscripciones ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600 dark:text-gray-400">Cargando IPS...</p>
                </div>
              ) : !suscripciones?.data || suscripciones.data.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-600 dark:text-gray-400">
                    No se encontraron suscripciones.
                    {suscripciones ? ` (Total: ${suscripciones.total})` : ' (Sin datos)'}
                  </p>
                  <div className="mt-2 text-xs text-gray-500">
                    Debug: activeTab={activeTab}, isAdmin={isAdmin.toString()}
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Hospital
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Estado
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Vencimiento
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Valor
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Acciones
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {suscripciones?.data.map((suscripcion) => (
                        <tr key={suscripcion.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {suscripcion.hospital?.nombre || 'Hospital'}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              NIT: {suscripcion.hospital?.nit || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {suscripcion.plan?.nombre || 'Plan'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getEstadoBadge(suscripcion.estado)}`}>
                              {suscripcion.estado}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {new Date(suscripcion.fecha_fin).toLocaleDateString()}
                            </div>
                            <div className={`text-sm ${getDiasRestantesColor(suscripcion.dias_restantes || 0)}`}>
                              {suscripcion.dias_restantes} días restantes
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {formatCurrency(suscripcion.valor_total)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end gap-2">
                              <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                <Eye className="h-4 w-4" />
                              </button>
                              <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                                <Edit className="h-4 w-4" />
                              </button>
                              <button 
                                onClick={() => handleToggleEstadoIps(suscripcion.id, suscripcion.estado)}
                                className={`${suscripcion.estado === 'ACTIVA' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                              >
                                {suscripcion.estado === 'ACTIVA' ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Placeholder para otras tabs */}
        {activeTab === 'planes' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {planes?.map(plan => (
              <div key={plan.id} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="text-center">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {plan.nombre}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {plan.descripcion}
                  </p>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-4">
                    {formatCurrency(plan.precio_mensual)}
                    <span className="text-sm text-gray-500">/mes</span>
                  </div>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2 mb-6">
                    <li>Hasta {plan.max_usuarios} usuarios</li>
                    <li>Hasta {plan.max_pacientes.toLocaleString()} pacientes</li>
                    <li>{plan.max_storage_gb} GB de almacenamiento</li>
                  </ul>
                  {!isAdmin && (
                    <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                      Seleccionar Plan
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'dashboard' && !isAdmin && dashboardData && (
          <div className="space-y-6">
            {/* Uso de recursos */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Uso de Recursos</h3>
              <div className="space-y-4">
                {Object.entries(dashboardData.uso_recursos).map(([key, value]) => (
                  <div key={key}>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 capitalize">{key}</span>
                      <span className="text-gray-900 dark:text-white">
                        {value.actual.toLocaleString()} / {value.maximo.toLocaleString()}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${value.porcentaje > 80 ? 'bg-red-500' : value.porcentaje > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                        style={{ width: `${value.porcentaje}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Notificaciones pendientes */}
            {dashboardData.notificaciones_pendientes.length > 0 && (
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notificaciones Pendientes</h3>
                <div className="space-y-3">
                  {dashboardData.notificaciones_pendientes.map(notif => (
                    <div key={notif.id} className={`p-3 rounded-lg border-l-4 ${
                      notif.prioridad === 'CRITICA' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                      notif.prioridad === 'ALTA' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                      'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    }`}>
                      <h4 className="font-medium text-gray-900 dark:text-white">{notif.titulo}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{notif.mensaje}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'estadisticas' && isAdmin && (
          <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 text-center">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Estadísticas Avanzadas
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Análisis detallado de ingresos, crecimiento y métricas - En desarrollo
            </p>
          </div>
        )}
      </div>
  );
};

export default GestionIps;
