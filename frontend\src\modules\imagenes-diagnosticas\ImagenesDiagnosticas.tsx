import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  FileText,
  Calendar,
  User,
  Building,
  Download,
  Upload,
  Camera,
  Monitor,
  Zap,
  Activity,
  Heart,
  Brain,
  Bone,
  Stethoscope,
  Microscope,
  Scan,
  Image as ImageIcon,
  Play,
  Pause,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move,
  Ruler,
  Palette
} from 'lucide-react';

// Interfaces
interface ImagenDiagnostica {
  id: string;
  numero_estudio: string;
  fecha_estudio: string;
  hora_estudio: string;
  paciente: {
    id: string;
    nombres: string;
    apellidos: string;
    documento: string;
    edad: number;
    genero: string;
  };
  medico_solicitante: {
    id: string;
    nombres: string;
    apellidos: string;
    especialidad: string;
  };
  tipo_estudio: string;
  modalidad: 'RX' | 'TAC' | 'RMN' | 'ECO' | 'MAMOGRAFIA' | 'FLUOROSCOPIA' | 'MEDICINA_NUCLEAR';
  equipo_utilizado: string;
  region_anatomica: string;
  indicacion_clinica: string;
  tecnica_utilizada: string;
  contraste: boolean;
  tipo_contraste?: string;
  dosis_radiacion?: number;
  imagenes: Array<{
    id: string;
    url: string;
    tipo: 'DICOM' | 'JPG' | 'PNG';
    tamaño: string;
    resolucion: string;
    serie: string;
    corte?: number;
  }>;
  hallazgos: string;
  conclusion: string;
  recomendaciones: string;
  estado: 'programado' | 'en_proceso' | 'completado' | 'reportado' | 'entregado';
  prioridad: 'rutina' | 'urgente' | 'emergencia';
  radiologo: {
    id: string;
    nombres: string;
    apellidos: string;
    registro_profesional: string;
  };
  fecha_reporte?: string;
  fecha_entrega?: string;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

interface ResumenImagenes {
  total_estudios: number;
  estudios_pendientes: number;
  estudios_completados: number;
  estudios_urgentes: number;
  tiempo_promedio_reporte: number;
  equipos_activos: number;
  estudios_hoy: number;
}

export const ImagenesDiagnosticas: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('estudios');
  
  // Estados de datos
  const [estudios, setEstudios] = useState<ImagenDiagnostica[]>([]);
  const [resumen, setResumen] = useState<ResumenImagenes | null>(null);
  const [cargando, setCargando] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Estados de UI
  const [filtros, setFiltros] = useState({
    busqueda: '',
    modalidad: '',
    estado: '',
    fecha_desde: '',
    fecha_hasta: '',
    prioridad: ''
  });
  const [mostrarFiltros, setMostrarFiltros] = useState(false);
  const [estudioSeleccionado, setEstudioSeleccionado] = useState<ImagenDiagnostica | null>(null);
  const [mostrarVisor, setMostrarVisor] = useState(false);

  // Datos mock para demostración
  const estudiosMock: ImagenDiagnostica[] = [
    {
      id: 'img-001',
      numero_estudio: 'RX-2024-001',
      fecha_estudio: '2024-01-15',
      hora_estudio: '10:30',
      paciente: {
        id: 'pac-001',
        nombres: 'María Elena',
        apellidos: 'González López',
        documento: '12345678',
        edad: 45,
        genero: 'F'
      },
      medico_solicitante: {
        id: 'med-001',
        nombres: 'Dr. Carlos',
        apellidos: 'Rodríguez',
        especialidad: 'Medicina Interna'
      },
      tipo_estudio: 'Radiografía de Tórax PA y Lateral',
      modalidad: 'RX',
      equipo_utilizado: 'Equipo RX Digital Philips DR-1',
      region_anatomica: 'Tórax',
      indicacion_clinica: 'Tos persistente, descartar neumonía',
      tecnica_utilizada: 'Proyecciones PA y lateral en bipedestación',
      contraste: false,
      dosis_radiacion: 0.1,
      imagenes: [
        {
          id: 'img-001-1',
          url: '/imagenes/rx-torax-pa.dcm',
          tipo: 'DICOM',
          tamaño: '2.5 MB',
          resolucion: '2048x2048',
          serie: 'Serie 1'
        },
        {
          id: 'img-001-2',
          url: '/imagenes/rx-torax-lat.dcm',
          tipo: 'DICOM',
          tamaño: '2.3 MB',
          resolucion: '2048x2048',
          serie: 'Serie 2'
        }
      ],
      hallazgos: 'Campos pulmonares con adecuada expansión bilateral. No se observan infiltrados ni consolidaciones. Silueta cardíaca de tamaño normal.',
      conclusion: 'Radiografía de tórax normal',
      recomendaciones: 'Control clínico. No requiere seguimiento radiológico inmediato.',
      estado: 'reportado',
      prioridad: 'rutina',
      radiologo: {
        id: 'rad-001',
        nombres: 'Dra. Ana',
        apellidos: 'Martínez',
        registro_profesional: 'RM-12345'
      },
      fecha_reporte: '2024-01-15',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T14:20:00Z'
    },
    {
      id: 'img-002',
      numero_estudio: 'TAC-2024-002',
      fecha_estudio: '2024-01-16',
      hora_estudio: '14:00',
      paciente: {
        id: 'pac-002',
        nombres: 'José Luis',
        apellidos: 'Pérez Silva',
        documento: '87654321',
        edad: 62,
        genero: 'M'
      },
      medico_solicitante: {
        id: 'med-002',
        nombres: 'Dr. Luis',
        apellidos: 'García',
        especialidad: 'Neurología'
      },
      tipo_estudio: 'TAC de Cráneo Simple y Contrastado',
      modalidad: 'TAC',
      equipo_utilizado: 'TAC Siemens Somatom 64 cortes',
      region_anatomica: 'Cráneo',
      indicacion_clinica: 'Cefalea intensa, descartar lesión intracraneal',
      tecnica_utilizada: 'Cortes axiales de 1mm, reconstrucciones multiplanares',
      contraste: true,
      tipo_contraste: 'Iodado no iónico 100ml',
      dosis_radiacion: 2.5,
      imagenes: [
        {
          id: 'img-002-1',
          url: '/imagenes/tac-craneo-simple.dcm',
          tipo: 'DICOM',
          tamaño: '45.2 MB',
          resolucion: '512x512',
          serie: 'Serie Simple',
          corte: 120
        },
        {
          id: 'img-002-2',
          url: '/imagenes/tac-craneo-contraste.dcm',
          tipo: 'DICOM',
          tamaño: '48.7 MB',
          resolucion: '512x512',
          serie: 'Serie Contrastada',
          corte: 120
        }
      ],
      hallazgos: 'Estructuras de línea media centradas. No se observan lesiones ocupantes de espacio. Sistema ventricular de tamaño normal.',
      conclusion: 'TAC de cráneo normal',
      recomendaciones: 'Correlación clínica. Control según evolución.',
      estado: 'completado',
      prioridad: 'urgente',
      radiologo: {
        id: 'rad-002',
        nombres: 'Dr. Miguel',
        apellidos: 'Torres',
        registro_profesional: 'RM-67890'
      },
      fecha_reporte: '2024-01-16',
      created_at: '2024-01-16T14:00:00Z',
      updated_at: '2024-01-16T16:45:00Z'
    }
  ];

  const resumenMock: ResumenImagenes = {
    total_estudios: 1250,
    estudios_pendientes: 45,
    estudios_completados: 1180,
    estudios_urgentes: 12,
    tiempo_promedio_reporte: 2.5,
    equipos_activos: 8,
    estudios_hoy: 25
  };

  // Cargar datos
  useEffect(() => {
    const cargarDatos = async () => {
      setCargando(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setEstudios(estudiosMock);
        setResumen(resumenMock);
      } catch (error) {
        setError('Error al cargar datos de imágenes diagnósticas');
      } finally {
        setCargando(false);
      }
    };

    cargarDatos();
  }, []);

  // Filtrar estudios
  const estudiosFiltrados = estudios.filter(estudio => {
    const cumpleBusqueda = !filtros.busqueda || 
      estudio.numero_estudio.toLowerCase().includes(filtros.busqueda.toLowerCase()) ||
      `${estudio.paciente.nombres} ${estudio.paciente.apellidos}`.toLowerCase().includes(filtros.busqueda.toLowerCase()) ||
      estudio.paciente.documento.includes(filtros.busqueda);
    
    const cumpleModalidad = !filtros.modalidad || estudio.modalidad === filtros.modalidad;
    const cumpleEstado = !filtros.estado || estudio.estado === filtros.estado;
    const cumplePrioridad = !filtros.prioridad || estudio.prioridad === filtros.prioridad;
    
    return cumpleBusqueda && cumpleModalidad && cumpleEstado && cumplePrioridad;
  });

  const obtenerColorEstado = (estado: string) => {
    switch (estado) {
      case 'completado':
        return 'bg-green-100 text-green-800';
      case 'reportado':
        return 'bg-blue-100 text-blue-800';
      case 'en_proceso':
        return 'bg-yellow-100 text-yellow-800';
      case 'programado':
        return 'bg-gray-100 text-gray-800';
      case 'entregado':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const obtenerColorPrioridad = (prioridad: string) => {
    switch (prioridad) {
      case 'emergencia':
        return 'bg-red-100 text-red-800';
      case 'urgente':
        return 'bg-orange-100 text-orange-800';
      case 'rutina':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const obtenerIconoModalidad = (modalidad: string) => {
    switch (modalidad) {
      case 'RX':
        return <Bone className="w-5 h-5" />;
      case 'TAC':
        return <Scan className="w-5 h-5" />;
      case 'RMN':
        return <Brain className="w-5 h-5" />;
      case 'ECO':
        return <Activity className="w-5 h-5" />;
      case 'MAMOGRAFIA':
        return <Heart className="w-5 h-5" />;
      default:
        return <ImageIcon className="w-5 h-5" />;
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleNuevoEstudio = () => {
    alert('🏥 NUEVO ESTUDIO DE IMÁGENES\n\nSe abrirá el formulario para programar un nuevo estudio de imágenes diagnósticas.\n\nIncluye:\n• Datos del paciente\n• Tipo de estudio\n• Modalidad de imagen\n• Indicación clínica\n• Programación de cita');
  };

  const handleVerEstudio = (estudio: ImagenDiagnostica) => {
    setEstudioSeleccionado(estudio);
    alert(`📋 DETALLES DEL ESTUDIO\n\nEstudio: ${estudio.numero_estudio}\nPaciente: ${estudio.paciente.nombres} ${estudio.paciente.apellidos}\nTipo: ${estudio.tipo_estudio}\nEstado: ${estudio.estado}\n\nSe abrirá la vista detallada del estudio.`);
  };

  const handleVerImagenes = (estudio: ImagenDiagnostica) => {
    setEstudioSeleccionado(estudio);
    setMostrarVisor(true);
    alert(`🖼️ VISOR DE IMÁGENES DICOM\n\nEstudio: ${estudio.numero_estudio}\nImágenes: ${estudio.imagenes.length}\nModalidad: ${estudio.modalidad}\n\nSe abrirá el visor DICOM con herramientas de:\n• Zoom y navegación\n• Mediciones\n• Ajuste de ventana\n• Anotaciones`);
  };

  if (cargando) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando imágenes diagnósticas...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-red-50 border border-red-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-semibold text-red-900 mb-2">Error al cargar datos</h3>
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Reintentar
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <ImageIcon className="w-8 h-8 text-blue-600" />
              Imágenes Diagnósticas
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral de estudios radiológicos y diagnóstico por imágenes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleNuevoEstudio}>
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Estudio
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        {resumen && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Estudios</p>
                    <p className="text-2xl font-bold text-blue-700">{resumen.total_estudios}</p>
                    <div className="flex items-center mt-2">
                      <ImageIcon className="w-4 h-4 text-blue-600 mr-1" />
                      <span className="text-sm text-blue-600">Este mes</span>
                    </div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                    <ImageIcon className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pendientes</p>
                    <p className="text-2xl font-bold text-yellow-700">{resumen.estudios_pendientes}</p>
                    <div className="flex items-center mt-2">
                      <Calendar className="w-4 h-4 text-yellow-600 mr-1" />
                      <span className="text-sm text-yellow-600">Por reportar</span>
                    </div>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                    <Calendar className="w-8 h-8 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Urgentes</p>
                    <p className="text-2xl font-bold text-red-700">{resumen.estudios_urgentes}</p>
                    <div className="flex items-center mt-2">
                      <Zap className="w-4 h-4 text-red-600 mr-1" />
                      <span className="text-sm text-red-600">Requieren atención</span>
                    </div>
                  </div>
                  <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                    <Zap className="w-8 h-8 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Equipos Activos</p>
                    <p className="text-2xl font-bold text-green-700">{resumen.equipos_activos}</p>
                    <div className="flex items-center mt-2">
                      <Monitor className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">Operativos</span>
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                    <Monitor className="w-8 h-8 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeTab === 'estudios' ? 'default' : 'outline'}
                onClick={() => setActiveTab('estudios')}
                className="flex items-center gap-2"
              >
                <ImageIcon className="w-4 h-4" />
                Estudios
              </Button>
              <Button
                variant={activeTab === 'equipos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('equipos')}
                className="flex items-center gap-2"
              >
                <Monitor className="w-4 h-4" />
                Equipos
              </Button>
              <Button
                variant={activeTab === 'reportes' ? 'default' : 'outline'}
                onClick={() => setActiveTab('reportes')}
                className="flex items-center gap-2"
              >
                <FileText className="w-4 h-4" />
                Reportes
              </Button>
              <Button
                variant={activeTab === 'agenda' ? 'default' : 'outline'}
                onClick={() => setActiveTab('agenda')}
                className="flex items-center gap-2"
              >
                <Calendar className="w-4 h-4" />
                Agenda
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'estudios' && (
          <>
            {/* Filtros y búsqueda */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por número de estudio, paciente o documento..."
                        className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={filtros.busqueda}
                        onChange={(e) => setFiltros({ ...filtros, busqueda: e.target.value })}
                      />
                    </div>
                    <Button
                      variant={mostrarFiltros ? 'default' : 'outline'}
                      onClick={() => setMostrarFiltros(!mostrarFiltros)}
                    >
                      <Filter className="w-4 h-4 mr-2" />
                      Filtros
                    </Button>
                  </div>

                  {mostrarFiltros && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Modalidad</label>
                        <select
                          value={filtros.modalidad}
                          onChange={(e) => setFiltros({ ...filtros, modalidad: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Todas las modalidades</option>
                          <option value="RX">Radiografía (RX)</option>
                          <option value="TAC">Tomografía (TAC)</option>
                          <option value="RMN">Resonancia (RMN)</option>
                          <option value="ECO">Ecografía (ECO)</option>
                          <option value="MAMOGRAFIA">Mamografía</option>
                          <option value="FLUOROSCOPIA">Fluoroscopía</option>
                          <option value="MEDICINA_NUCLEAR">Medicina Nuclear</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Estado</label>
                        <select
                          value={filtros.estado}
                          onChange={(e) => setFiltros({ ...filtros, estado: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Todos los estados</option>
                          <option value="programado">Programado</option>
                          <option value="en_proceso">En Proceso</option>
                          <option value="completado">Completado</option>
                          <option value="reportado">Reportado</option>
                          <option value="entregado">Entregado</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Prioridad</label>
                        <select
                          value={filtros.prioridad}
                          onChange={(e) => setFiltros({ ...filtros, prioridad: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Todas las prioridades</option>
                          <option value="rutina">Rutina</option>
                          <option value="urgente">Urgente</option>
                          <option value="emergencia">Emergencia</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Fecha</label>
                        <input
                          type="date"
                          value={filtros.fecha_desde}
                          onChange={(e) => setFiltros({ ...filtros, fecha_desde: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Lista de estudios */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Estudios ({estudiosFiltrados.length})
                </h3>
              </div>

              {estudiosFiltrados.length === 0 ? (
                <Card className="bg-gray-50 border border-gray-200">
                  <CardContent className="p-8 text-center">
                    <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No se encontraron estudios</h3>
                    <p className="text-gray-600 mb-4">
                      No hay estudios que coincidan con los filtros aplicados.
                    </p>
                    <Button onClick={() => setFiltros({
                      busqueda: '',
                      modalidad: '',
                      estado: '',
                      fecha_desde: '',
                      fecha_hasta: '',
                      prioridad: ''
                    })}>
                      Limpiar filtros
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                estudiosFiltrados.map((estudio) => (
                  <Card key={estudio.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            {obtenerIconoModalidad(estudio.modalidad)}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="text-lg font-semibold text-gray-900">{estudio.numero_estudio}</h4>
                              <Badge className={obtenerColorEstado(estudio.estado)}>
                                <span className="capitalize">{estudio.estado.replace('_', ' ')}</span>
                              </Badge>
                              <Badge className={obtenerColorPrioridad(estudio.prioridad)}>
                                <span className="capitalize">{estudio.prioridad}</span>
                              </Badge>
                              <Badge className="bg-purple-100 text-purple-800">
                                {estudio.modalidad}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-600">Paciente</p>
                                <p className="font-medium text-gray-900">
                                  {estudio.paciente.nombres} {estudio.paciente.apellidos}
                                </p>
                                <p className="text-sm text-gray-600">
                                  {estudio.paciente.documento} - {estudio.paciente.edad} años
                                </p>
                              </div>

                              <div>
                                <p className="text-sm text-gray-600">Estudio</p>
                                <p className="font-medium text-gray-900">{estudio.tipo_estudio}</p>
                                <p className="text-sm text-gray-600">
                                  {formatearFecha(estudio.fecha_estudio)} - {estudio.hora_estudio}
                                </p>
                              </div>

                              <div>
                                <p className="text-sm text-gray-600">Médico Solicitante</p>
                                <p className="font-medium text-gray-900">
                                  {estudio.medico_solicitante.nombres} {estudio.medico_solicitante.apellidos}
                                </p>
                                <p className="text-sm text-gray-600">{estudio.medico_solicitante.especialidad}</p>
                              </div>
                            </div>

                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-sm text-gray-600 mb-1">Indicación Clínica:</p>
                              <p className="text-sm text-gray-900">{estudio.indicacion_clinica}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleVerEstudio(estudio)}
                              title="Ver detalles"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleVerImagenes(estudio)}
                              title="Ver imágenes"
                              className="text-blue-600 border-blue-200 hover:bg-blue-50"
                            >
                              <ImageIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              title="Descargar"
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>

                          {estudio.estado === 'completado' && !estudio.fecha_reporte && (
                            <Button
                              size="sm"
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <FileText className="w-4 h-4 mr-2" />
                              Generar Reporte
                            </Button>
                          )}

                          {estudio.estado === 'reportado' && (
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <Download className="w-4 h-4 mr-2" />
                              Descargar Reporte
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </>
        )}

        {/* Otras pestañas */}
        {activeTab === 'equipos' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <Monitor className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Gestión de Equipos</h3>
              <p className="text-gray-600 mb-4">
                Administración y monitoreo de equipos de diagnóstico por imágenes
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Agregar Equipo
              </Button>
            </CardContent>
          </Card>
        )}

        {activeTab === 'reportes' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  Productividad
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Análisis de productividad por modalidad, equipo y radiólogo.
                </p>
                <Button className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Generar Reporte
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-green-600" />
                  Tiempos de Respuesta
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Análisis de tiempos desde solicitud hasta entrega de resultados.
                </p>
                <Button className="w-full" variant="outline">
                  <Calendar className="w-4 h-4 mr-2" />
                  Ver Métricas
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-orange-600" />
                  Calidad de Imagen
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Control de calidad y estadísticas de repetición de estudios.
                </p>
                <Button className="w-full" variant="outline">
                  <Monitor className="w-4 h-4 mr-2" />
                  Ver Estadísticas
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'agenda' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-8 text-center">
              <Calendar className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Agenda de Estudios</h3>
              <p className="text-gray-600 mb-4">
                Programación y gestión de citas para estudios de imágenes diagnósticas
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Programar Estudio
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ImagenesDiagnosticas;
