import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  DollarSign, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  BarChart3,
  PieChart,
  Target,
  AlertCircle,
  CheckCircle,
  Clock,
  Building2,
  Calculator,
  Percent
} from 'lucide-react';

// Interfaces de datos
interface Presupuesto {
  id: number;
  nombre: string;
  descripcion: string;
  año: number;
  periodo: 'Anual' | 'Semestral' | 'Trimestral' | 'Mensual';
  estado: 'Borrador' | 'Aprobado' | 'En_Ejecucion' | 'Cerrado' | 'Cancelado';
  monto_total: number;
  monto_ejecutado: number;
  fecha_inicio: string;
  fecha_fin: string;
  responsable: string;
  departamento: string;
  categorias: CategoriaPresupuesto[];
}

interface CategoriaPresupuesto {
  id: number;
  nombre: string;
  monto_asignado: number;
  monto_ejecutado: number;
  porcentaje_ejecucion: number;
  subcategorias: SubcategoriaPresupuesto[];
}

interface SubcategoriaPresupuesto {
  id: number;
  nombre: string;
  monto_asignado: number;
  monto_ejecutado: number;
  descripcion: string;
}

const PresupuestoModerno: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('presupuestos');
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroAño, setFiltroAño] = useState(new Date().getFullYear());
  const [filtroEstado, setFiltroEstado] = useState('todos');
  const [filtroDepartamento, setFiltroDepartamento] = useState('todos');

  // Datos de ejemplo para desarrollo
  const presupuestos: Presupuesto[] = [
    {
      id: 1,
      nombre: 'Presupuesto Anual 2024',
      descripcion: 'Presupuesto general del hospital para el año 2024',
      año: 2024,
      periodo: 'Anual',
      estado: 'En_Ejecucion',
      monto_total: 15000000000,
      monto_ejecutado: 8500000000,
      fecha_inicio: '2024-01-01',
      fecha_fin: '2024-12-31',
      responsable: 'Director Financiero',
      departamento: 'Administración',
      categorias: [
        {
          id: 1,
          nombre: 'Personal Médico',
          monto_asignado: 8000000000,
          monto_ejecutado: 4800000000,
          porcentaje_ejecucion: 60,
          subcategorias: []
        },
        {
          id: 2,
          nombre: 'Equipos Médicos',
          monto_asignado: 3000000000,
          monto_ejecutado: 1800000000,
          porcentaje_ejecucion: 60,
          subcategorias: []
        },
        {
          id: 3,
          nombre: 'Medicamentos',
          monto_asignado: 2500000000,
          monto_ejecutado: 1200000000,
          porcentaje_ejecucion: 48,
          subcategorias: []
        },
        {
          id: 4,
          nombre: 'Infraestructura',
          monto_asignado: 1500000000,
          monto_ejecutado: 700000000,
          porcentaje_ejecucion: 47,
          subcategorias: []
        }
      ]
    },
    {
      id: 2,
      nombre: 'Presupuesto Q1 2024',
      descripcion: 'Presupuesto del primer trimestre 2024',
      año: 2024,
      periodo: 'Trimestral',
      estado: 'Cerrado',
      monto_total: 3750000000,
      monto_ejecutado: 3600000000,
      fecha_inicio: '2024-01-01',
      fecha_fin: '2024-03-31',
      responsable: 'Coordinador Financiero',
      departamento: 'Finanzas',
      categorias: []
    },
    {
      id: 3,
      nombre: 'Presupuesto Emergencias',
      descripcion: 'Presupuesto especial para situaciones de emergencia',
      año: 2024,
      periodo: 'Anual',
      estado: 'Aprobado',
      monto_total: 2000000000,
      monto_ejecutado: 500000000,
      fecha_inicio: '2024-01-01',
      fecha_fin: '2024-12-31',
      responsable: 'Director Médico',
      departamento: 'Urgencias',
      categorias: []
    }
  ];

  const departamentos = [
    'Administración',
    'Finanzas',
    'Urgencias',
    'Cirugía',
    'Medicina Interna',
    'Pediatría',
    'Ginecología',
    'Laboratorio',
    'Radiología'
  ];

  // Filtrar presupuestos
  const filteredPresupuestos = presupuestos.filter(presupuesto => {
    const matchesSearch = 
      presupuesto.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      presupuesto.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
      presupuesto.responsable.toLowerCase().includes(searchTerm.toLowerCase()) ||
      presupuesto.departamento.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesAño = presupuesto.año === filtroAño;
    const matchesEstado = filtroEstado === 'todos' || presupuesto.estado === filtroEstado;
    const matchesDepartamento = filtroDepartamento === 'todos' || presupuesto.departamento === filtroDepartamento;
    
    return matchesSearch && matchesAño && matchesEstado && matchesDepartamento;
  });

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Borrador':
        return { color: 'bg-gray-100 text-gray-800', label: 'Borrador', icon: <FileText className="w-3 h-3" /> };
      case 'Aprobado':
        return { color: 'bg-green-100 text-green-800', label: 'Aprobado', icon: <CheckCircle className="w-3 h-3" /> };
      case 'En_Ejecucion':
        return { color: 'bg-blue-100 text-blue-800', label: 'En Ejecución', icon: <Clock className="w-3 h-3" /> };
      case 'Cerrado':
        return { color: 'bg-purple-100 text-purple-800', label: 'Cerrado', icon: <Target className="w-3 h-3" /> };
      case 'Cancelado':
        return { color: 'bg-red-100 text-red-800', label: 'Cancelado', icon: <AlertCircle className="w-3 h-3" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado, icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  // Formatear moneda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Calcular porcentaje de ejecución
  const calcularPorcentajeEjecucion = (ejecutado: number, total: number) => {
    return total > 0 ? Math.round((ejecutado / total) * 100) : 0;
  };

  // Calcular estadísticas
  const stats = {
    total_presupuestos: filteredPresupuestos.length,
    monto_total: filteredPresupuestos.reduce((sum, p) => sum + p.monto_total, 0),
    monto_ejecutado: filteredPresupuestos.reduce((sum, p) => sum + p.monto_ejecutado, 0),
    porcentaje_ejecucion: 0,
    presupuestos_activos: filteredPresupuestos.filter(p => p.estado === 'En_Ejecucion').length,
    presupuestos_aprobados: filteredPresupuestos.filter(p => p.estado === 'Aprobado').length,
    presupuestos_cerrados: filteredPresupuestos.filter(p => p.estado === 'Cerrado').length
  };

  stats.porcentaje_ejecucion = stats.monto_total > 0 ? 
    Math.round((stats.monto_ejecutado / stats.monto_total) * 100) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <DollarSign className="w-8 h-8 text-green-600" />
              Gestión Presupuestaria
            </h1>
            <p className="text-gray-600 mt-2">
              Control y seguimiento de presupuestos institucionales
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={() => setActiveTab('reportes')}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Reportes
            </Button>
            <Button 
              className="bg-green-600 hover:bg-green-700"
              onClick={() => navigate('/presupuesto/nuevo')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Presupuesto
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Presupuesto Total</p>
                  <p className="text-2xl font-bold text-green-700">
                    {formatCurrency(stats.monto_total)}
                  </p>
                  <div className="flex items-center mt-2">
                    <FileText className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">{stats.total_presupuestos} presupuestos</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Monto Ejecutado</p>
                  <p className="text-2xl font-bold text-blue-700">
                    {formatCurrency(stats.monto_ejecutado)}
                  </p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm text-blue-600">{stats.porcentaje_ejecucion}% ejecutado</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <Calculator className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Ejecución</p>
                  <p className="text-3xl font-bold text-purple-700">{stats.presupuestos_activos}</p>
                  <div className="flex items-center mt-2">
                    <Clock className="w-4 h-4 text-purple-600 mr-1" />
                    <span className="text-sm text-purple-600">Presupuestos activos</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                  <Clock className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Eficiencia</p>
                  <p className="text-3xl font-bold text-indigo-700">{stats.porcentaje_ejecucion}%</p>
                  <div className="flex items-center mt-2">
                    <Percent className="w-4 h-4 text-indigo-600 mr-1" />
                    <span className="text-sm text-indigo-600">Ejecución promedio</span>
                  </div>
                </div>
                <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                  <Target className="w-8 h-8 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button 
                variant={activeTab === 'presupuestos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('presupuestos')}
                className="flex items-center gap-2"
              >
                <DollarSign className="w-4 h-4" />
                Presupuestos
              </Button>
              <Button 
                variant={activeTab === 'ejecucion' ? 'default' : 'outline'}
                onClick={() => setActiveTab('ejecucion')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="w-4 h-4" />
                Ejecución
              </Button>
              <Button 
                variant={activeTab === 'categorias' ? 'default' : 'outline'}
                onClick={() => setActiveTab('categorias')}
                className="flex items-center gap-2"
              >
                <Building2 className="w-4 h-4" />
                Categorías
              </Button>
              <Button 
                variant={activeTab === 'reportes' ? 'default' : 'outline'}
                onClick={() => setActiveTab('reportes')}
                className="flex items-center gap-2"
              >
                <PieChart className="w-4 h-4" />
                Reportes
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'presupuestos' && (
          <>
            {/* Filtros y controles */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por nombre, descripción o responsable..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <select
                      value={filtroAño}
                      onChange={(e) => setFiltroAño(parseInt(e.target.value))}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value={2023}>2023</option>
                      <option value={2024}>2024</option>
                      <option value={2025}>2025</option>
                      <option value={2026}>2026</option>
                    </select>

                    <select
                      value={filtroEstado}
                      onChange={(e) => setFiltroEstado(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="todos">Todos los estados</option>
                      <option value="Borrador">Borrador</option>
                      <option value="Aprobado">Aprobado</option>
                      <option value="En_Ejecucion">En Ejecución</option>
                      <option value="Cerrado">Cerrado</option>
                      <option value="Cancelado">Cancelado</option>
                    </select>

                    <select
                      value={filtroDepartamento}
                      onChange={(e) => setFiltroDepartamento(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="todos">Todos los departamentos</option>
                      {departamentos.map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Resumen de filtros */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Mostrando {filteredPresupuestos.length} de {presupuestos.length} presupuestos</span>
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        En Ejecución: {stats.presupuestos_activos}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Aprobados: {stats.presupuestos_aprobados}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Cerrados: {stats.presupuestos_cerrados}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lista de presupuestos */}
            <div className="space-y-4">
              {filteredPresupuestos.map((presupuesto) => {
                const statusInfo = getStatusInfo(presupuesto.estado);
                const porcentajeEjecucion = calcularPorcentajeEjecucion(presupuesto.monto_ejecutado, presupuesto.monto_total);

                return (
                  <Card key={presupuesto.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
                            <DollarSign className="w-8 h-8 text-green-600" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-xl font-semibold text-gray-900">{presupuesto.nombre}</h3>
                              <Badge className={statusInfo.color}>
                                <div className="flex items-center gap-1">
                                  {statusInfo.icon}
                                  {statusInfo.label}
                                </div>
                              </Badge>
                              <Badge className="bg-gray-100 text-gray-800">
                                {presupuesto.periodo}
                              </Badge>
                            </div>

                            <p className="text-gray-600 mb-3">{presupuesto.descripcion}</p>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">
                                  <strong>Monto Total:</strong> {formatCurrency(presupuesto.monto_total)}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Ejecutado:</strong> {formatCurrency(presupuesto.monto_ejecutado)}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Disponible:</strong> {formatCurrency(presupuesto.monto_total - presupuesto.monto_ejecutado)}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Responsable:</strong> {presupuesto.responsable}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Departamento:</strong> {presupuesto.departamento}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Año:</strong> {presupuesto.año}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Inicio:</strong> {new Date(presupuesto.fecha_inicio).toLocaleDateString('es-CO')}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Fin:</strong> {new Date(presupuesto.fecha_fin).toLocaleDateString('es-CO')}
                                </p>
                                <div className="mt-2">
                                  <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                                    <span>Ejecución</span>
                                    <span>{porcentajeEjecucion}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                      className={`h-2 rounded-full ${
                                        porcentajeEjecucion >= 80 ? 'bg-green-500' :
                                        porcentajeEjecucion >= 60 ? 'bg-yellow-500' :
                                        'bg-red-500'
                                      }`}
                                      style={{ width: `${porcentajeEjecucion}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/presupuesto/${presupuesto.id}`)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/presupuesto/${presupuesto.id}/editar`)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>

                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/presupuesto/${presupuesto.id}/ejecucion`)}
                            >
                              <BarChart3 className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/presupuesto/${presupuesto.id}/reportes`)}
                            >
                              <FileText className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </>
        )}

        {/* Pestaña de Ejecución */}
        {activeTab === 'ejecucion' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Ejecución Presupuestaria</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Ejecución Presupuestaria</h3>
                <p className="text-gray-600 mb-6">Visualización detallada de la ejecución de presupuestos por categorías y períodos.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Configurar Reportes
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Reportes */}
        {activeTab === 'reportes' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Reportes Presupuestarios</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Reportes y Análisis</h3>
                <p className="text-gray-600 mb-6">Genere reportes detallados y análisis de la gestión presupuestaria.</p>
                <Button className="bg-green-600 hover:bg-green-700">
                  <FileText className="w-4 h-4 mr-2" />
                  Generar Reporte
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default PresupuestoModerno;
