import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  Building2, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  Star,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  BarChart3,
  PieChart,
  Target,
  AlertCircle,
  CheckCircle,
  Clock,
  Phone,
  Mail,
  MapPin,
  DollarSign,
  Package,
  Truck,
  Award,
  Users,
  ShoppingCart
} from 'lucide-react';

// Interfaces de datos
interface Proveedor {
  id: number;
  nombre: string;
  nit: string;
  razon_social: string;
  categoria: string;
  estado: 'Activo' | 'Inactivo' | 'En_Revision' | 'Suspendido';
  calificacion: number;
  telefono: string;
  email: string;
  direccion: string;
  ciudad: string;
  contacto_principal: string;
  fecha_registro: string;
  ultima_compra: string;
  monto_total_compras: number;
  numero_ordenes: number;
  tiempo_entrega_promedio: number;
  productos_servicios: string[];
  certificaciones: string[];
  evaluaciones: EvaluacionProveedor[];
}

interface EvaluacionProveedor {
  id: number;
  fecha: string;
  calidad: number;
  tiempo_entrega: number;
  servicio_cliente: number;
  precio: number;
  calificacion_general: number;
  comentarios: string;
  evaluador: string;
}

interface Contrato {
  id: number;
  proveedor_id: number;
  numero_contrato: string;
  fecha_inicio: string;
  fecha_fin: string;
  valor_total: number;
  estado: 'Vigente' | 'Vencido' | 'Suspendido';
  tipo: 'Suministros' | 'Servicios' | 'Mantenimiento' | 'Obras';
}

const ProveedoresModerno: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('proveedores');
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState('todos');
  const [filtroCategoria, setFiltroCategoria] = useState('todos');
  const [filtroCalificacion, setFiltroCalificacion] = useState('todos');

  // Datos de ejemplo para desarrollo
  const proveedores: Proveedor[] = [
    {
      id: 1,
      nombre: 'Suministros Médicos S.A.S.',
      nit: '900123456-1',
      razon_social: 'Suministros Médicos S.A.S.',
      categoria: 'Equipos Médicos',
      estado: 'Activo',
      calificacion: 4.8,
      telefono: '601-2345678',
      email: '<EMAIL>',
      direccion: 'Calle 100 #15-20',
      ciudad: 'Bogotá',
      contacto_principal: 'María González',
      fecha_registro: '2023-01-15',
      ultima_compra: '2024-11-20',
      monto_total_compras: 450000000,
      numero_ordenes: 45,
      tiempo_entrega_promedio: 3,
      productos_servicios: ['Equipos de diagnóstico', 'Monitores', 'Desfibriladores'],
      certificaciones: ['ISO 9001', 'ISO 13485', 'INVIMA'],
      evaluaciones: []
    },
    {
      id: 2,
      nombre: 'Farmacéuticos Unidos Ltda.',
      nit: '800987654-2',
      razon_social: 'Farmacéuticos Unidos Ltda.',
      categoria: 'Medicamentos',
      estado: 'Activo',
      calificacion: 4.5,
      telefono: '601-8765432',
      email: '<EMAIL>',
      direccion: 'Carrera 50 #80-45',
      ciudad: 'Medellín',
      contacto_principal: 'Carlos Rodríguez',
      fecha_registro: '2022-08-10',
      ultima_compra: '2024-11-25',
      monto_total_compras: 680000000,
      numero_ordenes: 78,
      tiempo_entrega_promedio: 2,
      productos_servicios: ['Medicamentos genéricos', 'Antibióticos', 'Analgésicos'],
      certificaciones: ['BPM', 'INVIMA', 'OMS'],
      evaluaciones: []
    },
    {
      id: 3,
      nombre: 'Servicios Hospitalarios Integrales',
      nit: '700456789-3',
      razon_social: 'Servicios Hospitalarios Integrales S.A.',
      categoria: 'Servicios',
      estado: 'En_Revision',
      calificacion: 4.2,
      telefono: '602-5551234',
      email: '<EMAIL>',
      direccion: 'Avenida 6 #25-30',
      ciudad: 'Cali',
      contacto_principal: 'Ana Martínez',
      fecha_registro: '2024-03-20',
      ultima_compra: '2024-10-15',
      monto_total_compras: 120000000,
      numero_ordenes: 12,
      tiempo_entrega_promedio: 5,
      productos_servicios: ['Limpieza hospitalaria', 'Mantenimiento', 'Seguridad'],
      certificaciones: ['ISO 14001', 'OHSAS 18001'],
      evaluaciones: []
    },
    {
      id: 4,
      nombre: 'Insumos Quirúrgicos del Norte',
      nit: '600789123-4',
      razon_social: 'Insumos Quirúrgicos del Norte S.A.S.',
      categoria: 'Insumos Quirúrgicos',
      estado: 'Suspendido',
      calificacion: 3.8,
      telefono: '605-9876543',
      email: '<EMAIL>',
      direccion: 'Calle 72 #10-15',
      ciudad: 'Barranquilla',
      contacto_principal: 'Luis Hernández',
      fecha_registro: '2023-06-05',
      ultima_compra: '2024-08-30',
      monto_total_compras: 280000000,
      numero_ordenes: 28,
      tiempo_entrega_promedio: 4,
      productos_servicios: ['Material quirúrgico', 'Suturas', 'Instrumental'],
      certificaciones: ['CE', 'FDA'],
      evaluaciones: []
    }
  ];

  const categorias = [
    'Equipos Médicos',
    'Medicamentos',
    'Insumos Quirúrgicos',
    'Servicios',
    'Mantenimiento',
    'Alimentación',
    'Limpieza',
    'Tecnología'
  ];

  // Filtrar proveedores
  const filteredProveedores = proveedores.filter(proveedor => {
    const matchesSearch = 
      proveedor.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proveedor.nit.includes(searchTerm) ||
      proveedor.razon_social.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proveedor.contacto_principal.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proveedor.ciudad.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEstado = filtroEstado === 'todos' || proveedor.estado === filtroEstado;
    const matchesCategoria = filtroCategoria === 'todos' || proveedor.categoria === filtroCategoria;
    const matchesCalificacion = filtroCalificacion === 'todos' || 
      (filtroCalificacion === '5' && proveedor.calificacion >= 4.5) ||
      (filtroCalificacion === '4' && proveedor.calificacion >= 4.0 && proveedor.calificacion < 4.5) ||
      (filtroCalificacion === '3' && proveedor.calificacion >= 3.0 && proveedor.calificacion < 4.0) ||
      (filtroCalificacion === '2' && proveedor.calificacion < 3.0);
    
    return matchesSearch && matchesEstado && matchesCategoria && matchesCalificacion;
  });

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return { color: 'bg-green-100 text-green-800', label: 'Activo', icon: <CheckCircle className="w-3 h-3" /> };
      case 'Inactivo':
        return { color: 'bg-gray-100 text-gray-800', label: 'Inactivo', icon: <AlertCircle className="w-3 h-3" /> };
      case 'En_Revision':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'En Revisión', icon: <Clock className="w-3 h-3" /> };
      case 'Suspendido':
        return { color: 'bg-red-100 text-red-800', label: 'Suspendido', icon: <AlertCircle className="w-3 h-3" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado, icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  // Formatear moneda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Renderizar estrellas de calificación
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 fill-yellow-200 text-yellow-400" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  // Calcular estadísticas
  const stats = {
    total: filteredProveedores.length,
    activos: filteredProveedores.filter(p => p.estado === 'Activo').length,
    inactivos: filteredProveedores.filter(p => p.estado === 'Inactivo').length,
    en_revision: filteredProveedores.filter(p => p.estado === 'En_Revision').length,
    suspendidos: filteredProveedores.filter(p => p.estado === 'Suspendido').length,
    calificacion_promedio: filteredProveedores.length > 0 ? 
      filteredProveedores.reduce((sum, p) => sum + p.calificacion, 0) / filteredProveedores.length : 0,
    monto_total: filteredProveedores.reduce((sum, p) => sum + p.monto_total_compras, 0),
    ordenes_total: filteredProveedores.reduce((sum, p) => sum + p.numero_ordenes, 0)
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Building2 className="w-8 h-8 text-blue-600" />
              Gestión de Proveedores
            </h1>
            <p className="text-gray-600 mt-2">
              Administración integral de proveedores, contratos y evaluaciones
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={() => setActiveTab('reportes')}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Reportes
            </Button>
            <Button 
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => navigate('/proveedores/nuevo')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Proveedor
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Proveedores</p>
                  <p className="text-3xl font-bold text-blue-700">{stats.total}</p>
                  <div className="flex items-center mt-2">
                    <Building2 className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm text-blue-600">{stats.activos} activos</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <Building2 className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Calificación Promedio</p>
                  <p className="text-3xl font-bold text-yellow-700">{stats.calificacion_promedio.toFixed(1)}</p>
                  <div className="flex items-center mt-2">
                    {renderStars(stats.calificacion_promedio).slice(0, 5)}
                  </div>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                  <Star className="w-8 h-8 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Monto Total Compras</p>
                  <p className="text-2xl font-bold text-green-700">
                    {formatCurrency(stats.monto_total)}
                  </p>
                  <div className="flex items-center mt-2">
                    <DollarSign className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">{stats.ordenes_total} órdenes</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Revisión</p>
                  <p className="text-3xl font-bold text-orange-700">{stats.en_revision}</p>
                  <div className="flex items-center mt-2">
                    <Clock className="w-4 h-4 text-orange-600 mr-1" />
                    <span className="text-sm text-orange-600">Pendientes</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg border border-orange-100">
                  <Clock className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button 
                variant={activeTab === 'proveedores' ? 'default' : 'outline'}
                onClick={() => setActiveTab('proveedores')}
                className="flex items-center gap-2"
              >
                <Building2 className="w-4 h-4" />
                Proveedores
              </Button>
              <Button 
                variant={activeTab === 'evaluaciones' ? 'default' : 'outline'}
                onClick={() => setActiveTab('evaluaciones')}
                className="flex items-center gap-2"
              >
                <Star className="w-4 h-4" />
                Evaluaciones
              </Button>
              <Button 
                variant={activeTab === 'contratos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('contratos')}
                className="flex items-center gap-2"
              >
                <FileText className="w-4 h-4" />
                Contratos
              </Button>
              <Button 
                variant={activeTab === 'compras' ? 'default' : 'outline'}
                onClick={() => setActiveTab('compras')}
                className="flex items-center gap-2"
              >
                <ShoppingCart className="w-4 h-4" />
                Órdenes de Compra
              </Button>
              <Button 
                variant={activeTab === 'reportes' ? 'default' : 'outline'}
                onClick={() => setActiveTab('reportes')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="w-4 h-4" />
                Reportes
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'proveedores' && (
          <>
            {/* Filtros y controles */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por nombre, NIT, contacto o ciudad..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <select
                      value={filtroEstado}
                      onChange={(e) => setFiltroEstado(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="todos">Todos los estados</option>
                      <option value="Activo">Activo</option>
                      <option value="Inactivo">Inactivo</option>
                      <option value="En_Revision">En Revisión</option>
                      <option value="Suspendido">Suspendido</option>
                    </select>

                    <select
                      value={filtroCategoria}
                      onChange={(e) => setFiltroCategoria(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="todos">Todas las categorías</option>
                      {categorias.map(categoria => (
                        <option key={categoria} value={categoria}>{categoria}</option>
                      ))}
                    </select>

                    <select
                      value={filtroCalificacion}
                      onChange={(e) => setFiltroCalificacion(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="todos">Todas las calificaciones</option>
                      <option value="5">5 estrellas (4.5+)</option>
                      <option value="4">4 estrellas (4.0-4.4)</option>
                      <option value="3">3 estrellas (3.0-3.9)</option>
                      <option value="2">Menos de 3 estrellas</option>
                    </select>
                  </div>
                </div>

                {/* Resumen de filtros */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Mostrando {filteredProveedores.length} de {proveedores.length} proveedores</span>
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Activos: {stats.activos}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        En Revisión: {stats.en_revision}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        Suspendidos: {stats.suspendidos}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lista de proveedores */}
            <div className="space-y-4">
              {filteredProveedores.map((proveedor) => {
                const statusInfo = getStatusInfo(proveedor.estado);

                return (
                  <Card key={proveedor.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-8 h-8 text-blue-600" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-xl font-semibold text-gray-900">{proveedor.nombre}</h3>
                              <Badge className={statusInfo.color}>
                                <div className="flex items-center gap-1">
                                  {statusInfo.icon}
                                  {statusInfo.label}
                                </div>
                              </Badge>
                              <Badge className="bg-gray-100 text-gray-800">
                                {proveedor.categoria}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2 mb-3">
                              <div className="flex items-center">
                                {renderStars(proveedor.calificacion)}
                                <span className="ml-2 text-sm text-gray-600">({proveedor.calificacion})</span>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">
                                  <strong>NIT:</strong> {proveedor.nit}
                                </p>
                                <p className="text-gray-600 flex items-center gap-1">
                                  <Phone className="w-3 h-3" />
                                  <strong>Teléfono:</strong> {proveedor.telefono}
                                </p>
                                <p className="text-gray-600 flex items-center gap-1">
                                  <Mail className="w-3 h-3" />
                                  <strong>Email:</strong> {proveedor.email}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600 flex items-center gap-1">
                                  <MapPin className="w-3 h-3" />
                                  <strong>Ciudad:</strong> {proveedor.ciudad}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Contacto:</strong> {proveedor.contacto_principal}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Registro:</strong> {new Date(proveedor.fecha_registro).toLocaleDateString('es-CO')}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Total Compras:</strong> {formatCurrency(proveedor.monto_total_compras)}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Órdenes:</strong> {proveedor.numero_ordenes}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Tiempo Entrega:</strong> {proveedor.tiempo_entrega_promedio} días
                                </p>
                              </div>
                            </div>

                            {/* Productos/Servicios */}
                            <div className="mt-3">
                              <p className="text-sm text-gray-600 mb-1"><strong>Productos/Servicios:</strong></p>
                              <div className="flex flex-wrap gap-1">
                                {proveedor.productos_servicios.slice(0, 3).map((producto, index) => (
                                  <Badge key={index} className="bg-blue-50 text-blue-700 text-xs">
                                    {producto}
                                  </Badge>
                                ))}
                                {proveedor.productos_servicios.length > 3 && (
                                  <Badge className="bg-gray-50 text-gray-600 text-xs">
                                    +{proveedor.productos_servicios.length - 3} más
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/proveedores/${proveedor.id}`)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/proveedores/${proveedor.id}/editar`)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>

                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/proveedores/${proveedor.id}/evaluaciones`)}
                            >
                              <Star className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/proveedores/${proveedor.id}/contratos`)}
                            >
                              <FileText className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </>
        )}

        {/* Pestaña de Evaluaciones */}
        {activeTab === 'evaluaciones' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Evaluaciones de Proveedores</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Star className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Sistema de Evaluaciones</h3>
                <p className="text-gray-600 mb-6">Gestione las evaluaciones de desempeño de sus proveedores.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Nueva Evaluación
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Contratos */}
        {activeTab === 'contratos' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Gestión de Contratos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contratos con Proveedores</h3>
                <p className="text-gray-600 mb-6">Administre contratos, renovaciones y términos de servicio.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Nuevo Contrato
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Órdenes de Compra */}
        {activeTab === 'compras' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Órdenes de Compra</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Gestión de Compras</h3>
                <p className="text-gray-600 mb-6">Administre órdenes de compra y seguimiento de entregas.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Nueva Orden de Compra
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pestaña de Reportes */}
        {activeTab === 'reportes' && (
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardHeader>
              <CardTitle>Reportes y Análisis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Reportes de Proveedores</h3>
                <p className="text-gray-600 mb-6">Genere reportes de desempeño, compras y evaluaciones.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Generar Reporte
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProveedoresModerno;
