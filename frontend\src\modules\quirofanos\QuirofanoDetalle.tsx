import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { quirofanosService } from "../../services/quirofanosService";
import { Button } from "../../components/ui/Button";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faProcedures,
  faUser,
  faCalendarAlt,
  faHospital,
  faCheckCircle,
  faExclamationCircle,
  faTimesCircle,
  faTools,
  faHistory,
  faEdit,
} from "@fortawesome/free-solid-svg-icons";
// Definir el tipo Quirofano para usar en el componente
interface Quirofano {
  id: string;
  numero: string;
  estado: "Disponible" | "Ocupado" | "En_Mantenimiento";
  ubicacion?: string;
  created_at?: string;
}

// Tipo para el historial de cirugías
interface CirugiaHistorial {
  id: string;
  quirofano_id: string;
  paciente_id: string;
  paciente_nombre: string;
  medico_id: string;
  medico_nombre: string;
  fecha_inicio: string;
  fecha_fin: string | null;
  tipo_cirugia: string;
  estado: string;
  notas?: string;
}

export const QuirofanoDetalle = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"informacion" | "historial">("informacion");

  // Obtener detalles del quirófano
  const {
    data: quirofano,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["quirofanos", id],
    queryFn: () => (id ? quirofanosService.getById(id) : Promise.reject("ID no proporcionado")),
    enabled: !!id,
  });

  // Simulación de datos de historial de cirugías
  const [historialCirugias, setHistorialCirugias] = useState<CirugiaHistorial[]>([]);
  
  useEffect(() => {
    // Simulación de carga de historial desde API
    if (id) {
      setTimeout(() => {
        const mockHistorial: CirugiaHistorial[] = [
          {
            id: "cir1",
            quirofano_id: id,
            paciente_id: "p1",
            paciente_nombre: "Carlos Ramírez",
            medico_id: "m1",
            medico_nombre: "Dra. Alicia Rodríguez",
            fecha_inicio: "2025-05-05T08:30:00",
            fecha_fin: "2025-05-05T11:15:00",
            tipo_cirugia: "Apendicectomía",
            estado: "Completada",
            notas: "Sin complicaciones"
          },
          {
            id: "cir2",
            quirofano_id: id,
            paciente_id: "p2",
            paciente_nombre: "Ana Martínez",
            medico_id: "m2",
            medico_nombre: "Dr. Roberto Méndez",
            fecha_inicio: "2025-05-03T10:00:00",
            fecha_fin: "2025-05-03T14:45:00",
            tipo_cirugia: "Colecistectomía laparoscópica",
            estado: "Completada",
            notas: "Paciente estable"
          },
          {
            id: "cir3",
            quirofano_id: id,
            paciente_id: "p3",
            paciente_nombre: "Luis Torres",
            medico_id: "m3",
            medico_nombre: "Dr. Manuel Guzmán",
            fecha_inicio: "2025-05-01T14:15:00",
            fecha_fin: "2025-05-01T16:30:00",
            tipo_cirugia: "Hernioplastía",
            estado: "Completada",
            notas: "Intervención exitosa"
          },
        ];
        setHistorialCirugias(mockHistorial);
      }, 800);
    }
  }, [id]);

  // Formatear fecha para mostrar
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string): string => {
    switch (estado) {
      case "Disponible":
        return "text-green-500 bg-green-500/10";
      case "Ocupado":
        return "text-red-500 bg-red-500/10";
      case "En_Mantenimiento":
        return "text-orange-500 bg-orange-500/10";
      default:
        return "text-gray-500 bg-gray-500/10";
    }
  };

  // Obtener icono según estado
  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case "Disponible":
        return <FontAwesomeIcon icon={faCheckCircle} />;
      case "Ocupado":
        return <FontAwesomeIcon icon={faExclamationCircle} />;
      case "En_Mantenimiento":
        return <FontAwesomeIcon icon={faTools} />;
      default:
        return <FontAwesomeIcon icon={faTimesCircle} />;
    }
  };
  
  // Formatear texto del estado para mostrar
  const formatEstadoText = (estado: string): string => {
    if (estado === "En_Mantenimiento") return "En Mantenimiento";
    return estado;
  };

  // Calcular duración de cirugía
  const calcularDuracion = (fechaInicio: string, fechaFin: string | null): string => {
    if (!fechaFin) return "En curso";

    const inicio = new Date(fechaInicio);
    const fin = new Date(fechaFin);
    const diff = fin.getTime() - inicio.getTime();
    
    const horas = Math.floor(diff / (1000 * 60 * 60));
    const minutos = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${horas}h ${minutos}m`;
  };

  // Renderizar pestañas
  const renderTabs = () => {
    return (
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="flex">
            <button
              className={`py-3 px-4 mr-2 border-b-2 ${
                activeTab === "informacion"
                  ? "border-blue-500 text-blue-500"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("informacion")}
            >
              Información
            </button>
            <button
              className={`py-3 px-4 mr-2 border-b-2 ${
                activeTab === "historial"
                  ? "border-blue-500 text-blue-500"
                  : "border-transparent text-gray-400 hover:text-white"
              }`}
              onClick={() => setActiveTab("historial")}
            >
              Historial de Cirugías
            </button>
          </nav>
        </div>
      </div>
    );
  };

  // Renderizar contenido según la pestaña activa
  const renderTabContent = () => {
    if (!quirofano) return null;

    if (activeTab === "informacion") {
      return (
        <div>
          <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Información del Quirófano</h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-400">Número</p>
                      <p className="text-white">
                        <FontAwesomeIcon icon={faProcedures} className="mr-2 text-blue-500" />
                        {quirofano.numero}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Estado</p>
                      <p className="text-white">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getEstadoColor(quirofano.estado)}`}>
                          {getEstadoIcon(quirofano.estado)} <span className="ml-1">{formatEstadoText(quirofano.estado)}</span>
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Ubicación</p>
                      <p className="text-white">
                        <FontAwesomeIcon icon={faHospital} className="mr-2 text-blue-500" />
                        {quirofano.ubicacion || "No especificada"}
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Información Adicional</h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-400">Especialidades</p>
                      <p className="text-white">
                        <FontAwesomeIcon icon={faUser} className="mr-2 text-blue-500" />
                        General
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Última modificación</p>
                      <p className="text-white">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-blue-500" />
                        {formatDate(new Date().toISOString())}
                      </p>
                    </div>
                    <div className="mt-6">
                      <Button
                        onClick={() => navigate(`/quirofanos/edit/${id}`)}
                        variant="outline"
                      >
                        <FontAwesomeIcon icon={faEdit} className="mr-2" />
                        Editar Quirófano
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div>
          <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-700">
              <h3 className="text-lg font-semibold text-white">
                <FontAwesomeIcon icon={faHistory} className="mr-2 text-blue-500" />
                Historial de Cirugías
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Registro histórico de las cirugías realizadas en este quirófano
              </p>
            </div>

            {historialCirugias.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-700">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Paciente
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Médico
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Inicio
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Fin
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Duración
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        Tipo
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {historialCirugias.map((cirugia) => (
                      <tr key={cirugia.id} className="hover:bg-gray-700/50">
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {cirugia.paciente_nombre}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {cirugia.medico_nombre}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {formatDate(cirugia.fecha_inicio)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {cirugia.fecha_fin ? formatDate(cirugia.fecha_fin) : "En curso"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {calcularDuracion(cirugia.fecha_inicio, cirugia.fecha_fin)}
                        </td>
                        <td className="px-4 py-3 text-sm text-white">
                          {cirugia.tipo_cirugia}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-400">No hay historial de cirugías disponible.</p>
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end">
            <Button variant="secondary" onClick={() => navigate("/quirofanos")}>
              <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
              Volver
            </Button>
          </div>
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (isError || !quirofano) {
    return (
      <div className="p-6">
        <div className="bg-red-900/30 text-red-400 p-4 rounded mb-6">
          <p className="text-center">Error al cargar los datos del quirófano.</p>
        </div>
        <div className="flex justify-center">
          <Button variant="secondary" onClick={() => navigate("/quirofanos")}>
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Volver a Quirófanos
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Button variant="secondary" onClick={() => navigate("/quirofanos")} className="mr-4">
          <FontAwesomeIcon icon={faArrowLeft} />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-white">
            Quirófano {quirofano.numero}
            <span className={`ml-3 text-sm px-3 py-1 rounded-full font-medium ${getEstadoColor(quirofano.estado)}`}>
              {getEstadoIcon(quirofano.estado)} <span className="ml-1">{formatEstadoText(quirofano.estado)}</span>
            </span>
          </h1>
          <p className="text-gray-400">
            {quirofano.ubicacion ? `Ubicación: ${quirofano.ubicacion}` : "Sin ubicación específica"}
          </p>
        </div>
      </div>

      {renderTabs()}
      {renderTabContent()}
    </div>
  );
};
