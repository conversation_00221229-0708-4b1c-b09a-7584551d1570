import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { 
  Users, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  UserPlus,
  Building2,
  Briefcase,
  Calendar,
  Clock,
  GraduationCap,
  Star,
  DollarSign,
  FileText,
  Settings,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

// Interfaces de datos
interface Empleado {
  id: number;
  nombres: string;
  apellidos: string;
  documento: string;
  email: string;
  telefono: string;
  cargo: string;
  departamento: string;
  estado: 'Activo' | 'Inactivo' | 'Vacaciones' | 'Licencia';
  fecha_ingreso: string;
  salario: number;
  foto?: string;
  especialidad?: string;
  turno: string;
  supervisor?: string;
}

interface Departamento {
  id: number;
  nombre: string;
  descripcion: string;
  jefe: string;
  empleados_count: number;
  presupuesto: number;
  estado: 'Activo' | 'Inactivo';
}

interface Cargo {
  id: number;
  nombre: string;
  departamento: string;
  nivel: string;
  salario_min: number;
  salario_max: number;
  requisitos: string[];
  responsabilidades: string[];
  estado: 'Activo' | 'Inactivo';
}

const RecursosHumanosModerno: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('empleados');
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState('todos');
  const [filtroDepartamento, setFiltroDepartamento] = useState('todos');

  // Datos de ejemplo para desarrollo
  const empleados: Empleado[] = [
    {
      id: 1,
      nombres: 'María Elena',
      apellidos: 'García López',
      documento: '12345678',
      email: '<EMAIL>',
      telefono: '3001234567',
      cargo: 'Médico General',
      departamento: 'Medicina Interna',
      estado: 'Activo',
      fecha_ingreso: '2023-01-15',
      salario: 8500000,
      especialidad: 'Medicina Interna',
      turno: 'Diurno',
      supervisor: 'Dr. Carlos Ruiz'
    },
    {
      id: 2,
      nombres: 'Carlos Alberto',
      apellidos: 'Rodríguez Pérez',
      documento: '87654321',
      email: '<EMAIL>',
      telefono: '3009876543',
      cargo: 'Enfermero Jefe',
      departamento: 'Urgencias',
      estado: 'Activo',
      fecha_ingreso: '2022-03-10',
      salario: 4500000,
      turno: 'Nocturno',
      supervisor: 'Dra. Ana Martínez'
    },
    {
      id: 3,
      nombres: 'Ana Sofía',
      apellidos: 'Martínez Silva',
      documento: '11223344',
      email: '<EMAIL>',
      telefono: '3005551234',
      cargo: 'Cirujano',
      departamento: 'Cirugía',
      estado: 'Vacaciones',
      fecha_ingreso: '2021-06-20',
      salario: 12000000,
      especialidad: 'Cirugía General',
      turno: 'Diurno'
    },
    {
      id: 4,
      nombres: 'Luis Fernando',
      apellidos: 'Hernández Castro',
      documento: '55667788',
      email: '<EMAIL>',
      telefono: '3007778888',
      cargo: 'Administrador',
      departamento: 'Administración',
      estado: 'Activo',
      fecha_ingreso: '2020-09-05',
      salario: 6000000,
      turno: 'Diurno',
      supervisor: 'Gerente General'
    }
  ];

  const departamentos: Departamento[] = [
    {
      id: 1,
      nombre: 'Medicina Interna',
      descripcion: 'Atención médica general y especializada',
      jefe: 'Dr. Carlos Ruiz',
      empleados_count: 15,
      presupuesto: 180000000,
      estado: 'Activo'
    },
    {
      id: 2,
      nombre: 'Urgencias',
      descripcion: 'Atención de emergencias médicas',
      jefe: 'Dra. Ana Martínez',
      empleados_count: 25,
      presupuesto: 250000000,
      estado: 'Activo'
    },
    {
      id: 3,
      nombre: 'Cirugía',
      descripcion: 'Procedimientos quirúrgicos',
      jefe: 'Dr. Roberto Silva',
      empleados_count: 12,
      presupuesto: 300000000,
      estado: 'Activo'
    },
    {
      id: 4,
      nombre: 'Administración',
      descripcion: 'Gestión administrativa y financiera',
      jefe: 'Lic. Patricia Gómez',
      empleados_count: 8,
      presupuesto: 120000000,
      estado: 'Activo'
    }
  ];

  // Filtrar empleados
  const filteredEmpleados = empleados.filter(empleado => {
    const matchesSearch = 
      empleado.nombres.toLowerCase().includes(searchTerm.toLowerCase()) ||
      empleado.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
      empleado.cargo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      empleado.departamento.toLowerCase().includes(searchTerm.toLowerCase()) ||
      empleado.documento.includes(searchTerm);
    
    const matchesEstado = filtroEstado === 'todos' || empleado.estado === filtroEstado;
    const matchesDepartamento = filtroDepartamento === 'todos' || empleado.departamento === filtroDepartamento;
    
    return matchesSearch && matchesEstado && matchesDepartamento;
  });

  // Obtener información de estado
  const getStatusInfo = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return { color: 'bg-green-100 text-green-800', label: 'Activo', icon: <CheckCircle className="w-3 h-3" /> };
      case 'Inactivo':
        return { color: 'bg-gray-100 text-gray-800', label: 'Inactivo', icon: <AlertCircle className="w-3 h-3" /> };
      case 'Vacaciones':
        return { color: 'bg-blue-100 text-blue-800', label: 'Vacaciones', icon: <Calendar className="w-3 h-3" /> };
      case 'Licencia':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'Licencia', icon: <Clock className="w-3 h-3" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: estado, icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  // Formatear salario
  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Calcular estadísticas
  const stats = {
    total: empleados.length,
    activos: empleados.filter(e => e.estado === 'Activo').length,
    inactivos: empleados.filter(e => e.estado === 'Inactivo').length,
    vacaciones: empleados.filter(e => e.estado === 'Vacaciones').length,
    licencias: empleados.filter(e => e.estado === 'Licencia').length,
    departamentos: departamentos.length,
    presupuesto_total: departamentos.reduce((sum, dept) => sum + dept.presupuesto, 0)
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Users className="w-8 h-8 text-blue-600" />
              Recursos Humanos
            </h1>
            <p className="text-gray-600 mt-2">
              Gestión integral del talento humano y administración de personal
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={() => setActiveTab('reportes')}
            >
              <FileText className="w-4 h-4 mr-2" />
              Reportes
            </Button>
            <Button 
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => navigate('/recursos-humanos/empleado/nuevo')}
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Nuevo Empleado
            </Button>
          </div>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Empleados</p>
                  <p className="text-3xl font-bold text-blue-700">{stats.total}</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">+5.2% vs mes anterior</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Empleados Activos</p>
                  <p className="text-3xl font-bold text-green-700">{stats.activos}</p>
                  <div className="flex items-center mt-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">{((stats.activos/stats.total)*100).toFixed(1)}% del total</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-100">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Departamentos</p>
                  <p className="text-3xl font-bold text-purple-700">{stats.departamentos}</p>
                  <div className="flex items-center mt-2">
                    <Building2 className="w-4 h-4 text-purple-600 mr-1" />
                    <span className="text-sm text-purple-600">Áreas operativas</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                  <Building2 className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Presupuesto Total</p>
                  <p className="text-2xl font-bold text-indigo-700">
                    {formatSalary(stats.presupuesto_total)}
                  </p>
                  <div className="flex items-center mt-2">
                    <DollarSign className="w-4 h-4 text-indigo-600 mr-1" />
                    <span className="text-sm text-indigo-600">Mensual</span>
                  </div>
                </div>
                <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                  <DollarSign className="w-8 h-8 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navegación por pestañas */}
        <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button 
                variant={activeTab === 'empleados' ? 'default' : 'outline'}
                onClick={() => setActiveTab('empleados')}
                className="flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Empleados
              </Button>
              <Button 
                variant={activeTab === 'departamentos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('departamentos')}
                className="flex items-center gap-2"
              >
                <Building2 className="w-4 h-4" />
                Departamentos
              </Button>
              <Button 
                variant={activeTab === 'cargos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('cargos')}
                className="flex items-center gap-2"
              >
                <Briefcase className="w-4 h-4" />
                Cargos
              </Button>
              <Button 
                variant={activeTab === 'nomina' ? 'default' : 'outline'}
                onClick={() => setActiveTab('nomina')}
                className="flex items-center gap-2"
              >
                <DollarSign className="w-4 h-4" />
                Nómina
              </Button>
              <Button 
                variant={activeTab === 'turnos' ? 'default' : 'outline'}
                onClick={() => setActiveTab('turnos')}
                className="flex items-center gap-2"
              >
                <Clock className="w-4 h-4" />
                Turnos
              </Button>
              <Button 
                variant={activeTab === 'capacitaciones' ? 'default' : 'outline'}
                onClick={() => setActiveTab('capacitaciones')}
                className="flex items-center gap-2"
              >
                <GraduationCap className="w-4 h-4" />
                Capacitaciones
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contenido de las pestañas */}
        {activeTab === 'empleados' && (
          <>
            {/* Filtros y controles */}
            <Card className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar por nombre, cargo, departamento o documento..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <select
                      value={filtroEstado}
                      onChange={(e) => setFiltroEstado(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="todos">Todos los estados</option>
                      <option value="Activo">Activo</option>
                      <option value="Inactivo">Inactivo</option>
                      <option value="Vacaciones">Vacaciones</option>
                      <option value="Licencia">Licencia</option>
                    </select>

                    <select
                      value={filtroDepartamento}
                      onChange={(e) => setFiltroDepartamento(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="todos">Todos los departamentos</option>
                      {departamentos.map(dept => (
                        <option key={dept.id} value={dept.nombre}>{dept.nombre}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Resumen de filtros */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Mostrando {filteredEmpleados.length} de {empleados.length} empleados</span>
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Activos: {stats.activos}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Vacaciones: {stats.vacaciones}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        Inactivos: {stats.inactivos}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lista de empleados */}
            <div className="space-y-4">
              {filteredEmpleados.map((empleado) => {
                const statusInfo = getStatusInfo(empleado.estado);

                return (
                  <Card key={empleado.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                            {empleado.foto ? (
                              <img src={empleado.foto} alt={empleado.nombres} className="w-full h-full rounded-lg object-cover" />
                            ) : (
                              <Users className="w-8 h-8 text-blue-600" />
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-xl font-semibold text-gray-900">
                                {empleado.nombres} {empleado.apellidos}
                              </h3>
                              <Badge className={statusInfo.color}>
                                <div className="flex items-center gap-1">
                                  {statusInfo.icon}
                                  {statusInfo.label}
                                </div>
                              </Badge>
                              <Badge className="bg-gray-100 text-gray-800">
                                {empleado.cargo}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">
                                  <strong>Documento:</strong> {empleado.documento}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Email:</strong> {empleado.email}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Teléfono:</strong> {empleado.telefono}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Departamento:</strong> {empleado.departamento}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Turno:</strong> {empleado.turno}
                                </p>
                                <p className="text-gray-600">
                                  <strong>Ingreso:</strong> {new Date(empleado.fecha_ingreso).toLocaleDateString('es-CO')}
                                </p>
                              </div>

                              <div>
                                <p className="text-gray-600">
                                  <strong>Salario:</strong> {formatSalary(empleado.salario)}
                                </p>
                                {empleado.especialidad && (
                                  <p className="text-gray-600">
                                    <strong>Especialidad:</strong> {empleado.especialidad}
                                  </p>
                                )}
                                {empleado.supervisor && (
                                  <p className="text-gray-600">
                                    <strong>Supervisor:</strong> {empleado.supervisor}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/recursos-humanos/empleado/${empleado.id}`)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/recursos-humanos/empleado/${empleado.id}/editar`)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>

                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/recursos-humanos/empleado/${empleado.id}/horarios`)}
                            >
                              <Clock className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/recursos-humanos/empleado/${empleado.id}/evaluaciones`)}
                            >
                              <Star className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </>
        )}

        {/* Pestaña de Departamentos */}
        {activeTab === 'departamentos' && (
          <div className="space-y-4">
            {departamentos.map((departamento) => (
              <Card key={departamento.id} className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Building2 className="w-8 h-8 text-purple-600" />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold text-gray-900">{departamento.nombre}</h3>
                          <Badge className={departamento.estado === 'Activo' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {departamento.estado}
                          </Badge>
                        </div>

                        <p className="text-gray-600 mb-3">{departamento.descripcion}</p>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">
                              <strong>Jefe de Departamento:</strong> {departamento.jefe}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">
                              <strong>Empleados:</strong> {departamento.empleados_count}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">
                              <strong>Presupuesto:</strong> {formatSalary(departamento.presupuesto)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecursosHumanosModerno;
