import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import RRHH from '../RRHH';

// Mock del hook useAuth
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user',
      hospital_id: 1,
      nombre: 'Test User',
      rol: 'ADMIN'
    }
  })
}));

// Mock de React Query
const mockUseQuery = jest.fn();
jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQuery: () => mockUseQuery(),
  useQueryClient: () => ({
    invalidateQueries: jest.fn()
  })
}));

// Wrapper para providers
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('RRHH (Recursos Humanos)', () => {
  const mockEmpleados = [
    {
      id: 1,
      nombres: 'Juan Carlos',
      apellidos: 'Pérez García',
      documento: '12345678',
      email: '<EMAIL>',
      telefono: '**********',
      cargo_nombre: 'Médico General',
      departamento_nombre: 'Medicina Interna',
      estado: 'Activo',
      fecha_ingreso: '2023-01-15',
      salario: 5000000
    },
    {
      id: 2,
      nombres: 'María Elena',
      apellidos: 'González López',
      documento: '87654321',
      email: '<EMAIL>',
      telefono: '**********',
      cargo_nombre: 'Enfermera Jefe',
      departamento_nombre: 'Enfermería',
      estado: 'Activo',
      fecha_ingreso: '2022-06-10',
      salario: 3500000
    }
  ];

  const mockResumenRRHH = {
    total_empleados: 150,
    empleados_activos: 140,
    empleados_inactivos: 10,
    empleados_vacaciones: 5,
    empleados_licencia: 3
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock de datos por defecto
    mockUseQuery.mockReturnValue({
      data: mockEmpleados,
      isLoading: false,
      error: null
    });
  });

  describe('Renderizado inicial', () => {
    it('debe renderizar el componente correctamente', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que el título principal esté presente
      expect(screen.getByText('Recursos Humanos')).toBeInTheDocument();
      
      // Verificar que los botones de navegación estén presentes
      expect(screen.getByText('Dept.')).toBeInTheDocument();
      expect(screen.getByText('Cargos')).toBeInTheDocument();
      expect(screen.getByText('Nóm.')).toBeInTheDocument();
    });

    it('debe mostrar las estadísticas cuando están disponibles', () => {
      // Mock específico para el resumen
      mockUseQuery.mockReturnValueOnce({
        data: mockResumenRRHH,
        isLoading: false,
        error: null
      });

      render(<RRHH />, { wrapper: createWrapper() });

      expect(screen.getByText('150')).toBeInTheDocument(); // Total empleados
      expect(screen.getByText('140')).toBeInTheDocument(); // Activos
      expect(screen.getByText('10')).toBeInTheDocument(); // Inactivos
      expect(screen.getByText('8')).toBeInTheDocument(); // Vacaciones + licencia
    });

    it('debe mostrar mensaje de carga cuando isLoading es true', () => {
      mockUseQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null
      });

      render(<RRHH />, { wrapper: createWrapper() });

      expect(screen.getByText('Cargando datos de Recursos Humanos...')).toBeInTheDocument();
    });

    it('debe mostrar mensaje de error cuando hay un error', () => {
      mockUseQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: new Error('Error de conexión')
      });

      render(<RRHH />, { wrapper: createWrapper() });

      expect(screen.getByText('Error al cargar algunos datos. Por favor, intente más tarde.')).toBeInTheDocument();
    });
  });

  describe('Funcionalidad de búsqueda y filtros', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockEmpleados,
        isLoading: false,
        error: null
      });
    });

    it('debe permitir buscar empleados', async () => {
      const user = userEvent.setup();
      render(<RRHH />, { wrapper: createWrapper() });

      // Encontrar el campo de búsqueda
      const searchInput = screen.getByPlaceholderText('Buscar...');
      expect(searchInput).toBeInTheDocument();

      // Escribir en el campo de búsqueda
      await user.type(searchInput, 'Juan');

      // Verificar que el valor se actualice
      expect(searchInput).toHaveValue('Juan');
    });

    it('debe filtrar empleados por estado', async () => {
      const user = userEvent.setup();
      render(<RRHH />, { wrapper: createWrapper() });

      // Encontrar el select de estado
      const estadoSelect = screen.getByDisplayValue('Todos');
      expect(estadoSelect).toBeInTheDocument();

      // Cambiar el filtro
      await user.selectOptions(estadoSelect, 'Activo');
      expect(estadoSelect).toHaveValue('Activo');
    });
  });

  describe('Lista de empleados', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockEmpleados,
        isLoading: false,
        error: null
      });
    });

    it('debe mostrar la lista de empleados', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que se muestren los empleados
      expect(screen.getByText('Juan Carlos Pérez García')).toBeInTheDocument();
      expect(screen.getByText('María Elena González López')).toBeInTheDocument();
      expect(screen.getByText('Médico General')).toBeInTheDocument();
      expect(screen.getByText('Enfermera Jefe')).toBeInTheDocument();
    });

    it('debe mostrar los estados de los empleados', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que se muestren los estados
      const estadosActivos = screen.getAllByText('Activo');
      expect(estadosActivos).toHaveLength(2); // Ambos empleados están activos
    });

    it('debe mostrar botones de acción para cada empleado', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que haya botones de editar y eliminar
      const editButtons = screen.getAllByTitle('Editar');
      const deleteButtons = screen.getAllByTitle('Eliminar');
      
      expect(editButtons).toHaveLength(2);
      expect(deleteButtons).toHaveLength(2);
    });
  });

  describe('Funcionalidad de agregar empleado', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockEmpleados,
        isLoading: false,
        error: null
      });
    });

    it('debe mostrar el botón para agregar empleado', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      const addButton = screen.getByText('Agregar Empleado');
      expect(addButton).toBeInTheDocument();
    });

    it('debe abrir el modal al hacer clic en agregar empleado', async () => {
      const user = userEvent.setup();
      render(<RRHH />, { wrapper: createWrapper() });

      const addButton = screen.getByText('Agregar Empleado');
      await user.click(addButton);

      // Nota: El modal se renderiza condicionalmente, 
      // este test verificaría que se llame la función correcta
      expect(addButton).toBeInTheDocument();
    });
  });

  describe('Responsividad', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockEmpleados,
        isLoading: false,
        error: null
      });
    });

    it('debe mostrar la tabla con scroll horizontal en pantallas pequeñas', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que la tabla tenga la clase de overflow
      const tableContainer = screen.getByRole('table').closest('.overflow-x-auto');
      expect(tableContainer).toBeInTheDocument();
    });

    it('debe mostrar información condensada en móviles', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Verificar que existan elementos con clases responsive
      const mobileElements = document.querySelectorAll('.sm\\:hidden, .md\\:hidden, .lg\\:hidden');
      expect(mobileElements.length).toBeGreaterThan(0);
    });
  });

  describe('Manejo de datos vacíos', () => {
    it('debe mostrar mensaje cuando no hay empleados', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null
      });

      render(<RRHH />, { wrapper: createWrapper() });

      expect(screen.getByText('No se encontraron empleados que coincidan con los filtros.')).toBeInTheDocument();
    });
  });

  describe('Integración con modales', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue({
        data: mockEmpleados,
        isLoading: false,
        error: null
      });
    });

    it('debe renderizar los modales necesarios', () => {
      render(<RRHH />, { wrapper: createWrapper() });

      // Los modales se renderizan condicionalmente, pero sus componentes deben estar presentes
      // Verificar que el componente principal se renderice sin errores
      expect(screen.getByText('Recursos Humanos')).toBeInTheDocument();
    });
  });
});
