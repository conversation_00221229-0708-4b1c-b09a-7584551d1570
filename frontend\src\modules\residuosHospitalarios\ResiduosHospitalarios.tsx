import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import * as residuosService from '../../services/residuosHospitalariosService';
import { ResiduoHospitalario, CategoriaResiduo } from '../../types';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faFilter,
  faFileInvoice,
  faChartLine,
  faRecycle,
  faLeaf,
  faHospital
} from '@fortawesome/free-solid-svg-icons';
import { ResiduoHospitalarioModal } from './ResiduoHospitalarioModal';
import { ResiduoHospitalarioDetalleModal } from './ResiduoHospitalarioDetalleModal';

export const ResiduosHospitalarios = () => {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDetalleModalOpen, setIsDetalleModalOpen] = useState(false);
  const [selectedResiduo, setSelectedResiduo] = useState<ResiduoHospitalario | null>(null);
  const [filtroCategoria, setFiltroCategoria] = useState<CategoriaResiduo | ''>('');
  const [filtroArea, setFiltroArea] = useState('');
  const [filtroFechaInicio, setFiltroFechaInicio] = useState('');
  const [filtroFechaFin, setFiltroFechaFin] = useState('');

  // Obtener residuos hospitalarios
  const { data: residuos = [], isLoading } = useQuery({
    queryKey: ['residuosHospitalarios'],
    queryFn: residuosService.getResiduosHospitalarios
  });

  // Obtener resumen de residuos
  const { data: resumen } = useQuery({
    queryKey: ['resumenResiduos'],
    queryFn: residuosService.getResumenResiduos
  });

  // Mutación para eliminar residuo
  const eliminarResiduoMutation = useMutation({
    mutationFn: residuosService.deleteResiduoHospitalario,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['residuosHospitalarios'] });
      queryClient.invalidateQueries({ queryKey: ['resumenResiduos'] });
    }
  });

  // Filtrar residuos
  const residuosFiltrados = residuos.filter(residuo => {
    const matchCategoria = filtroCategoria ? residuo.categoria === filtroCategoria : true;
    const matchArea = filtroArea ? residuo.area_generadora.toLowerCase().includes(filtroArea.toLowerCase()) : true;
    
    let matchFechaInicio = true;
    if (filtroFechaInicio) {
      const fechaInicio = new Date(filtroFechaInicio);
      const fechaResiduo = new Date(residuo.fecha_generacion);
      matchFechaInicio = fechaResiduo >= fechaInicio;
    }
    
    let matchFechaFin = true;
    if (filtroFechaFin) {
      const fechaFin = new Date(filtroFechaFin);
      const fechaResiduo = new Date(residuo.fecha_generacion);
      matchFechaFin = fechaResiduo <= fechaFin;
    }
    
    return matchCategoria && matchArea && matchFechaInicio && matchFechaFin;
  });

  // Obtener áreas únicas para el filtro
  const areasUnicas = [...new Set(residuos.map(residuo => residuo.area_generadora))];

  // Manejar apertura del modal de detalle
  const handleVerDetalle = (residuo: ResiduoHospitalario) => {
    setSelectedResiduo(residuo);
    setIsDetalleModalOpen(true);
  };

  // Manejar apertura del modal de edición
  const handleEditar = (residuo: ResiduoHospitalario) => {
    setSelectedResiduo(residuo);
    setIsModalOpen(true);
  };

  // Manejar eliminación de residuo
  const handleEliminar = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar este registro de residuo hospitalario?')) {
      eliminarResiduoMutation.mutate(id);
    }
  };

  // Formatear fecha
  const formatearFecha = (fecha: string) => {
    return format(new Date(fecha), 'dd/MM/yyyy HH:mm', { locale: es });
  };

  // Obtener color según categoría
  const getColorCategoria = (categoria: CategoriaResiduo) => {
    switch (categoria) {
      case 'infeccioso':
        return 'text-red-500';
      case 'peligroso':
        return 'text-yellow-500';
      case 'común':
        return 'text-green-500';
      case 'biopeligroso':
        return 'text-purple-500';
      default:
        return '';
    }
  };

  return (
    <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Gestión de Residuos Hospitalarios</h1>
          <Button onClick={() => { setSelectedResiduo(null); setIsModalOpen(true); }}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Residuo
          </Button>
        </div>

        {/* Resumen */}
        {resumen && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-800 rounded-lg p-4 shadow-md">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faRecycle} className="text-2xl text-blue-500 mr-3" />
                <div>
                  <h3 className="text-gray-400 text-sm">Total Residuos</h3>
                  <p className="text-white text-xl font-bold">{resumen.total_kg.toFixed(2)} kg</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4 shadow-md">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faLeaf} className="text-2xl text-green-500 mr-3" />
                <div>
                  <h3 className="text-gray-400 text-sm">Residuos Comunes</h3>
                  <p className="text-white text-xl font-bold">{(resumen.por_categoria.común || 0).toFixed(2)} kg</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4 shadow-md">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faHospital} className="text-2xl text-red-500 mr-3" />
                <div>
                  <h3 className="text-gray-400 text-sm">Residuos Infecciosos</h3>
                  <p className="text-white text-xl font-bold">{(resumen.por_categoria.infeccioso || 0).toFixed(2)} kg</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4 shadow-md">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faChartLine} className="text-2xl text-purple-500 mr-3" />
                <div>
                  <h3 className="text-gray-400 text-sm">Áreas Generadoras</h3>
                  <p className="text-white text-xl font-bold">{Object.keys(resumen.por_area).length}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filtros */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-400 mb-1">Categoría</label>
              <Select
                value={filtroCategoria}
                onChange={(e) => setFiltroCategoria(e.target.value as CategoriaResiduo | '')}
                className="w-full"
              >
                <option value="">Todas</option>
                <option value="infeccioso">Infeccioso</option>
                <option value="peligroso">Peligroso</option>
                <option value="común">Común</option>
                <option value="biopeligroso">Biopeligroso</option>
              </Select>
            </div>
            
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-400 mb-1">Área</label>
              <Select
                value={filtroArea}
                onChange={(e) => setFiltroArea(e.target.value)}
                className="w-full"
              >
                <option value="">Todas</option>
                {areasUnicas.map((area) => (
                  <option key={area} value={area}>{area}</option>
                ))}
              </Select>
            </div>
            
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-400 mb-1">Fecha Inicio</label>
              <Input
                type="date"
                value={filtroFechaInicio}
                onChange={(e) => setFiltroFechaInicio(e.target.value)}
                className="w-full"
              />
            </div>
            
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-400 mb-1">Fecha Fin</label>
              <Input
                type="date"
                value={filtroFechaFin}
                onChange={(e) => setFiltroFechaFin(e.target.value)}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Tabla de residuos */}
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          {isLoading ? (
            <div className="p-4 text-center text-white">Cargando...</div>
          ) : residuosFiltrados.length === 0 ? (
            <div className="p-4 text-center text-white">No se encontraron residuos hospitalarios</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Fecha</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Categoría</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Descripción</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Peso (kg)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Área</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Disposición</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {residuosFiltrados.map((residuo) => (
                    <tr key={residuo.id} className="hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatearFecha(residuo.fecha_generacion)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`${getColorCategoria(residuo.categoria)} font-medium`}>
                          {residuo.categoria.charAt(0).toUpperCase() + residuo.categoria.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {residuo.descripcion}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {residuo.peso_kg.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {residuo.area_generadora}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {residuo.fecha_disposicion ? formatearFecha(residuo.fecha_disposicion) : 'Pendiente'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 space-x-2">
                        <button
                          onClick={() => handleVerDetalle(residuo)}
                          className="text-blue-500 hover:text-blue-400"
                          title="Ver detalle"
                        >
                          <FontAwesomeIcon icon={faFileInvoice} />
                        </button>
                        <button
                          onClick={() => handleEditar(residuo)}
                          className="text-yellow-500 hover:text-yellow-400"
                          title="Editar"
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                        <button
                          onClick={() => handleEliminar(residuo.id)}
                          className="text-red-500 hover:text-red-400"
                          title="Eliminar"
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Modal para crear/editar residuo */}
        {isModalOpen && (
          <ResiduoHospitalarioModal
            residuo={selectedResiduo}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: ['residuosHospitalarios'] });
              queryClient.invalidateQueries({ queryKey: ['resumenResiduos'] });
              setIsModalOpen(false);
            }}
          />
        )}

        {/* Modal para ver detalle de residuo */}
        {isDetalleModalOpen && selectedResiduo && (
          <ResiduoHospitalarioDetalleModal
            residuo={selectedResiduo}
            isOpen={isDetalleModalOpen}
            onClose={() => setIsDetalleModalOpen(false)}
          />
        )}
      </div>
  );
};

export default ResiduosHospitalarios;

