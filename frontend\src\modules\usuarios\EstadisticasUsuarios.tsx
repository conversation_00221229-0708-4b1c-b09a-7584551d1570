import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { usuariosService } from '../../services/usuariosService';
import { 
  Users, 
  UserCheck, 
  Shield, 
  Activity, 
  Clock,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON>hart
} from 'lucide-react';

const EstadisticasUsuarios: React.FC = () => {
  const { data: estadisticas, isLoading } = useQuery({
    queryKey: ['usuarios-estadisticas'],
    queryFn: () => usuariosService.getEstadisticas()
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!estadisticas) {
    return null;
  }

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Usuarios</p>
              <p className="text-2xl font-bold text-gray-900">{estadisticas.total_usuarios}</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600">+12% este mes</span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Usuarios Activos</p>
              <p className="text-2xl font-bold text-green-700">{estadisticas.usuarios_activos}</p>
              <div className="flex items-center mt-1">
                <span className="text-xs text-gray-600">
                  {((estadisticas.usuarios_activos / estadisticas.total_usuarios) * 100).toFixed(1)}% del total
                </span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <UserCheck className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Roles Únicos</p>
              <p className="text-2xl font-bold text-purple-700">{estadisticas.usuarios_por_rol.length}</p>
              <div className="flex items-center mt-1">
                <span className="text-xs text-gray-600">Roles configurados</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Shield className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Especialidades</p>
              <p className="text-2xl font-bold text-indigo-700">{estadisticas.usuarios_por_especialidad.length}</p>
              <div className="flex items-center mt-1">
                <span className="text-xs text-gray-600">Especialidades activas</span>
              </div>
            </div>
            <div className="p-3 bg-indigo-100 rounded-lg">
              <Activity className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Distribución por roles y especialidades */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usuarios por Rol */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              Distribución por Rol
            </h3>
          </div>
          <div className="space-y-4">
            {estadisticas.usuarios_por_rol.map((item, index) => {
              const porcentaje = (item.cantidad / estadisticas.total_usuarios) * 100;
              const colores = [
                'bg-blue-500',
                'bg-green-500',
                'bg-purple-500',
                'bg-yellow-500',
                'bg-red-500',
                'bg-indigo-500',
                'bg-pink-500'
              ];
              
              return (
                <div key={item.rol_nombre} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${colores[index % colores.length]}`}></div>
                    <span className="text-sm font-medium text-gray-900">{item.rol_nombre}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${colores[index % colores.length]}`}
                        style={{ width: `${porcentaje}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-8 text-right">{item.cantidad}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Usuarios por Especialidad */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <PieChart className="w-5 h-5 text-green-600" />
              Distribución por Especialidad
            </h3>
          </div>
          <div className="space-y-4">
            {estadisticas.usuarios_por_especialidad.length > 0 ? (
              estadisticas.usuarios_por_especialidad.map((item, index) => {
                const totalConEspecialidad = estadisticas.usuarios_por_especialidad.reduce((sum, esp) => sum + esp.cantidad, 0);
                const porcentaje = (item.cantidad / totalConEspecialidad) * 100;
                const colores = [
                  'bg-emerald-500',
                  'bg-cyan-500',
                  'bg-violet-500',
                  'bg-orange-500',
                  'bg-rose-500',
                  'bg-teal-500'
                ];
                
                return (
                  <div key={item.especialidad} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${colores[index % colores.length]}`}></div>
                      <span className="text-sm font-medium text-gray-900">{item.especialidad}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${colores[index % colores.length]}`}
                          style={{ width: `${porcentaje}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 w-8 text-right">{item.cantidad}</span>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8">
                <Activity className="mx-auto h-8 w-8 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">No hay usuarios con especialidades asignadas</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Actividad reciente */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Clock className="w-5 h-5 text-orange-600" />
            Último Acceso de Usuarios
          </h3>
        </div>
        <div className="space-y-3">
          {estadisticas.ultimo_acceso.map((item) => (
            <div key={item.usuario_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-700">
                    {item.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{item.username}</p>
                  <p className="text-xs text-gray-500">Usuario ID: {item.usuario_id}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-900">{formatearFecha(item.ultimo_acceso)}</p>
                <p className="text-xs text-gray-500">Último acceso</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Métricas adicionales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center">
            <div className="p-3 bg-yellow-100 rounded-lg inline-flex mb-3">
              <Users className="w-6 h-6 text-yellow-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {estadisticas.usuarios_por_rol.find(r => r.rol_nombre === 'Médico')?.cantidad || 0}
            </p>
            <p className="text-sm text-gray-600">Médicos Registrados</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center">
            <div className="p-3 bg-green-100 rounded-lg inline-flex mb-3">
              <UserCheck className="w-6 h-6 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {estadisticas.usuarios_por_rol.find(r => r.rol_nombre === 'Enfermera')?.cantidad || 0}
            </p>
            <p className="text-sm text-gray-600">Enfermeras Registradas</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center">
            <div className="p-3 bg-purple-100 rounded-lg inline-flex mb-3">
              <Shield className="w-6 h-6 text-purple-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {estadisticas.usuarios_por_rol.filter(r => 
                r.rol_nombre.includes('Administrador')
              ).reduce((sum, r) => sum + r.cantidad, 0)}
            </p>
            <p className="text-sm text-gray-600">Administradores</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EstadisticasUsuarios;
