import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { usuariosService } from '../../services/usuariosService';
import { Usuario, UsuarioFormData, UsuarioEditData, ESPECIALIDADES } from '../../types/usuarios';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { X, Save, Eye, EyeOff } from 'lucide-react';

interface FormularioUsuarioProps {
  usuario?: Usuario | null;
  onClose: () => void;
  onSuccess: () => void;
}

const FormularioUsuario: React.FC<FormularioUsuarioProps> = ({
  usuario,
  onClose,
  onSuccess
}) => {
  const queryClient = useQueryClient();
  const esEdicion = !!usuario;

  const [formData, setFormData] = useState<UsuarioFormData>({
    tipo_identificacion_id: usuario?.tipo_identificacion_id || 1,
    numero_identificacion: usuario?.numero_identificacion || '',
    username: usuario?.username || '',
    password: '',
    confirm_password: '',
    rol_id: usuario?.rol_id || 3,
    email: usuario?.email || '',
    especialidad: usuario?.especialidad
  });

  const [errores, setErrores] = useState<Record<string, string>>({});
  const [mostrarPassword, setMostrarPassword] = useState(false);
  const [mostrarConfirmPassword, setMostrarConfirmPassword] = useState(false);

  // Queries
  const { data: roles = [] } = useQuery({
    queryKey: ['roles'],
    queryFn: () => usuariosService.getRoles()
  });

  const { data: tiposIdentificacion = [] } = useQuery({
    queryKey: ['tipos-identificacion'],
    queryFn: () => usuariosService.getTiposIdentificacion()
  });

  // Mutations
  const crearMutation = useMutation({
    mutationFn: (data: UsuarioFormData) => usuariosService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['usuarios'] });
      queryClient.invalidateQueries({ queryKey: ['usuarios-estadisticas'] });
      onSuccess();
    }
  });

  const actualizarMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UsuarioEditData }) => 
      usuariosService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['usuarios'] });
      queryClient.invalidateQueries({ queryKey: ['usuarios-estadisticas'] });
      onSuccess();
    }
  });

  const handleChange = (field: keyof UsuarioFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errores[field]) {
      setErrores(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validarFormulario = (): boolean => {
    const nuevosErrores: Record<string, string> = {};

    // Validaciones básicas
    if (!formData.numero_identificacion.trim()) {
      nuevosErrores.numero_identificacion = 'El número de identificación es requerido';
    }

    if (!formData.username.trim()) {
      nuevosErrores.username = 'El nombre de usuario es requerido';
    } else if (formData.username.length < 3) {
      nuevosErrores.username = 'El nombre de usuario debe tener al menos 3 caracteres';
    }

    if (!formData.email.trim()) {
      nuevosErrores.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      nuevosErrores.email = 'El email no tiene un formato válido';
    }

    // Validaciones de contraseña (solo para creación o si se está cambiando)
    if (!esEdicion || formData.password) {
      if (!formData.password) {
        nuevosErrores.password = 'La contraseña es requerida';
      } else if (formData.password.length < 6) {
        nuevosErrores.password = 'La contraseña debe tener al menos 6 caracteres';
      }

      if (formData.password !== formData.confirm_password) {
        nuevosErrores.confirm_password = 'Las contraseñas no coinciden';
      }
    }

    setErrores(nuevosErrores);
    return Object.keys(nuevosErrores).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validarFormulario()) {
      return;
    }

    if (esEdicion && usuario) {
      const dataEdicion: UsuarioEditData = {
        tipo_identificacion_id: formData.tipo_identificacion_id,
        numero_identificacion: formData.numero_identificacion,
        username: formData.username,
        rol_id: formData.rol_id,
        email: formData.email,
        especialidad: formData.especialidad
      };

      // Solo incluir contraseña si se está cambiando
      if (formData.password) {
        dataEdicion.password = formData.password;
        dataEdicion.confirm_password = formData.confirm_password;
      }

      actualizarMutation.mutate({ id: usuario.id, data: dataEdicion });
    } else {
      crearMutation.mutate(formData);
    }
  };

  const rolSeleccionado = roles.find(r => r.id === formData.rol_id);
  const requiereEspecialidad = rolSeleccionado?.nombre === 'Médico' || rolSeleccionado?.nombre === 'Enfermera';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              {esEdicion ? 'Editar Usuario' : 'Crear Nuevo Usuario'}
            </h3>
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Tipo de Identificación */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Identificación *
                </label>
                <Select
                  value={formData.tipo_identificacion_id.toString()}
                  onChange={(e) => handleChange('tipo_identificacion_id', parseInt(e.target.value))}
                  className={errores.tipo_identificacion_id ? 'border-red-500' : ''}
                >
                  {tiposIdentificacion.map(tipo => (
                    <option key={tipo.id} value={tipo.id.toString()}>
                      {tipo.codigo} - {tipo.descripcion}
                    </option>
                  ))}
                </Select>
                {errores.tipo_identificacion_id && (
                  <p className="mt-1 text-sm text-red-600">{errores.tipo_identificacion_id}</p>
                )}
              </div>

              {/* Número de Identificación */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Número de Identificación *
                </label>
                <Input
                  type="text"
                  value={formData.numero_identificacion}
                  onChange={(e) => handleChange('numero_identificacion', e.target.value)}
                  className={errores.numero_identificacion ? 'border-red-500' : ''}
                  placeholder="Ingrese el número de identificación"
                />
                {errores.numero_identificacion && (
                  <p className="mt-1 text-sm text-red-600">{errores.numero_identificacion}</p>
                )}
              </div>

              {/* Nombre de Usuario */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de Usuario *
                </label>
                <Input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleChange('username', e.target.value)}
                  className={errores.username ? 'border-red-500' : ''}
                  placeholder="Ingrese el nombre de usuario"
                />
                {errores.username && (
                  <p className="mt-1 text-sm text-red-600">{errores.username}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className={errores.email ? 'border-red-500' : ''}
                  placeholder="<EMAIL>"
                />
                {errores.email && (
                  <p className="mt-1 text-sm text-red-600">{errores.email}</p>
                )}
              </div>

              {/* Rol */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rol *
                </label>
                <Select
                  value={formData.rol_id.toString()}
                  onChange={(e) => handleChange('rol_id', parseInt(e.target.value))}
                  className={errores.rol_id ? 'border-red-500' : ''}
                >
                  {roles.map(rol => (
                    <option key={rol.id} value={rol.id.toString()}>
                      {rol.nombre}
                    </option>
                  ))}
                </Select>
                {errores.rol_id && (
                  <p className="mt-1 text-sm text-red-600">{errores.rol_id}</p>
                )}
              </div>

              {/* Especialidad (condicional) */}
              {requiereEspecialidad && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Especialidad {rolSeleccionado?.nombre === 'Médico' ? '*' : ''}
                  </label>
                  <Select
                    value={formData.especialidad || ''}
                    onChange={(e) => handleChange('especialidad', e.target.value)}
                    className={errores.especialidad ? 'border-red-500' : ''}
                  >
                    <option value="">Seleccione una especialidad</option>
                    {ESPECIALIDADES.map(esp => (
                      <option key={esp.value} value={esp.value}>
                        {esp.label}
                      </option>
                    ))}
                  </Select>
                  {errores.especialidad && (
                    <p className="mt-1 text-sm text-red-600">{errores.especialidad}</p>
                  )}
                </div>
              )}
            </div>

            {/* Contraseñas */}
            <div className="border-t pt-6">
              <h4 className="text-md font-medium text-gray-900 mb-4">
                {esEdicion ? 'Cambiar Contraseña (opcional)' : 'Contraseña *'}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Contraseña */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {esEdicion ? 'Nueva Contraseña' : 'Contraseña *'}
                  </label>
                  <div className="relative">
                    <Input
                      type={mostrarPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleChange('password', e.target.value)}
                      className={errores.password ? 'border-red-500 pr-10' : 'pr-10'}
                      placeholder={esEdicion ? 'Dejar vacío para mantener actual' : 'Ingrese la contraseña'}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setMostrarPassword(!mostrarPassword)}
                    >
                      {mostrarPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errores.password && (
                    <p className="mt-1 text-sm text-red-600">{errores.password}</p>
                  )}
                </div>

                {/* Confirmar Contraseña */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {esEdicion ? 'Confirmar Nueva Contraseña' : 'Confirmar Contraseña *'}
                  </label>
                  <div className="relative">
                    <Input
                      type={mostrarConfirmPassword ? 'text' : 'password'}
                      value={formData.confirm_password}
                      onChange={(e) => handleChange('confirm_password', e.target.value)}
                      className={errores.confirm_password ? 'border-red-500 pr-10' : 'pr-10'}
                      placeholder="Confirme la contraseña"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setMostrarConfirmPassword(!mostrarConfirmPassword)}
                    >
                      {mostrarConfirmPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errores.confirm_password && (
                    <p className="mt-1 text-sm text-red-600">{errores.confirm_password}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Botones */}
            <div className="flex justify-end gap-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={crearMutation.isPending || actualizarMutation.isPending}
              >
                <Save className="w-4 h-4 mr-2" />
                {crearMutation.isPending || actualizarMutation.isPending
                  ? (esEdicion ? 'Actualizando...' : 'Creando...')
                  : (esEdicion ? 'Actualizar Usuario' : 'Crear Usuario')
                }
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FormularioUsuario;
