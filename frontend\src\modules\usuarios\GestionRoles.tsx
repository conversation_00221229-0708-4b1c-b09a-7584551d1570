import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usuariosService } from '../../services/usuariosService';
import { Rol, PERMISOS_SISTEMA, ROLES_PREDEFINIDOS } from '../../types/usuarios';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { 
  Shield, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Users,
  Settings,
  Check,
  X,
  Lock,
  Unlock
} from 'lucide-react';

const GestionRoles: React.FC = () => {
  const queryClient = useQueryClient();
  const [modalAbierto, setModalAbierto] = useState<string | null>(null);
  const [rolSeleccionado, setRolSeleccionado] = useState<Rol | null>(null);

  // Query para obtener roles
  const { data: roles = [], isLoading } = useQuery({
    queryKey: ['roles'],
    queryFn: () => usuariosService.getRoles()
  });

  const obtenerColorRol = (rolNombre: string) => {
    const colores: Record<string, string> = {
      'Super Administrador': 'bg-red-100 text-red-800 border-red-200',
      'Administrador': 'bg-purple-100 text-purple-800 border-purple-200',
      'Médico': 'bg-blue-100 text-blue-800 border-blue-200',
      'Enfermera': 'bg-green-100 text-green-800 border-green-200',
      'Recepcionista': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'Farmaceuta': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'Contador': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colores[rolNombre] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const contarPermisos = (permisos: Record<string, any>) => {
    if (permisos.all) return PERMISOS_SISTEMA.length;
    return Object.keys(permisos).length;
  };

  const handleVerDetalles = (rol: Rol) => {
    setRolSeleccionado(rol);
    setModalAbierto('ver');
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="ml-3 text-gray-600">Cargando roles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="w-6 h-6 text-purple-600" />
            Gestión de Roles y Permisos
          </h1>
          <p className="text-gray-600 mt-1">
            Configura los roles del sistema y sus permisos asociados
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configurar
          </Button>
          <Button onClick={() => setModalAbierto('crear')}>
            <Plus className="w-4 h-4 mr-2" />
            Nuevo Rol
          </Button>
        </div>
      </div>

      {/* Estadísticas de roles */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Roles</p>
              <p className="text-2xl font-bold text-gray-900">{roles.length}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Shield className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Roles Administrativos</p>
              <p className="text-2xl font-bold text-red-700">
                {roles.filter(r => r.nombre.includes('Administrador')).length}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <Lock className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Roles Clínicos</p>
              <p className="text-2xl font-bold text-blue-700">
                {roles.filter(r => ['Médico', 'Enfermera'].includes(r.nombre)).length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Roles Operativos</p>
              <p className="text-2xl font-bold text-green-700">
                {roles.filter(r => !r.nombre.includes('Administrador') && !['Médico', 'Enfermera'].includes(r.nombre)).length}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Unlock className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Lista de roles */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Roles del Sistema</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {roles.map((rol) => (
            <div key={rol.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 rounded-lg bg-purple-100 flex items-center justify-center">
                      <Shield className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-1">
                      <h4 className="text-lg font-semibold text-gray-900">{rol.nombre}</h4>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${obtenerColorRol(rol.nombre)}`}>
                        {rol.nombre}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{rol.descripcion}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <Settings className="w-3 h-3" />
                        {contarPermisos(rol.permisos)} permisos
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        Hospital ID: {rol.hospital_id}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleVerDetalles(rol)}
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Ver
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setRolSeleccionado(rol);
                      setModalAbierto('editar');
                    }}
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Editar
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setRolSeleccionado(rol);
                      setModalAbierto('eliminar');
                    }}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    Eliminar
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {roles.length === 0 && (
          <div className="text-center py-12">
            <Shield className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay roles configurados</h3>
            <p className="mt-1 text-sm text-gray-500">
              Comienza creando un nuevo rol para el sistema.
            </p>
            <div className="mt-6">
              <Button onClick={() => setModalAbierto('crear')}>
                <Plus className="w-4 h-4 mr-2" />
                Crear Primer Rol
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Modal Ver Detalles */}
      {modalAbierto === 'ver' && rolSeleccionado && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  Detalles del Rol: {rolSeleccionado.nombre}
                </h3>
                <Button variant="outline" onClick={() => setModalAbierto(null)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-6">
                {/* Información básica */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nombre del Rol
                    </label>
                    <p className="text-sm text-gray-900">{rolSeleccionado.nombre}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hospital ID
                    </label>
                    <p className="text-sm text-gray-900">{rolSeleccionado.hospital_id}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descripción
                  </label>
                  <p className="text-sm text-gray-900">{rolSeleccionado.descripcion}</p>
                </div>

                {/* Permisos */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Permisos Asignados ({contarPermisos(rolSeleccionado.permisos)})
                  </label>
                  
                  {rolSeleccionado.permisos.all ? (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center gap-2">
                        <Check className="w-5 h-5 text-green-600" />
                        <span className="font-medium text-green-800">Acceso Completo</span>
                      </div>
                      <p className="text-sm text-green-700 mt-1">
                        Este rol tiene acceso a todas las funcionalidades del sistema.
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {PERMISOS_SISTEMA.map((permiso) => {
                        const tienePermiso = Object.keys(rolSeleccionado.permisos).some(key => 
                          permiso.includes(key) || rolSeleccionado.permisos[key]
                        );
                        
                        return (
                          <div 
                            key={permiso} 
                            className={`flex items-center gap-2 p-2 rounded border ${
                              tienePermiso 
                                ? 'bg-green-50 border-green-200 text-green-800' 
                                : 'bg-gray-50 border-gray-200 text-gray-500'
                            }`}
                          >
                            {tienePermiso ? (
                              <Check className="w-4 h-4 text-green-600" />
                            ) : (
                              <X className="w-4 h-4 text-gray-400" />
                            )}
                            <span className="text-xs font-medium">{permiso}</span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setModalAbierto('editar');
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Editar Rol
                </Button>
                <Button variant="outline" onClick={() => setModalAbierto(null)}>
                  Cerrar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionRoles;
