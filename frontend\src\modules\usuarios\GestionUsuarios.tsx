import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usuariosService } from '../../services/usuariosService';
import { Usuario, FiltrosUsuarios, Rol, TipoIdentificacion } from '../../types/usuarios';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import FormularioUsuario from './FormularioUsuario';
import EstadisticasUsuarios from './EstadisticasUsuarios';
import GestionRoles from './GestionRoles';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  Key,
  UserCheck,
  Shield,
  Mail,
  Phone,
  Calendar,
  MoreVertical,
  Download,
  RefreshCw
} from 'lucide-react';

const GestionUsuarios: React.FC = () => {
  const queryClient = useQueryClient();
  const [filtros, setFiltros] = useState<FiltrosUsuarios>({});
  const [mostrarFiltros, setMostrarFiltros] = useState(false);
  const [usuarioSeleccionado, setUsuarioSeleccionado] = useState<Usuario | null>(null);
  const [modalAbierto, setModalAbierto] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('usuarios');

  // Queries
  const { data: usuarios = [], isLoading, error } = useQuery({
    queryKey: ['usuarios', filtros],
    queryFn: () => usuariosService.getAll(filtros)
  });

  const { data: roles = [] } = useQuery({
    queryKey: ['roles'],
    queryFn: () => usuariosService.getRoles()
  });

  const { data: tiposIdentificacion = [] } = useQuery({
    queryKey: ['tipos-identificacion'],
    queryFn: () => usuariosService.getTiposIdentificacion()
  });

  const { data: estadisticas } = useQuery({
    queryKey: ['usuarios-estadisticas'],
    queryFn: () => usuariosService.getEstadisticas()
  });

  // Mutations
  const eliminarMutation = useMutation({
    mutationFn: (id: number) => usuariosService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['usuarios'] });
      queryClient.invalidateQueries({ queryKey: ['usuarios-estadisticas'] });
      setModalAbierto(null);
    }
  });

  const resetearPasswordMutation = useMutation({
    mutationFn: (id: number) => usuariosService.resetearPassword(id),
    onSuccess: (passwordTemporal) => {
      alert(`Contraseña reseteada. Nueva contraseña temporal: ${passwordTemporal}`);
      setModalAbierto(null);
    }
  });

  // Handlers
  const handleBusqueda = (valor: string) => {
    setFiltros(prev => ({ ...prev, busqueda: valor }));
  };

  const handleFiltroRol = (rolId: string) => {
    setFiltros(prev => ({ 
      ...prev, 
      rol_id: rolId ? parseInt(rolId) : undefined 
    }));
  };

  const handleEliminar = (usuario: Usuario) => {
    setUsuarioSeleccionado(usuario);
    setModalAbierto('eliminar');
  };

  const handleResetearPassword = (usuario: Usuario) => {
    setUsuarioSeleccionado(usuario);
    setModalAbierto('resetear-password');
  };

  const confirmarEliminar = () => {
    if (usuarioSeleccionado) {
      eliminarMutation.mutate(usuarioSeleccionado.id);
    }
  };

  const confirmarResetearPassword = () => {
    if (usuarioSeleccionado) {
      resetearPasswordMutation.mutate(usuarioSeleccionado.id);
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const obtenerColorRol = (rolNombre: string) => {
    const colores: Record<string, string> = {
      'Super Administrador': 'bg-red-100 text-red-800',
      'Administrador': 'bg-purple-100 text-purple-800',
      'Médico': 'bg-blue-100 text-blue-800',
      'Enfermera': 'bg-green-100 text-green-800',
      'Recepcionista': 'bg-yellow-100 text-yellow-800',
      'Farmaceuta': 'bg-indigo-100 text-indigo-800',
      'Contador': 'bg-gray-100 text-gray-800'
    };
    return colores[rolNombre] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="ml-3 text-gray-600">Cargando usuarios...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">Error al cargar los usuarios</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'usuarios', label: 'Usuarios', icon: Users },
    { id: 'estadisticas', label: 'Estadísticas', icon: UserCheck },
    { id: 'roles', label: 'Roles y Permisos', icon: Shield }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Users className="w-6 h-6 text-blue-600" />
            Gestión de Usuarios
          </h1>
          <p className="text-gray-600 mt-1">
            Administra los usuarios del sistema, roles y permisos
          </p>
        </div>
        <div className="flex items-center gap-3">
          {activeTab === 'usuarios' && (
            <>
              <Button variant="outline" onClick={() => setMostrarFiltros(!mostrarFiltros)}>
                <Filter className="w-4 h-4 mr-2" />
                Filtros
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Exportar
              </Button>
              <Button onClick={() => setModalAbierto('crear')}>
                <Plus className="w-4 h-4 mr-2" />
                Nuevo Usuario
              </Button>
            </>
          )}
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Pestañas */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenido de las pestañas */}
      {activeTab === 'usuarios' && (
        <div className="space-y-6">
          {/* Estadísticas */}
          {estadisticas && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Usuarios</p>
                <p className="text-2xl font-bold text-gray-900">{estadisticas.total_usuarios}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Usuarios Activos</p>
                <p className="text-2xl font-bold text-green-700">{estadisticas.usuarios_activos}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <UserCheck className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Roles Activos</p>
                <p className="text-2xl font-bold text-purple-700">{roles.length}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Shield className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Médicos</p>
                <p className="text-2xl font-bold text-blue-700">
                  {estadisticas.usuarios_por_rol.find(r => r.rol_nombre === 'Médico')?.cantidad || 0}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <UserCheck className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filtros */}
      {mostrarFiltros && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Buscar
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar por nombre, email o documento..."
                  className="pl-10"
                  value={filtros.busqueda || ''}
                  onChange={(e) => handleBusqueda(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rol
              </label>
              <Select
                value={filtros.rol_id?.toString() || ''}
                onChange={(e) => handleFiltroRol(e.target.value)}
              >
                <option value="">Todos los roles</option>
                {roles.map(rol => (
                  <option key={rol.id} value={rol.id.toString()}>
                    {rol.nombre}
                  </option>
                ))}
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => setFiltros({})}
                className="w-full"
              >
                Limpiar Filtros
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Tabla de usuarios */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Identificación
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rol
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Especialidad
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha Creación
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {usuarios.map((usuario) => (
                <tr key={usuario.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-700">
                            {usuario.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {usuario.username}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="w-3 h-3 mr-1" />
                          {usuario.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {usuario.tipo_identificacion?.codigo} {usuario.numero_identificacion}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${obtenerColorRol(usuario.rol?.nombre || '')}`}>
                      {usuario.rol?.nombre}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {usuario.especialidad || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {formatearFecha(usuario.created_at)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setUsuarioSeleccionado(usuario);
                          setModalAbierto('ver');
                        }}
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setUsuarioSeleccionado(usuario);
                          setModalAbierto('editar');
                        }}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResetearPassword(usuario)}
                      >
                        <Key className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEliminar(usuario)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {usuarios.length === 0 && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay usuarios</h3>
            <p className="mt-1 text-sm text-gray-500">
              Comienza creando un nuevo usuario.
            </p>
            <div className="mt-6">
              <Button onClick={() => setModalAbierto('crear')}>
                <Plus className="w-4 h-4 mr-2" />
                Nuevo Usuario
              </Button>
            </div>
          </div>
        )}
        </div>
      )}

      {/* Pestaña Estadísticas */}
      {activeTab === 'estadisticas' && (
        <EstadisticasUsuarios />
      )}

      {/* Pestaña Roles y Permisos */}
      {activeTab === 'roles' && (
        <GestionRoles />
      )}

      {/* Modales */}
      {modalAbierto === 'eliminar' && usuarioSeleccionado && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    Eliminar Usuario
                  </h3>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">
                  ¿Estás seguro de que deseas eliminar al usuario <strong>{usuarioSeleccionado.username}</strong>? 
                  Esta acción no se puede deshacer.
                </p>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setModalAbierto(null)}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={confirmarEliminar}
                  disabled={eliminarMutation.isPending}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {eliminarMutation.isPending ? 'Eliminando...' : 'Eliminar'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {modalAbierto === 'resetear-password' && usuarioSeleccionado && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <Key className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    Resetear Contraseña
                  </h3>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">
                  ¿Estás seguro de que deseas resetear la contraseña del usuario <strong>{usuarioSeleccionado.username}</strong>?
                  Se generará una contraseña temporal.
                </p>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setModalAbierto(null)}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={confirmarResetearPassword}
                  disabled={resetearPasswordMutation.isPending}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  {resetearPasswordMutation.isPending ? 'Reseteando...' : 'Resetear'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal Ver Usuario */}
      {modalAbierto === 'ver' && usuarioSeleccionado && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  Detalles del Usuario
                </h3>
                <Button
                  variant="outline"
                  onClick={() => setModalAbierto(null)}
                >
                  ×
                </Button>
              </div>

              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-xl font-medium text-blue-700">
                      {usuarioSeleccionado.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">
                      {usuarioSeleccionado.username}
                    </h4>
                    <p className="text-gray-600">{usuarioSeleccionado.email}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tipo de Identificación
                    </label>
                    <p className="text-sm text-gray-900">
                      {usuarioSeleccionado.tipo_identificacion?.descripcion}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Número de Identificación
                    </label>
                    <p className="text-sm text-gray-900">
                      {usuarioSeleccionado.numero_identificacion}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Rol
                    </label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${obtenerColorRol(usuarioSeleccionado.rol?.nombre || '')}`}>
                      {usuarioSeleccionado.rol?.nombre}
                    </span>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Especialidad
                    </label>
                    <p className="text-sm text-gray-900">
                      {usuarioSeleccionado.especialidad || 'No especificada'}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fecha de Creación
                    </label>
                    <p className="text-sm text-gray-900">
                      {formatearFecha(usuarioSeleccionado.created_at)}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Última Actualización
                    </label>
                    <p className="text-sm text-gray-900">
                      {formatearFecha(usuarioSeleccionado.updated_at)}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descripción del Rol
                  </label>
                  <p className="text-sm text-gray-600">
                    {usuarioSeleccionado.rol?.descripcion}
                  </p>
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setModalAbierto('editar');
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setModalAbierto(null)}
                >
                  Cerrar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal Crear/Editar Usuario */}
      {(modalAbierto === 'crear' || modalAbierto === 'editar') && (
        <FormularioUsuario
          usuario={modalAbierto === 'editar' ? usuarioSeleccionado : null}
          onClose={() => setModalAbierto(null)}
          onSuccess={() => setModalAbierto(null)}
        />
      )}
    </div>
  );
};

export default GestionUsuarios;
