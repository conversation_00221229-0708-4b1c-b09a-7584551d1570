# Módulo de Gestión de Usuarios

Este módulo proporciona funcionalidades completas para la gestión de usuarios, roles y permisos en el sistema Hipócrates/Genio.

## Características Principales

### 🔐 Gestión de Usuarios
- **CRUD completo** de usuarios
- **Validación de formularios** con mensajes de error
- **Filtros avanzados** por rol, especialidad y búsqueda
- **Exportación de datos** en múltiples formatos
- **Reseteo de contraseñas** con generación automática
- **Vista detallada** de información de usuario

### 📊 Estadísticas y Análisis
- **Dashboard de métricas** de usuarios
- **Distribución por roles** y especialidades
- **Actividad reciente** de usuarios
- **Gráficos interactivos** de distribución
- **KPIs principales** del sistema

### 🛡️ Gestión de Roles y Permisos
- **Configuración de roles** del sistema
- **Asignación de permisos** granular
- **Roles predefinidos** según el script SQL
- **Vista detallada** de permisos por rol
- **Gestión de accesos** por módulo

## Estructura del Módulo

```
src/modules/usuarios/
├── GestionUsuarios.tsx      # Componente principal con pestañas
├── FormularioUsuario.tsx    # Formulario crear/editar usuarios
├── EstadisticasUsuarios.tsx # Dashboard de estadísticas
├── GestionRoles.tsx         # Gestión de roles y permisos
├── index.ts                 # Exportaciones del módulo
└── README.md               # Documentación
```

## Tipos y Servicios

### Tipos TypeScript
- `Usuario` - Estructura principal del usuario
- `UsuarioFormData` - Datos para crear usuario
- `UsuarioEditData` - Datos para editar usuario
- `Rol` - Estructura de roles del sistema
- `TipoIdentificacion` - Tipos de documento
- `EstadisticasUsuarios` - Métricas del sistema

### Servicios
- `usuariosService` - API para operaciones CRUD
- Métodos: `getAll()`, `getById()`, `create()`, `update()`, `delete()`
- Métodos auxiliares: `getTiposIdentificacion()`, `getRoles()`, `getEstadisticas()`

## Integración con Base de Datos

El módulo está diseñado para integrarse con la estructura SQL definida en:
- `gestion_general.usuarios` - Tabla principal de usuarios
- `gestion_general.roles` - Tabla de roles del sistema
- `gestion_general.tipos_identificacion` - Tipos de documento
- `gestion_general.tipo_especialidad` - Especialidades médicas

### Campos Principales
- **Identificación**: Tipo y número de documento (encriptado)
- **Credenciales**: Username y password (hasheado con bcrypt)
- **Información**: Email (encriptado) y especialidad
- **Seguridad**: Roles y permisos por hospital
- **Auditoría**: Fechas de creación y actualización

## Características de Seguridad

### 🔒 Encriptación
- **Números de identificación** encriptados con AES-256-CBC
- **Emails** encriptados con AES-256-CBC
- **Contraseñas** hasheadas con bcrypt
- **Claves derivadas** de `gestion_general.claves_encriptacion`

### 🛡️ Control de Acceso
- **Row Level Security** habilitado en PostgreSQL
- **Políticas de acceso** por hospital
- **Permisos granulares** por módulo
- **Roles jerárquicos** del sistema

### 🔐 Validaciones
- **Formato de email** validado
- **Longitud de contraseña** mínima
- **Unicidad de username** por hospital
- **Tipos de identificación** según normativas colombianas

## Roles Predefinidos

1. **Super Administrador** - Acceso completo al sistema
2. **Administrador** - Gestión del hospital
3. **Médico** - Funciones clínicas y consultas
4. **Enfermera** - Cuidados y seguimiento
5. **Recepcionista** - Gestión de citas y pacientes
6. **Farmaceuta** - Gestión de medicamentos
7. **Contador** - Funciones financieras

## Permisos del Sistema

### Módulos Principales
- `usuarios.*` - Gestión de usuarios
- `pacientes.*` - Gestión de pacientes
- `citas.*` - Sistema de citas
- `consultas.*` - Consultas médicas
- `cirugias.*` - Procedimientos quirúrgicos
- `hospitalizaciones.*` - Gestión de hospitalización
- `urgencias.*` - Atención de urgencias
- `inventario.*` - Control de inventario
- `farmacia.*` - Gestión farmacéutica
- `facturacion.*` - Facturación y cobros
- `reportes.*` - Generación de reportes
- `administracion.*` - Configuración del sistema

### Acciones por Módulo
- `crear` - Crear nuevos registros
- `leer` - Consultar información
- `actualizar` - Modificar registros
- `eliminar` - Eliminar registros

## Uso del Componente

```tsx
import { GestionUsuarios } from '../modules/usuarios';

// En AppRoutes.tsx
<Route
  path="/admin/users"
  element={
    <ProtectedRoute requiredRole="SUPER_ADMIN">
      <Layout>
        <GestionUsuarios />
      </Layout>
    </ProtectedRoute>
  }
/>
```

## Configuración de Desarrollo

### Mock Data
El servicio incluye datos simulados para desarrollo:
- 6 usuarios de ejemplo con diferentes roles
- 7 roles predefinidos del sistema
- 8 tipos de identificación según normativa colombiana
- Estadísticas calculadas dinámicamente

### Integración con Backend
Para conectar con el backend real:
1. Reemplazar `usuariosService` con llamadas HTTP reales
2. Configurar endpoints según la API REST
3. Implementar manejo de errores y autenticación
4. Configurar encriptación/desencriptación de campos

## Funcionalidades Futuras

- [ ] **Autenticación 2FA** - Factor de autenticación adicional
- [ ] **Logs de auditoría** - Registro de acciones de usuarios
- [ ] **Sesiones activas** - Control de sesiones concurrentes
- [ ] **Políticas de contraseña** - Configuración de complejidad
- [ ] **Notificaciones** - Alertas por email/SMS
- [ ] **Importación masiva** - Carga de usuarios desde Excel/CSV
- [ ] **API de integración** - Sincronización con sistemas externos
- [ ] **Dashboard avanzado** - Métricas en tiempo real
