import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from '../components/layout/Layout';
import NotFound from '../pages/NotFound';
import { useAuth } from '../context/AuthContext';
import { usePermissions, SystemModule } from '../hooks/usePermissions';

// Import missing modules for 404 routes
import { Hospitalizaciones } from '../modules/hospitalizaciones/Hospitalizaciones';
import { HospitalizacionNueva } from '../modules/hospitalizaciones/HospitalizacionNueva';
import { HospitalizacionDetalle } from '../modules/hospitalizaciones/HospitalizacionDetalle';
import { HospitalizacionEditar } from '../modules/hospitalizaciones/HospitalizacionEditar';

// Financial modules
import Facturacion from '../modules/facturacion/Facturacion';
import Contabilidad from '../modules/contabilidad/Contabilidad';
import { Presupuesto } from '../modules/presupuesto/Presupuesto';
import { ReportesFinancieros } from '../modules/reportes/ReportesFinancieros';

// Recursos Humanos
import { RRHH, RRHHIntegral } from '../modules/recursosHumanos';

// Proveedores
import Proveedores from '../modules/proveedores';

// Autorizaciones
import Autorizaciones from '../modules/autorizaciones';
import AutorizacionDetallePage from '../modules/autorizaciones/AutorizacionDetallePage';

// Incidentes Adversos
import IncidentesAdversos from '../modules/incidentes-adversos';
import IncidenteDetallePage from '../modules/incidentes-adversos/IncidenteDetallePage';

import { Login } from '../pages/auth/Login';
import Pacientes from '../modules/pacientes/Pacientes';
import PacienteNuevo from '../modules/pacientes/PacienteNuevo';
import PacienteEditar from '../modules/pacientes/PacienteEditar';
import PacienteDetalle from '../modules/pacientes/PacienteDetalle';
import AnaliticaPredictiva from '../modules/analitica-predictiva/AnaliticaPredictiva';
import ProveedoresYCompras from '../modules/proveedores-y-compras/ProveedoresYCompras';
import { HistoriasClinicas } from '../modules/historias-clinicas/HistoriasClinicas';
import { HistoriaClinicaNueva } from '../modules/historias-clinicas/HistoriaClinicaNueva';
import Laboratorio from '../modules/laboratorio/Laboratorio';
import BancoSangre from '../modules/banco-sangre/BancoSangre';
import Medicos from '../modules/medicos/Medicos';
import MedicoDetalle from '../modules/medicos/MedicoDetalle';
import Enfermeras from '../modules/enfermeras/Enfermeras';
import Telemedicina from '../modules/telemedicina/Telemedicina';
import Farmacia from '../modules/farmacia/Farmacia';
import Activos from '../modules/activos/Activos';
import Inventario from '../modules/inventario/Inventario';
import Medicamentos from '../modules/medicamentos/Medicamentos';
import Dispensaciones from '../modules/dispensaciones/Dispensaciones';
import ImagenesDiagnosticas from '../modules/imagenes-diagnosticas/ImagenesDiagnosticas';
import GestionIps from '../modules/gestion-ips/GestionIps';
import DashboardPrincipal from '../components/dashboard/DashboardPrincipal';
import ResiduosHospitalarios from '../modules/residuosHospitalarios/ResiduosHospitalarios';
import Administracion from '../modules/administracion/Administracion';
import AdministracionNegocio from '../modules/administracion-negocio/AdministracionNegocio';
import AdministracionNegocioSimple from '../modules/administracion-negocio/AdministracionNegocioSimple';
import TestComponent from '../modules/administracion-negocio/test';
import DebugAuth from '../modules/administracion-negocio/debug';
import { HistoriaClinicaDetalle } from '../modules/historias-clinicas/HistoriaClinicaDetalle';
import { ConfiguracionCIE11 } from '../modules/configuraciones/ConfiguracionCIE11';
import { EstadisticasCIE11 } from '../modules/reportes/EstadisticasCIE11';
import DiagnosticosCIE from '../modules/diagnosticos-cie/DiagnosticosCIE';
import { Consultas } from '../modules/consultas/Consultas';
import { ConsultaNueva } from '../modules/consultas/ConsultaNueva';
import { ConsultaEditar } from '../modules/consultas/ConsultaEditar';
import { ConsultaDetalle } from '../modules/consultas/ConsultaDetalle';
// Importaciones de Dispensaciones
import DispensacionDetalle from '../modules/dispensaciones/DispensacionDetalle';
import DispensacionForm from '../modules/dispensaciones/DispensacionForm';

// Importaciones de Urgencias
import { Urgencias } from '../modules/urgencias/Urgencias';
import { UrgenciaNueva } from '../modules/urgencias/UrgenciaNueva';
import { UrgenciaEditar } from '../modules/urgencias/UrgenciaEditar';
import { UrgenciaDetalle } from '../modules/urgencias/UrgenciaDetalle';
import DashboardUrgencias from '../modules/urgencias/DashboardUrgencias';
import { UrgenciasDebug } from '../modules/urgencias/UrgenciasDebug';

// Importaciones de Ambulancias
import { Ambulancias } from '../modules/ambulancias/Ambulancias';
import { AmbulanciaNueva } from '../modules/ambulancias/AmbulanciaNueva';
import { AmbulanciaEditar } from '../modules/ambulancias/AmbulanciaEditar';
import { AmbulanciaDetalle } from '../modules/ambulancias/AmbulanciaDetalle';
import { AmbulanciasMapa } from '../modules/ambulancias/AmbulanciasMapa';
import { AmbulanciaUbicacion } from '../modules/ambulancias/AmbulanciaUbicacion';
import AmbulanceDashboard from '../modules/ambulancias/Dashboard';
import AmbulanceMap from '../modules/ambulancias/AmbulanceMap';

// Importaciones de Quirófanos
import { Quirofanos } from '../modules/quirofanos/Quirofanos';
import { QuirofanoDetalle } from '../modules/quirofanos/QuirofanoDetalle';
import { QuirofanoAsignacion } from '../modules/quirofanos/QuirofanoAsignacion';
import { QuirofanoEstadisticas } from '../modules/quirofanos/QuirofanoEstadisticas';
import { QuirofanoReserva } from '../modules/quirofanos/QuirofanoReserva';
import { CirugiaDetalle as QuirofanoCirugiaDetalle } from '../modules/quirofanos/CirugiaDetalle';
import { CirugiaLista } from '../modules/quirofanos/CirugiaLista';

// Importaciones de Cirugías
import {
  Cirugias,
  CirugiasSimple,
  CirugiaDetalle as CirugiaDetalleIndependiente,
  CirugiaNueva,
  CirugiaEditar
} from '../modules/cirugias';


// Componente de carga
const Loading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
  </div>
);

// Ruta protegida con verificación de permisos
const ProtectedRoute = ({
  children,
  requiredModule,
  requiredRole
}: {
  children: React.ReactNode;
  requiredModule?: SystemModule;
  requiredRole?: string;
}) => {
  const { user, isLoading } = useAuth();
  const { hasModuleAccess, userRole, isSystemAdmin } = usePermissions();

  if (isLoading) return <Loading />;
  if (!user) return <Navigate to="/login" replace />;

  // Verificar rol requerido si se especifica
  if (requiredRole) {
    const hasRequiredRole = requiredRole === 'SUPER_ADMIN' ? isSystemAdmin() : userRole === requiredRole;

    if (!hasRequiredRole) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100">
          <div className="text-center p-8 bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-lg rounded-lg max-w-md">
            <div className="text-red-500 text-6xl mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
            <p className="text-gray-600 mb-4">
              No tienes permisos para acceder a esta sección.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Se requiere rol: <span className="font-semibold">{requiredRole}</span>
            </p>
            <button
              onClick={() => window.history.back()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Volver
            </button>
          </div>
        </div>
      );
    }
  }

  // Verificar permisos del módulo si se especifica
  if (requiredModule && !hasModuleAccess(requiredModule)) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100">
        <div className="text-center p-8 bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-lg rounded-lg max-w-md">
          <div className="text-red-500 text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-4">
            No tienes permisos para acceder a este módulo.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Módulo requerido: <span className="font-semibold">{requiredModule}</span>
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Volver
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

const AppRoutes = () => (
  <Routes>
    <Route path="/login" element={<Login />} />
    <Route path="/" element={<Navigate to="/dashboard" replace />} />

    <Route
      path="/dashboard"
      element={
        <ProtectedRoute>
          <Layout>
            <DashboardPrincipal />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes"
      element={
        <ProtectedRoute requiredModule="pacientes">
          <Layout>
            <Pacientes />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/analitica-predictiva"
      element={
        <ProtectedRoute>
          <Layout>
            <AnaliticaPredictiva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/proveedores-y-compras"
      element={
        <ProtectedRoute>
          <Layout>
            <ProveedoresYCompras />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Historias Clínicas */}
    <Route
      path="/historias-clinicas"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriasClinicas />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/historias-clinicas/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriaClinicaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/historias-clinicas/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriaClinicaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

        {/* Rutas de Pacientes */}
    <Route
      path="/pacientes/nuevo"
      element={
        <ProtectedRoute requiredModule="pacientes">
          <Layout>
            <PacienteNuevo />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes/:id/editar"
      element={
        <ProtectedRoute requiredModule="pacientes">
          <Layout>
            <PacienteEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes/:id"
      element={
        <ProtectedRoute requiredModule="pacientes">
          <Layout>
            <PacienteDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Consultas */}
    <Route
      path="/consultas"
      element={
        <ProtectedRoute>
          <Layout>
            <Consultas />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/:id/editar"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Urgencias */}
    <Route
      path="/urgencias"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <Urgencias />
        </ProtectedRoute>
      }
    />

    <Route
      path="/urgencias/dashboard"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <DashboardUrgencias />
        </ProtectedRoute>
      }
    />

    <Route
      path="/urgencias/debug"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <UrgenciasDebug />
        </ProtectedRoute>
      }
    />

    <Route
      path="/urgencias/nueva"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <UrgenciaNueva />
        </ProtectedRoute>
      }
    />

    <Route
      path="/urgencias/:id/editar"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <UrgenciaEditar />
        </ProtectedRoute>
      }
    />

    <Route
      path="/urgencias/:id"
      element={
        <ProtectedRoute requiredModule="urgencias">
          <Layout>
            <UrgenciaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Quirófanos */}
    <Route
      path="/quirofanos"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <Quirofanos />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/estadisticas"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <QuirofanoEstadisticas />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/asignacion"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <QuirofanoAsignacion />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/reserva"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <QuirofanoReserva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/cirugias"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <CirugiaLista />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/cirugias/:id"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <QuirofanoCirugiaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/quirofanos/:id"
      element={
        <ProtectedRoute requiredModule="quirofanos">
          <Layout>
            <QuirofanoDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Cirugías */}
    <Route
      path="/cirugias"
      element={
        <ProtectedRoute requiredModule="cirugias">
          <Layout>
            <Cirugias />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/cirugias/nueva"
      element={
        <ProtectedRoute requiredModule="cirugias">
          <Layout>
            <CirugiaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/cirugias/editar/:id"
      element={
        <ProtectedRoute requiredModule="cirugias">
          <Layout>
            <CirugiaEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/cirugias/ver/:id"
      element={
        <ProtectedRoute requiredModule="cirugias">
          <Layout>
            <CirugiaDetalleIndependiente />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Diagnósticos CIE */}
    <Route
      path="/diagnosticos-cie"
      element={
        <ProtectedRoute>
          <Layout>
            <DiagnosticosCIE />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Configuración CIE-11 */}
    <Route
      path="/configuraciones/cie11"
      element={
        <ProtectedRoute>
          <Layout>
            <ConfiguracionCIE11 />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Estadísticas CIE-11 */}
    <Route
      path="/reportes/cie11"
      element={
        <ProtectedRoute>
          <Layout>
            <EstadisticasCIE11 />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Dispensaciones */}
    <Route
      path="/dispensaciones"
      element={
        <ProtectedRoute>
          <Layout>
            <Dispensaciones />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionForm />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/editar/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionForm />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Laboratorio */}
    <Route
      path="/laboratorio"
      element={
        <ProtectedRoute requiredModule="laboratorio">
          <Laboratorio />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Imágenes Diagnósticas */}
    <Route
      path="/imagenes-diagnosticas"
      element={
        <ProtectedRoute requiredModule="imagenes-diagnosticas">
          <Layout>
            <ImagenesDiagnosticas />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Banco de Sangre */}
    <Route
      path="/banco-sangre"
      element={
        <ProtectedRoute requiredModule="banco-sangre">
          <BancoSangre />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Médicos */}
    <Route
      path="/medicos"
      element={
        <ProtectedRoute requiredModule="medicos">
          <Medicos />
        </ProtectedRoute>
      }
    />

    <Route
      path="/medicos/:id"
      element={
        <ProtectedRoute requiredModule="medicos">
          <MedicoDetalle />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Enfermeras */}
    <Route
      path="/enfermeras"
      element={
        <ProtectedRoute requiredModule="enfermeras">
          <Enfermeras />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Telemedicina */}
    <Route
      path="/telemedicina"
      element={
        <ProtectedRoute requiredModule="telemedicina">
          <Telemedicina />
        </ProtectedRoute>
      }
    />

    {/* Redirección de compatibilidad */}
    <Route
      path="/teleconsultas"
      element={<Navigate to="/telemedicina" replace />}
    />

    {/* Redirección de citas a consultas */}
    <Route
      path="/citas"
      element={<Navigate to="/consultas" replace />}
    />
    <Route
      path="/citas/*"
      element={<Navigate to="/consultas" replace />}
    />

    {/* Rutas de Farmacia */}
    <Route
      path="/farmacia"
      element={
        <ProtectedRoute requiredModule="farmacia">
          <Farmacia />
        </ProtectedRoute>
      }
    />

    {/* Ruta para detalles de medicamento */}
    <Route
      path="/farmacia/medicamentos/:id"
      element={
        <ProtectedRoute requiredModule="farmacia">
          <Farmacia />
        </ProtectedRoute>
      }
    />

    {/* Gestión de Medicamentos */}
    <Route
      path="/medicamentos"
      element={
        <ProtectedRoute requiredModule="farmacia">
          <Layout>
            <Medicamentos />
          </Layout>
        </ProtectedRoute>
      }
    />



    {/* Gestión de Inventario General */}
    <Route
      path="/inventario"
      element={
        <ProtectedRoute requiredModule="inventario">
          <Layout>
            <Inventario />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Gestión de Activos */}
    <Route
      path="/activos"
      element={
        <ProtectedRoute requiredModule="inventario">
          <Layout>
            <Activos />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Residuos Hospitalarios */}
    <Route
      path="/residuosHospitalarios"
      element={
        <ProtectedRoute>
          <ResiduosHospitalarios />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Hospitalizaciones */}
    <Route
      path="/hospitalizaciones"
      element={
        <ProtectedRoute>
          <Layout>
            <Hospitalizaciones />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/hospitalizaciones/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <HospitalizacionNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/hospitalizaciones/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <HospitalizacionDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/hospitalizaciones/:id/editar"
      element={
        <ProtectedRoute>
          <Layout>
            <HospitalizacionEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas Financieras */}
    <Route
      path="/facturacion"
      element={
        <ProtectedRoute>
          <Layout>
            <Facturacion />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/contabilidad"
      element={
        <ProtectedRoute>
          <Layout>
            <Contabilidad />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/presupuesto"
      element={
        <ProtectedRoute>
          <Layout>
            <Presupuesto />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/reportes/financieros"
      element={
        <ProtectedRoute>
          <Layout>
            <ReportesFinancieros />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Administración */}
    <Route
      path="/administracion"
      element={
        <ProtectedRoute>
          <Administracion />
        </ProtectedRoute>
      }
    />

    {/* Rutas de Administración - Usuarios */}
    <Route
      path="/admin/users"
      element={
        <ProtectedRoute requiredRole="SUPER_ADMIN">
          <Layout>
            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
              <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Gestión de Usuarios</h1>
                <div className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm rounded-lg p-6">
                  <p className="text-gray-600">Módulo de gestión de usuarios en desarrollo.</p>
                  <p className="text-sm text-gray-500 mt-2">Esta funcionalidad estará disponible próximamente.</p>
                </div>
              </div>
            </div>
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Administración - Roles */}
    <Route
      path="/admin/roles"
      element={
        <ProtectedRoute requiredRole="SUPER_ADMIN">
          <Layout>
            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
              <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Gestión de Roles</h1>
                <div className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm rounded-lg p-6">
                  <p className="text-gray-600">Módulo de gestión de roles y permisos en desarrollo.</p>
                  <p className="text-sm text-gray-500 mt-2">Esta funcionalidad estará disponible próximamente.</p>
                </div>
              </div>
            </div>
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Administración - Configuración */}
    <Route
      path="/admin/settings"
      element={
        <ProtectedRoute requiredRole="SUPER_ADMIN">
          <Layout>
            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 p-6">
              <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Configuración del Sistema</h1>
                <div className="bg-white/85 backdrop-blur-sm border border-gray-200/50 shadow-sm rounded-lg p-6">
                  <p className="text-gray-600">Módulo de configuración del sistema en desarrollo.</p>
                  <p className="text-sm text-gray-500 mt-2">Esta funcionalidad estará disponible próximamente.</p>
                </div>
              </div>
            </div>
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Gestión de IPS - Solo SUPER_ADMIN */}
    <Route
      path="/gestion-ips"
      element={
        <ProtectedRoute requiredModule="gestion-ips">
          <GestionIps />
        </ProtectedRoute>
      }
    />



    {/* Rutas de Administración de Negocio SaaS - Solo SUPER_ADMIN */}
    <Route
      path="/administracion-negocio"
      element={
        <ProtectedRoute requiredModule="administracion-negocio">
          <Layout>
            <AdministracionNegocioSimple />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Ruta temporal de test */}
    <Route
      path="/test-components"
      element={
        <ProtectedRoute>
          <Layout>
            <TestComponent />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Ruta temporal de debug */}
    <Route
      path="/debug-auth"
      element={
        <ProtectedRoute>
          <Layout>
            <DebugAuth />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas del módulo de Cirugías */}
    <Route
      path="/cirugias"
      element={
        <ProtectedRoute>
          <Layout>
            <CirugiasSimple />
          </Layout>
        </ProtectedRoute>
      }
    />
    
    <Route
      path="/cirugias/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <CirugiaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />
    
    <Route
      path="/cirugias/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <CirugiaDetalleIndependiente />
          </Layout>
        </ProtectedRoute>
      }
    />
    
    <Route
      path="/cirugias/editar/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <CirugiaEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Recursos Humanos */}
    <Route
      path="/recursos-humanos"
      element={
        <ProtectedRoute>
          <Layout>
            <RRHHIntegral />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/recursos-humanos/nuevo"
      element={
        <ProtectedRoute>
          <Layout>
            <RRHH />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/recursos-humanos/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <RRHH />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Proveedores */}
    <Route
      path="/proveedores"
      element={
        <ProtectedRoute>
          <Layout>
            <Proveedores />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Autorizaciones */}
    <Route
      path="/autorizaciones"
      element={
        <ProtectedRoute>
          <Layout>
            <Autorizaciones />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/autorizaciones/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <AutorizacionDetallePage />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Incidentes Adversos */}
    <Route
      path="/incidentes-adversos"
      element={
        <ProtectedRoute>
          <Layout>
            <IncidentesAdversos />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/incidentes-adversos/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <IncidenteDetallePage />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Ambulancias */}
    <Route
      path="/ambulancias"
      element={
        <ProtectedRoute>
          <Layout>
            <Ambulancias />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanciaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/:id/editar"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanciaEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/dashboard"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanceDashboard />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanciaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/mapa"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanciasMapa />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/mapa-tiempo-real"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanceMap />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/ambulancias/:id/ubicacion"
      element={
        <ProtectedRoute>
          <Layout>
            <AmbulanciaUbicacion />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route path="*" element={<NotFound />} />
  </Routes>
);

export default AppRoutes;
