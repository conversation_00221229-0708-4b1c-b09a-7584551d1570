import { api } from '../utils/api';

// Interfaces
export interface User {
  id: string;
  username: string;
  email: string;
  nombre: string;
  apellido: string;
  rol: string;
  hospital_id: number;
  activo: boolean;
  ultimo_acceso?: string;
  fecha_creacion: string;
  permisos?: string[];
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

export interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

export interface RefreshResponse {
  access_token: string;
  expires_in: number;
}

// Constantes
const ACCESS_TOKEN_KEY = 'hipocrates_access_token';
const REFRESH_TOKEN_KEY = 'hipocrates_refresh_token';
const USER_KEY = 'hipocrates_user';
const TOKEN_EXPIRY_KEY = 'hipocrates_token_expiry';

// Tiempo de buffer antes de que expire el token (5 minutos)
const REFRESH_BUFFER_TIME = 5 * 60 * 1000;

class AuthServiceAdvanced {
  private refreshPromise: Promise<string> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.setupTokenRefresh();
    this.setupAxiosInterceptors();
  }

  /**
   * Configurar interceptores de Axios para manejo automático de tokens
   */
  private setupAxiosInterceptors() {
    // Interceptor de request para agregar token
    api.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Interceptor de response para manejar tokens expirados
    api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return api(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Configurar refresh automático de tokens
   */
  private setupTokenRefresh() {
    const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (expiryTime) {
      const timeUntilExpiry = parseInt(expiryTime) - Date.now();
      const refreshTime = timeUntilExpiry - REFRESH_BUFFER_TIME;

      if (refreshTime > 0) {
        this.scheduleTokenRefresh(refreshTime);
      } else if (this.getRefreshToken()) {
        // Token ya expiró, intentar refresh inmediatamente
        this.refreshAccessToken().catch(() => this.logout());
      }
    }
  }

  /**
   * Programar refresh automático del token
   */
  private scheduleTokenRefresh(delay: number) {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    this.refreshTimer = setTimeout(async () => {
      try {
        await this.refreshAccessToken();
      } catch (error) {
        console.error('Error en refresh automático:', error);
        this.logout();
      }
    }, delay);
  }

  /**
   * Login del usuario
   */
  async login(email: string, password: string): Promise<User> {
    try {
      const response = await api.post<LoginResponse>('/auth/login', {
        email,
        password
      });

      const { user, tokens } = response.data;
      this.storeTokens(tokens);
      this.storeUser(user);
      this.setupTokenRefresh();

      return user;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Error en el login');
    }
  }

  /**
   * Logout del usuario
   */
  async logout(): Promise<void> {
    try {
      const refreshToken = this.getRefreshToken();
      if (refreshToken) {
        await api.post('/auth/logout', { refresh_token: refreshToken });
      }
    } catch (error) {
      console.error('Error en logout:', error);
    } finally {
      this.clearTokens();
      this.clearRefreshTimer();
    }
  }

  /**
   * Refresh del access token
   */
  async refreshAccessToken(): Promise<string> {
    // Evitar múltiples requests de refresh simultáneos
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh(refreshToken);

    try {
      const newToken = await this.refreshPromise;
      return newToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Realizar el refresh del token
   */
  private async performTokenRefresh(refreshToken: string): Promise<string> {
    try {
      const response = await api.post<RefreshResponse>('/auth/refresh', {
        refresh_token: refreshToken
      });

      const { access_token, expires_in } = response.data;
      
      // Actualizar tokens
      localStorage.setItem(ACCESS_TOKEN_KEY, access_token);
      const expiryTime = Date.now() + (expires_in * 1000);
      localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());

      // Programar próximo refresh
      const refreshTime = (expires_in * 1000) - REFRESH_BUFFER_TIME;
      this.scheduleTokenRefresh(refreshTime);

      return access_token;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Error refreshing token');
    }
  }

  /**
   * Obtener usuario actual
   */
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }

  /**
   * Verificar si el usuario está autenticado
   */
  isAuthenticated(): boolean {
    const token = this.getAccessToken();
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  /**
   * Verificar si el token está próximo a expirar
   */
  isTokenExpiringSoon(): boolean {
    const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (!expiryTime) return true;

    const timeUntilExpiry = parseInt(expiryTime) - Date.now();
    return timeUntilExpiry < REFRESH_BUFFER_TIME;
  }

  /**
   * Obtener access token
   */
  private getAccessToken(): string | null {
    return localStorage.getItem(ACCESS_TOKEN_KEY);
  }

  /**
   * Obtener refresh token
   */
  private getRefreshToken(): string | null {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }

  /**
   * Almacenar tokens
   */
  private storeTokens(tokens: AuthTokens) {
    localStorage.setItem(ACCESS_TOKEN_KEY, tokens.access_token);
    localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);
    
    const expiryTime = Date.now() + (tokens.expires_in * 1000);
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
  }

  /**
   * Almacenar usuario
   */
  private storeUser(user: User) {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
  }

  /**
   * Limpiar tokens
   */
  private clearTokens() {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  }

  /**
   * Limpiar timer de refresh
   */
  private clearRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Cambiar contraseña
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await api.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Error cambiando contraseña');
    }
  }

  /**
   * Solicitar reset de contraseña
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await api.post('/auth/request-password-reset', { email });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Error solicitando reset');
    }
  }

  /**
   * Confirmar reset de contraseña
   */
  async confirmPasswordReset(token: string, newPassword: string): Promise<void> {
    try {
      await api.post('/auth/confirm-password-reset', {
        token,
        new_password: newPassword
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Error confirmando reset');
    }
  }
}

// Instancia singleton
export const authServiceAdvanced = new AuthServiceAdvanced();
export default authServiceAdvanced;
