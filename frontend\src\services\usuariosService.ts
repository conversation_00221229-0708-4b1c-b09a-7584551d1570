import { 
  Usuario, 
  UsuarioFormData, 
  UsuarioEditData, 
  FiltrosUsuarios, 
  EstadisticasUsuarios,
  Rol,
  TipoIdentificacion,
  Permiso
} from '../types/usuarios';

// Simulación de datos para desarrollo
// En producción, estos datos vendrán del backend basado en el script SQL

const mockTiposIdentificacion: TipoIdentificacion[] = [
  { id: 1, codigo: 'CC', descripcion: 'Cédula de Ciudadanía' },
  { id: 2, codigo: 'CE', descripcion: 'Cédula de Extranjería' },
  { id: 3, codigo: 'PA', descripcion: 'Pasaporte' },
  { id: 4, codigo: 'RC', descripcion: 'Registro Civil' },
  { id: 5, codigo: 'TI', descripcion: 'Tarjeta de Identidad' },
  { id: 6, codigo: 'AS', descripcion: 'Adulto sin Identificación' },
  { id: 7, codigo: 'MS', descripcion: 'Menor sin Identificación' },
  { id: 8, codigo: 'NU', descripcion: 'Número Único de Identificación' }
];

const mockRoles: Rol[] = [
  {
    id: 1,
    nombre: 'Super Administrador',
    descripcion: 'Acceso completo al sistema',
    permisos: { all: true },
    hospital_id: 1
  },
  {
    id: 2,
    nombre: 'Administrador',
    descripcion: 'Administrador del hospital',
    permisos: { admin: true },
    hospital_id: 1
  },
  {
    id: 3,
    nombre: 'Médico',
    descripcion: 'Profesional médico',
    permisos: { medical: true },
    hospital_id: 1
  },
  {
    id: 4,
    nombre: 'Enfermera',
    descripción: 'Profesional de enfermería',
    permisos: { nursing: true },
    hospital_id: 1
  },
  {
    id: 5,
    nombre: 'Recepcionista',
    descripcion: 'Personal de recepción',
    permisos: { reception: true },
    hospital_id: 1
  },
  {
    id: 6,
    nombre: 'Farmaceuta',
    descripcion: 'Profesional farmacéutico',
    permisos: { pharmacy: true },
    hospital_id: 1
  },
  {
    id: 7,
    nombre: 'Contador',
    descripcion: 'Profesional contable',
    permisos: { accounting: true },
    hospital_id: 1
  }
];

const mockUsuarios: Usuario[] = [
  {
    id: 1,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '********',
    username: 'admin',
    rol_id: 1,
    email: '<EMAIL>',
    especialidad: 'Clínica',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[0]
  },
  {
    id: 2,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '********',
    username: 'dr.garcia',
    rol_id: 3,
    email: '<EMAIL>',
    especialidad: 'Quirúrgica',
    created_at: '2024-01-16T09:00:00Z',
    updated_at: '2024-01-16T09:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[2]
  },
  {
    id: 3,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '********',
    username: 'enf.martinez',
    rol_id: 4,
    email: '<EMAIL>',
    especialidad: 'Clínica',
    created_at: '2024-01-17T10:00:00Z',
    updated_at: '2024-01-17T10:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[3]
  },
  {
    id: 4,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '55667788',
    username: 'recep.lopez',
    rol_id: 5,
    email: '<EMAIL>',
    created_at: '2024-01-18T11:00:00Z',
    updated_at: '2024-01-18T11:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[4]
  },
  {
    id: 5,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '99887766',
    username: 'farm.rodriguez',
    rol_id: 6,
    email: '<EMAIL>',
    especialidad: 'Clínica',
    created_at: '2024-01-19T12:00:00Z',
    updated_at: '2024-01-19T12:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[5]
  },
  {
    id: 6,
    hospital_id: 1,
    tipo_identificacion_id: 1,
    numero_identificacion: '44556677',
    username: 'cont.perez',
    rol_id: 7,
    email: '<EMAIL>',
    created_at: '2024-01-20T13:00:00Z',
    updated_at: '2024-01-20T13:00:00Z',
    tipo_identificacion: mockTiposIdentificacion[0],
    rol: mockRoles[6]
  }
];

class UsuariosService {
  // Obtener todos los usuarios con filtros
  async getAll(filtros?: FiltrosUsuarios): Promise<Usuario[]> {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simular latencia
    
    let usuarios = [...mockUsuarios];
    
    if (filtros?.busqueda) {
      const busqueda = filtros.busqueda.toLowerCase();
      usuarios = usuarios.filter(usuario => 
        usuario.username.toLowerCase().includes(busqueda) ||
        usuario.email.toLowerCase().includes(busqueda) ||
        usuario.numero_identificacion.includes(busqueda)
      );
    }
    
    if (filtros?.rol_id) {
      usuarios = usuarios.filter(usuario => usuario.rol_id === filtros.rol_id);
    }
    
    if (filtros?.especialidad) {
      usuarios = usuarios.filter(usuario => usuario.especialidad === filtros.especialidad);
    }
    
    return usuarios;
  }

  // Obtener usuario por ID
  async getById(id: number): Promise<Usuario | null> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockUsuarios.find(usuario => usuario.id === id) || null;
  }

  // Crear nuevo usuario
  async create(data: UsuarioFormData): Promise<Usuario> {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const nuevoUsuario: Usuario = {
      id: Math.max(...mockUsuarios.map(u => u.id)) + 1,
      hospital_id: 1, // En producción vendrá del contexto del usuario
      tipo_identificacion_id: data.tipo_identificacion_id,
      numero_identificacion: data.numero_identificacion,
      username: data.username,
      rol_id: data.rol_id,
      email: data.email,
      especialidad: data.especialidad,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      tipo_identificacion: mockTiposIdentificacion.find(t => t.id === data.tipo_identificacion_id),
      rol: mockRoles.find(r => r.id === data.rol_id)
    };
    
    mockUsuarios.push(nuevoUsuario);
    return nuevoUsuario;
  }

  // Actualizar usuario
  async update(id: number, data: UsuarioEditData): Promise<Usuario> {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const index = mockUsuarios.findIndex(usuario => usuario.id === id);
    if (index === -1) {
      throw new Error('Usuario no encontrado');
    }
    
    const usuarioActualizado: Usuario = {
      ...mockUsuarios[index],
      tipo_identificacion_id: data.tipo_identificacion_id,
      numero_identificacion: data.numero_identificacion,
      username: data.username,
      rol_id: data.rol_id,
      email: data.email,
      especialidad: data.especialidad,
      updated_at: new Date().toISOString(),
      tipo_identificacion: mockTiposIdentificacion.find(t => t.id === data.tipo_identificacion_id),
      rol: mockRoles.find(r => r.id === data.rol_id)
    };
    
    mockUsuarios[index] = usuarioActualizado;
    return usuarioActualizado;
  }

  // Eliminar usuario
  async delete(id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = mockUsuarios.findIndex(usuario => usuario.id === id);
    if (index === -1) {
      throw new Error('Usuario no encontrado');
    }
    
    mockUsuarios.splice(index, 1);
  }

  // Obtener tipos de identificación
  async getTiposIdentificacion(): Promise<TipoIdentificacion[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockTiposIdentificacion;
  }

  // Obtener roles
  async getRoles(): Promise<Rol[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockRoles;
  }

  // Obtener estadísticas
  async getEstadisticas(): Promise<EstadisticasUsuarios> {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const usuariosPorRol = mockRoles.map(rol => ({
      rol_nombre: rol.nombre,
      cantidad: mockUsuarios.filter(u => u.rol_id === rol.id).length
    }));
    
    const usuariosPorEspecialidad = [
      { especialidad: 'Clínica' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Clínica').length },
      { especialidad: 'Quirúrgica' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Quirúrgica').length },
      { especialidad: 'Diagnóstica' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Diagnóstica').length },
      { especialidad: 'Pediatría' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Pediatría').length },
      { especialidad: 'Salud Mental' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Salud Mental').length },
      { especialidad: 'Emergente' as const, cantidad: mockUsuarios.filter(u => u.especialidad === 'Emergente').length }
    ].filter(item => item.cantidad > 0);
    
    return {
      total_usuarios: mockUsuarios.length,
      usuarios_activos: mockUsuarios.length, // En producción se verificaría el estado
      usuarios_por_rol: usuariosPorRol,
      usuarios_por_especialidad: usuariosPorEspecialidad,
      ultimo_acceso: mockUsuarios.slice(0, 5).map(u => ({
        usuario_id: u.id,
        username: u.username,
        ultimo_acceso: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      }))
    };
  }

  // Cambiar contraseña
  async cambiarPassword(id: number, passwordActual: string, passwordNueva: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 600));
    // En producción se verificaría la contraseña actual y se actualizaría
  }

  // Resetear contraseña
  async resetearPassword(id: number): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 600));
    // En producción se generaría una contraseña temporal
    return 'temp123456';
  }
}

export const usuariosService = new UsuariosService();
