// Tipos para el módulo de Gestión de Usuarios
// Basado en la estructura de la base de datos del script SQL

export interface TipoIdentificacion {
  id: number;
  codigo: 'CC' | 'CE' | 'PA' | 'RC' | 'TI' | 'AS' | 'MS' | 'NU';
  descripcion: string;
}

export interface Rol {
  id: number;
  nombre: string;
  descripcion: string;
  permisos: Record<string, any>;
  hospital_id: number;
}

export interface Permiso {
  id: number;
  nombre: string;
  descripcion: string;
  hospital_id: number;
}

export type TipoEspecialidad = 'Clínica' | 'Quirúrgica' | 'Diagnóstica' | 'Pediatría' | 'Salud Mental' | 'Emergente';

export interface Usuario {
  id: number;
  hospital_id: number;
  tipo_identificacion_id: number;
  numero_identificacion: string; // Desencriptado en frontend
  username: string;
  password?: string; // Solo para creación/edición
  rol_id: number;
  email: string; // Desencriptado en frontend
  especialidad?: TipoEspecialidad;
  created_at: string;
  updated_at: string;
  
  // Relaciones populadas
  tipo_identificacion?: TipoIdentificacion;
  rol?: Rol;
}

export interface UsuarioFormData {
  tipo_identificacion_id: number;
  numero_identificacion: string;
  username: string;
  password: string;
  confirm_password: string;
  rol_id: number;
  email: string;
  especialidad?: TipoEspecialidad;
}

export interface UsuarioEditData {
  tipo_identificacion_id: number;
  numero_identificacion: string;
  username: string;
  password?: string; // Opcional en edición
  confirm_password?: string;
  rol_id: number;
  email: string;
  especialidad?: TipoEspecialidad;
}

export interface FiltrosUsuarios {
  busqueda?: string;
  rol_id?: number;
  especialidad?: TipoEspecialidad;
  activo?: boolean;
}

export interface EstadisticasUsuarios {
  total_usuarios: number;
  usuarios_activos: number;
  usuarios_por_rol: Array<{
    rol_nombre: string;
    cantidad: number;
  }>;
  usuarios_por_especialidad: Array<{
    especialidad: TipoEspecialidad;
    cantidad: number;
  }>;
  ultimo_acceso: Array<{
    usuario_id: number;
    username: string;
    ultimo_acceso: string;
  }>;
}

// Constantes para especialidades
export const ESPECIALIDADES: Array<{ value: TipoEspecialidad; label: string }> = [
  { value: 'Clínica', label: 'Clínica' },
  { value: 'Quirúrgica', label: 'Quirúrgica' },
  { value: 'Diagnóstica', label: 'Diagnóstica' },
  { value: 'Pediatría', label: 'Pediatría' },
  { value: 'Salud Mental', label: 'Salud Mental' },
  { value: 'Emergente', label: 'Emergente' }
];

// Constantes para tipos de identificación
export const TIPOS_IDENTIFICACION_LABELS: Record<string, string> = {
  'CC': 'Cédula de Ciudadanía',
  'CE': 'Cédula de Extranjería',
  'PA': 'Pasaporte',
  'RC': 'Registro Civil',
  'TI': 'Tarjeta de Identidad',
  'AS': 'Adulto sin Identificación',
  'MS': 'Menor sin Identificación',
  'NU': 'Número Único de Identificación'
};

// Permisos predefinidos del sistema
export const PERMISOS_SISTEMA = [
  'usuarios.crear',
  'usuarios.leer',
  'usuarios.actualizar',
  'usuarios.eliminar',
  'pacientes.crear',
  'pacientes.leer',
  'pacientes.actualizar',
  'pacientes.eliminar',
  'citas.crear',
  'citas.leer',
  'citas.actualizar',
  'citas.eliminar',
  'consultas.crear',
  'consultas.leer',
  'consultas.actualizar',
  'consultas.eliminar',
  'cirugias.crear',
  'cirugias.leer',
  'cirugias.actualizar',
  'cirugias.eliminar',
  'hospitalizaciones.crear',
  'hospitalizaciones.leer',
  'hospitalizaciones.actualizar',
  'hospitalizaciones.eliminar',
  'urgencias.crear',
  'urgencias.leer',
  'urgencias.actualizar',
  'urgencias.eliminar',
  'inventario.crear',
  'inventario.leer',
  'inventario.actualizar',
  'inventario.eliminar',
  'farmacia.crear',
  'farmacia.leer',
  'farmacia.actualizar',
  'farmacia.eliminar',
  'facturacion.crear',
  'facturacion.leer',
  'facturacion.actualizar',
  'facturacion.eliminar',
  'reportes.generar',
  'reportes.exportar',
  'administracion.configurar',
  'administracion.auditoria',
  'telemedicina.crear',
  'telemedicina.leer',
  'telemedicina.actualizar',
  'ambulancias.crear',
  'ambulancias.leer',
  'ambulancias.actualizar',
  'ambulancias.eliminar'
];

// Roles predefinidos del sistema
export const ROLES_PREDEFINIDOS = [
  {
    nombre: 'Super Administrador',
    descripcion: 'Acceso completo al sistema',
    permisos: PERMISOS_SISTEMA
  },
  {
    nombre: 'Administrador',
    descripcion: 'Administrador del hospital',
    permisos: PERMISOS_SISTEMA.filter(p => !p.includes('administracion.configurar'))
  },
  {
    nombre: 'Médico',
    descripcion: 'Profesional médico',
    permisos: [
      'pacientes.leer',
      'pacientes.actualizar',
      'citas.leer',
      'citas.actualizar',
      'consultas.crear',
      'consultas.leer',
      'consultas.actualizar',
      'cirugias.crear',
      'cirugias.leer',
      'cirugias.actualizar',
      'hospitalizaciones.crear',
      'hospitalizaciones.leer',
      'hospitalizaciones.actualizar',
      'urgencias.crear',
      'urgencias.leer',
      'urgencias.actualizar',
      'farmacia.leer',
      'telemedicina.crear',
      'telemedicina.leer',
      'telemedicina.actualizar'
    ]
  },
  {
    nombre: 'Enfermera',
    descripcion: 'Profesional de enfermería',
    permisos: [
      'pacientes.leer',
      'pacientes.actualizar',
      'citas.leer',
      'consultas.leer',
      'hospitalizaciones.leer',
      'hospitalizaciones.actualizar',
      'urgencias.leer',
      'urgencias.actualizar',
      'farmacia.leer',
      'inventario.leer'
    ]
  },
  {
    nombre: 'Recepcionista',
    descripcion: 'Personal de recepción',
    permisos: [
      'pacientes.crear',
      'pacientes.leer',
      'pacientes.actualizar',
      'citas.crear',
      'citas.leer',
      'citas.actualizar'
    ]
  },
  {
    nombre: 'Farmaceuta',
    descripcion: 'Profesional farmacéutico',
    permisos: [
      'pacientes.leer',
      'farmacia.crear',
      'farmacia.leer',
      'farmacia.actualizar',
      'inventario.leer',
      'inventario.actualizar'
    ]
  },
  {
    nombre: 'Contador',
    descripcion: 'Profesional contable',
    permisos: [
      'facturacion.crear',
      'facturacion.leer',
      'facturacion.actualizar',
      'reportes.generar',
      'reportes.exportar'
    ]
  }
];
