// Sistema de monitoreo de performance para Hipócrates
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'navigation' | 'resource' | 'measure' | 'custom';
  url?: string;
  details?: Record<string, any>;
}

interface PerformanceReport {
  timestamp: number;
  url: string;
  userAgent: string;
  metrics: PerformanceMetric[];
  vitals: {
    fcp?: number; // First Contentful Paint
    lcp?: number; // Largest Contentful Paint
    fid?: number; // First Input Delay
    cls?: number; // Cumulative Layout Shift
    ttfb?: number; // Time to First Byte
  };
  resources: {
    totalSize: number;
    loadTime: number;
    resourceCount: number;
    slowResources: Array<{
      name: string;
      duration: number;
      size?: number;
    }>;
  };
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isMonitoring = false;
  private reportCallback?: (report: PerformanceReport) => void;

  constructor() {
    this.setupPerformanceObservers();
  }

  // Iniciar monitoreo
  start(reportCallback?: (report: PerformanceReport) => void) {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.reportCallback = reportCallback;
    
    console.log('[Performance] Iniciando monitoreo de performance');
    
    // Monitorear métricas iniciales
    this.collectNavigationMetrics();
    this.collectResourceMetrics();
    
    // Reportar cada 30 segundos
    setInterval(() => {
      this.generateReport();
    }, 30000);
  }

  // Detener monitoreo
  stop() {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    console.log('[Performance] Monitoreo detenido');
  }

  // Configurar observadores de performance
  private setupPerformanceObservers() {
    if (!('PerformanceObserver' in window)) {
      console.warn('[Performance] PerformanceObserver no soportado');
      return;
    }

    // Observer para Navigation Timing
    try {
      const navObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.addMetric({
            name: 'navigation',
            value: entry.duration,
            timestamp: Date.now(),
            type: 'navigation',
            details: {
              domContentLoaded: (entry as PerformanceNavigationTiming).domContentLoadedEventEnd - entry.startTime,
              loadComplete: (entry as PerformanceNavigationTiming).loadEventEnd - entry.startTime,
              transferSize: (entry as PerformanceNavigationTiming).transferSize
            }
          });
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);
    } catch (error) {
      console.warn('[Performance] Error configurando navigation observer:', error);
    }

    // Observer para Resource Timing
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const resourceEntry = entry as PerformanceResourceTiming;
          this.addMetric({
            name: 'resource',
            value: entry.duration,
            timestamp: Date.now(),
            type: 'resource',
            url: entry.name,
            details: {
              transferSize: resourceEntry.transferSize,
              encodedBodySize: resourceEntry.encodedBodySize,
              decodedBodySize: resourceEntry.decodedBodySize,
              initiatorType: resourceEntry.initiatorType
            }
          });
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (error) {
      console.warn('[Performance] Error configurando resource observer:', error);
    }

    // Observer para Paint Timing
    try {
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.addMetric({
            name: entry.name,
            value: entry.startTime,
            timestamp: Date.now(),
            type: 'measure'
          });
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      console.warn('[Performance] Error configurando paint observer:', error);
    }

    // Observer para Layout Shift
    try {
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const layoutShift = entry as any;
          if (!layoutShift.hadRecentInput) {
            this.addMetric({
              name: 'cumulative-layout-shift',
              value: layoutShift.value,
              timestamp: Date.now(),
              type: 'measure'
            });
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    } catch (error) {
      console.warn('[Performance] Error configurando CLS observer:', error);
    }
  }

  // Agregar métrica
  private addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Mantener solo las últimas 1000 métricas
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  // Recopilar métricas de navegación
  private collectNavigationMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (!navigation) return;

    const metrics = {
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      ssl: navigation.secureConnectionStart > 0 ? navigation.connectEnd - navigation.secureConnectionStart : 0,
      ttfb: navigation.responseStart - navigation.requestStart,
      download: navigation.responseEnd - navigation.responseStart,
      domParse: navigation.domContentLoadedEventStart - navigation.responseEnd,
      domReady: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart
    };

    Object.entries(metrics).forEach(([name, value]) => {
      this.addMetric({
        name: `navigation-${name}`,
        value,
        timestamp: Date.now(),
        type: 'navigation'
      });
    });
  }

  // Recopilar métricas de recursos
  private collectResourceMetrics() {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    resources.forEach(resource => {
      this.addMetric({
        name: 'resource-load',
        value: resource.duration,
        timestamp: Date.now(),
        type: 'resource',
        url: resource.name,
        details: {
          transferSize: resource.transferSize,
          type: resource.initiatorType
        }
      });
    });
  }

  // Medir tiempo de ejecución de función
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;

    this.addMetric({
      name: `function-${name}`,
      value: duration,
      timestamp: Date.now(),
      type: 'custom'
    });

    return result;
  }

  // Medir tiempo de ejecución de función async
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;

    this.addMetric({
      name: `async-function-${name}`,
      value: duration,
      timestamp: Date.now(),
      type: 'custom'
    });

    return result;
  }

  // Marcar evento personalizado
  mark(name: string, details?: Record<string, any>) {
    performance.mark(name);
    
    this.addMetric({
      name: `mark-${name}`,
      value: performance.now(),
      timestamp: Date.now(),
      type: 'custom',
      details
    });
  }

  // Medir entre dos marcas
  measure(name: string, startMark: string, endMark: string) {
    try {
      performance.measure(name, startMark, endMark);
      const measure = performance.getEntriesByName(name, 'measure')[0];
      
      this.addMetric({
        name: `measure-${name}`,
        value: measure.duration,
        timestamp: Date.now(),
        type: 'measure'
      });
    } catch (error) {
      console.warn(`[Performance] Error midiendo ${name}:`, error);
    }
  }

  // Generar reporte de performance
  generateReport(): PerformanceReport {
    const now = Date.now();
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    // Calcular métricas de recursos
    const totalSize = resources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
    const totalLoadTime = resources.reduce((sum, r) => sum + r.duration, 0);
    const slowResources = resources
      .filter(r => r.duration > 1000) // > 1 segundo
      .map(r => ({
        name: r.name,
        duration: r.duration,
        size: r.transferSize
      }))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    // Obtener Core Web Vitals
    const vitals = this.getWebVitals();

    const report: PerformanceReport = {
      timestamp: now,
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: [...this.metrics],
      vitals,
      resources: {
        totalSize,
        loadTime: totalLoadTime,
        resourceCount: resources.length,
        slowResources
      }
    };

    // Enviar reporte si hay callback
    if (this.reportCallback) {
      this.reportCallback(report);
    }

    // Log en desarrollo
    if (process.env.NODE_ENV === 'development') {
      console.log('[Performance] Reporte generado:', report);
    }

    return report;
  }

  // Obtener Core Web Vitals
  private getWebVitals() {
    const vitals: PerformanceReport['vitals'] = {};

    // First Contentful Paint
    const fcp = performance.getEntriesByName('first-contentful-paint')[0];
    if (fcp) vitals.fcp = fcp.startTime;

    // Largest Contentful Paint
    const lcpMetric = this.metrics.find(m => m.name === 'largest-contentful-paint');
    if (lcpMetric) vitals.lcp = lcpMetric.value;

    // Cumulative Layout Shift
    const clsMetrics = this.metrics.filter(m => m.name === 'cumulative-layout-shift');
    if (clsMetrics.length > 0) {
      vitals.cls = clsMetrics.reduce((sum, m) => sum + m.value, 0);
    }

    // Time to First Byte
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      vitals.ttfb = navigation.responseStart - navigation.requestStart;
    }

    return vitals;
  }

  // Obtener métricas actuales
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Limpiar métricas
  clearMetrics() {
    this.metrics = [];
    performance.clearMarks();
    performance.clearMeasures();
  }
}

// Instancia singleton
export const performanceMonitor = new PerformanceMonitor();

// Hook para usar el monitor de performance
export const usePerformanceMonitor = () => {
  return {
    start: performanceMonitor.start.bind(performanceMonitor),
    stop: performanceMonitor.stop.bind(performanceMonitor),
    mark: performanceMonitor.mark.bind(performanceMonitor),
    measure: performanceMonitor.measure.bind(performanceMonitor),
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsyncFunction: performanceMonitor.measureAsyncFunction.bind(performanceMonitor),
    generateReport: performanceMonitor.generateReport.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    clearMetrics: performanceMonitor.clearMetrics.bind(performanceMonitor)
  };
};

export default performanceMonitor;
