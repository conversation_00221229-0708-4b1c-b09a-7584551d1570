import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import fs from 'fs'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Cargar variables de entorno basadas en el modo actual
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [
      react({
        // Configuración para React
        babel: {
          plugins: [
            // Añadir plugins de Babel si son necesarios
          ],
        },
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        'src': path.resolve(__dirname, './src'),
        'components': path.resolve(__dirname, './src/components'),
        'pages': path.resolve(__dirname, './src/pages'),
        'modules': path.resolve(__dirname, './src/modules'),
        'services': path.resolve(__dirname, './src/services'),
        'utils': path.resolve(__dirname, './src/utils'),
        'types': path.resolve(__dirname, './src/types'),
        'routes': path.resolve(__dirname, './src/routes'),
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    server: {
      port: 3000,
      strictPort: false, // Permitir cambiar de puerto si está ocupado
      https: (() => {
        // Configurar HTTPS para desarrollo si los certificados existen
        const keyPath = path.resolve(__dirname, 'ssl/localhost-key.pem');
        const certPath = path.resolve(__dirname, 'ssl/localhost.pem');

        if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
          return {
            key: fs.readFileSync(keyPath),
            cert: fs.readFileSync(certPath),
          };
        }

        // Si no hay certificados, usar HTTP
        return false;
      })(),
      hmr: {
        overlay: true,
      },
      watch: {
        usePolling: true,
      },
      headers: {
        // Content Security Policy headers
        'Content-Security-Policy': [
          "default-src 'self'",
          "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Necesario para desarrollo
          "style-src 'self' 'unsafe-inline'",
          "img-src 'self' data: https:",
          "font-src 'self' data:",
          "connect-src 'self' ws: wss: https://icd.who.int https://id.who.int",
          "frame-src 'none'",
          "object-src 'none'",
          "base-uri 'self'",
          "form-action 'self'"
        ].join('; '),
        // Otros headers de seguridad
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'ui-vendor': ['@fortawesome/fontawesome-svg-core', '@fortawesome/free-solid-svg-icons', '@fortawesome/react-fontawesome'],
            'data-vendor': ['@tanstack/react-query', 'axios', 'zustand'],
          }
        }
      }
    },
    optimizeDeps: {
      include: [
        'react', 
        'react-dom', 
        'react-router-dom',
        '@tanstack/react-query',
        'axios',
        'zustand',
        'react-hook-form'
      ],
      exclude: []
    },
    css: {
      devSourcemap: true,
    },
    base: env.VITE_BASE_URL || '/',
  }
})
